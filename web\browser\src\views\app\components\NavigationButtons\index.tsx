import { observer } from 'mobx-react-lite';
import * as React from 'react';
import { viewUtils } from '@browser/core/utils/platform-lite';
import store from '../../store';
import { ToolbarButton } from '../ToolbarButton';
import {
  ICON_CLOSE,
  ICON_FORWARD,
  ICON_BACK,
  ICON_REFRESH,
  ICON_HOME,
} from '@mario-ai/shared';
import { cn } from '@browser/utils/tailwind-helpers';
import { getWebUIURL } from '@browser/core/utils/webui';

const onBackClick = () => {
  if (store.tabs.selectedTab) {
    viewUtils.goBack(String(store.tabs.selectedTab.id));
  }
};

const onForwardClick = () => {
  if (store.tabs.selectedTab) {
    viewUtils.goForward(String(store.tabs.selectedTab.id));
  }
};

const onRefreshClick = () => {
  if (store.tabs.selectedTab) {
    if (store.tabs.selectedTab.loading) {
      viewUtils.stop(String(store.tabs.selectedTab.id));
    } else {
      viewUtils.reload(String(store.tabs.selectedTab.id));
    }
  }
};

const onHomeClick = () => {
  if (store.tabs.selectedTab) {
    // 使用正确的WebUI URL生成函数，支持开发和生产环境
    const homeUrl = getWebUIURL('newtab');
    viewUtils.loadURL(String(store.tabs.selectedTab.id), homeUrl);
  }
};

export const NavigationButtons = observer(() => {
  const { selectedTab } = store.tabs;

  let loading = false;

  if (selectedTab) {
    loading = selectedTab.loading;
  }

  // StyledContainer 样式 - Tailwind 版本
  const containerClasses = cn(
    'flex items-center',
    '[&]:[-webkit-app-region:no-drag]' // -webkit-app-region: no-drag
  );

  return (
    <div className={containerClasses}>
      <ToolbarButton
        disabled={!store.navigationState.canGoBack}
        size={20}
        icon={ICON_BACK}
        style={{ marginLeft: 6 }}
        onClick={onBackClick}
        tooltip="后退"
      />
      <ToolbarButton
        disabled={!store.navigationState.canGoForward}
        size={20}
        icon={ICON_FORWARD}
        onClick={onForwardClick}
        tooltip="前进"
      />
      <ToolbarButton
        size={20}
        icon={loading ? ICON_CLOSE : ICON_REFRESH}
        onClick={onRefreshClick}
        tooltip={loading ? "停止" : "刷新"}
      />
      <ToolbarButton
        size={20}
        icon={ICON_HOME}
        onClick={onHomeClick}
        tooltip="首页"
      />
    </div>
  );
});
