/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.html"
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // AI工具栏主题色
        'mario-ai-toolbar': 'var(--mario-ai-toolbar-bg)',
        'mario-border': 'var(--mario-border-color)',
        'mario-hover': 'var(--mario-hover-bg)',
        'mario-active': 'var(--mario-active-bg)',
        'mario-text-secondary': 'var(--mario-text-secondary)',
        'mario-tooltip': 'var(--mario-tooltip-bg)',
        'mario-tooltip-text': 'var(--mario-tooltip-text)',

        // 前卫主题色
        'futuristic-titlebar': 'var(--titlebar-bg)',
        'futuristic-toolbar': 'var(--toolbar-bg)',
        'futuristic-bookmark': 'var(--bookmark-bar-bg)',
        'futuristic-home': 'var(--home-bg)',
        'futuristic-text': 'var(--titlebar-text)',
        'futuristic-toolbar-text': 'var(--toolbar-text)',
        'futuristic-button': 'var(--button-bg)',
        'futuristic-button-hover': 'var(--button-hover-bg)',
        'futuristic-input': 'var(--input-bg)',
        'futuristic-input-border': 'var(--input-border)',
        // 映射现有主题变量
        'mario-titlebar': 'var(--mario-titlebar-bg)',
        'mario-addressbar': 'var(--mario-addressbar-bg)',
        'mario-addressbar-text': 'var(--mario-addressbar-text)',
        'mario-toolbar': 'var(--mario-toolbar-bg)',
        'mario-toolbar-line': 'var(--mario-toolbar-bottom-line)',
        'mario-toolbar-separator': 'var(--mario-toolbar-separator)',
        'mario-tab-text': 'var(--mario-tab-text)',
        'mario-tab-selected': 'var(--mario-tab-selected-text)',
        'mario-control': 'var(--mario-control-bg)',
        'mario-control-hover': 'var(--mario-control-hover-bg)',
        'mario-control-value': 'var(--mario-control-value)',
        'mario-switch': 'var(--mario-switch-bg)',
        'mario-dialog': 'var(--mario-dialog-bg)',
        'mario-dialog-text': 'var(--mario-dialog-text)',
        'mario-dialog-separator': 'var(--mario-dialog-separator)',
        'mario-searchbox': 'var(--mario-searchbox-bg)',
        'mario-page': 'var(--mario-page-bg)',
        'mario-page-text': 'var(--mario-page-text)',
        'mario-nav-drawer1': 'var(--mario-nav-drawer1-bg)',
        'mario-nav-drawer2': 'var(--mario-nav-drawer2-bg)',
        'mario-dropdown': 'var(--mario-dropdown-bg)',
        'mario-dropdown-translucent': 'var(--mario-dropdown-bg-translucent)',
        'mario-dropdown-separator': 'var(--mario-dropdown-separator)',
        'mario-accent': 'var(--mario-accent)',
      },
      fontFamily: {
        'roboto': ['Roboto', 'system-ui', 'sans-serif'],
        'system': ['system-ui', 'sans-serif'],
      },
      fontSize: {
        // Typography 系统
        'h1': ['96px', { letterSpacing: '-0.015625em', fontWeight: '300' }],
        'h2': ['60px', { letterSpacing: '-0.0083333em', fontWeight: '300' }],
        'h3': ['48px', { letterSpacing: '0em', fontWeight: '400' }],
        'h4': ['34px', { letterSpacing: '0.0073529em', fontWeight: '400' }],
        'h5': ['24px', { letterSpacing: '0em', fontWeight: '400' }],
        'h6': ['20px', { letterSpacing: '0.0075em', fontWeight: '500' }],
        'subtitle1': ['16px', { letterSpacing: '0.009375em', fontWeight: '400' }],
        'subtitle2': ['14px', { letterSpacing: '0.0071429em', fontWeight: '500' }],
        'body1': ['16px', { letterSpacing: '0.03125em', fontWeight: '400' }],
        'body2': ['14px', { letterSpacing: '0.0178571em', fontWeight: '400' }],
        'button': ['14px', { letterSpacing: '0.0535714em', fontWeight: '500' }],
        'caption': ['12px', { letterSpacing: '0.0333333em', fontWeight: '400' }],
        'overline': ['10px', { letterSpacing: '0.15em', fontWeight: '400', textTransform: 'uppercase' }],
      },
      boxShadow: {
        'dialog': '0 12px 16px rgba(0, 0, 0, 0.12), 0 8px 10px rgba(0, 0, 0, 0.16)',
        'dialog-light': '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
      },
      borderRadius: {
        'dialog': '10px',
      },
      animation: {
        'fadeIn': 'fadeIn 0.15s ease-out',
        'preloader-rotate': 'preloader-rotate 2s linear infinite',
        'preloader-dash': 'preloader-dash 1.5s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'preloader-rotate': {
          '100%': {
            '-webkit-transform': 'rotate(360deg)',
            'transform': 'rotate(360deg)'
          },
        },
        'preloader-dash': {
          '0%': {
            'stroke-dasharray': '1, 200',
            'stroke-dashoffset': '0',
          },
          '50%': {
            'stroke-dasharray': '89, 200',
            'stroke-dashoffset': '-35px',
          },
          '100%': {
            'stroke-dasharray': '89, 200',
            'stroke-dashoffset': '-124px',
          },
        }
      }
    }
  },
  plugins: []
}
