import { eventUtils } from '@browser/core/utils/platform-lite';
import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { IFormFillMenuItem } from '@mario-ai/shared';
import { cn } from '@browser/utils/tailwind-helpers';
import { transparency } from '@mario-ai/shared';

const onClick = (data: IFormFillMenuItem) => () => {
  eventUtils.send(`form-fill-update-${store.windowId}`, data._id, true);
  eventUtils.send(`form-fill-hide-${store.windowId}`);
};

const onMouseEnter = (data: IFormFillMenuItem) => () => {
  eventUtils.send(`form-fill-update-${store.windowId}`, data._id);
};

const onMouseLeave = () => {
  eventUtils.send(`form-fill-update-${store.windowId}`);
};

const Item = observer(({ data }: { data: IFormFillMenuItem }) => {
  const hasSubtext = !!data.subtext;

  // StyledItem 样式 - Tailwind 版本
  const itemClasses = cn(
    'w-full flex flex-col justify-center cursor-pointer',
    'hover:bg-black/4',
    hasSubtext ? 'h-14' : 'h-8'
  );

  // Text 样式 - Tailwind 版本
  const textClasses = cn(
    'px-3 text-sm pointer-events-none whitespace-nowrap text-ellipsis overflow-hidden',
    'text-black font-roboto'
  );

  // SubText 样式 - Tailwind 版本
  const subTextClasses = cn(
    textClasses,
    'mt-1 text-xs',
    `text-black opacity-[${transparency.text.medium}]`
  );

  return (
    <div
      className={itemClasses}
      onClick={onClick(data)}
      onMouseEnter={onMouseEnter(data)}
      onMouseLeave={onMouseLeave}
    >
      <div className={textClasses}>{data.text}</div>
      <div className={subTextClasses}>{data.subtext}</div>
    </div>
  );
});

export default observer(() => {
  // StyledList 样式 - Tailwind 版本
  const listClasses = cn(
    'w-full h-full py-2 overflow-hidden',
    // noButtons() 混入的等效样式
    'select-none'
  );

  return (
    <div className={listClasses}>
      {store.items.map((item) => (
        <Item key={item._id} data={item} />
      ))}
    </div>
  );
});
