import { makeObservable, observable, toJS } from 'mobx';

import { IStartupTab } from '@mario-ai/shared';
import { eventUtils } from '@browser/core/utils/platform-lite';

export class StartupTabsStore {
  // ✅ 重构: 移除复杂的数据库抽象层，改为直接IPC调用 (借鉴原始工程)

  public isLoaded = false;

  @observable
  public list: IStartupTab[] = [];

  constructor() {
    makeObservable(this);
  }

  public async load() {
    if (this.isLoaded) return;

    this.isLoaded = true;
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      this.list = await eventUtils.invoke('storage-get', {
        scope: 'startupTabs',
        query: {}
      });
      console.log('[StartupTabsStore] Loaded startup tabs via IPC:', this.list.length);
    } catch (error) {
      console.error('[StartupTabsStore] Error loading startup tabs:', error);
      this.list = [];
    }
  }

  public async addStartupDefaultTabItems(items: IStartupTab[]) {
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      await eventUtils.invoke('storage-remove', {
        scope: 'startupTabs',
        query: { isUserDefined: true },
        multi: true
      });

      this.list = this.list.filter((x) => !x.isUserDefined);

      for (const item of items.filter((x) => x.url !== undefined && x.url.length > 1)) {
        // 序列化MobX对象，确保数据库操作安全 (保持原始工程的toJS策略)
        const serializedItem = toJS(item);
        const insertedItem = await eventUtils.invoke('storage-insert', {
          scope: 'startupTabs',
          item: serializedItem
        });
        this.list.push(insertedItem);
      }
      console.log('[StartupTabsStore] Added startup tabs via IPC:', items.length);
    } catch (error) {
      console.error('[StartupTabsStore] Error adding startup tabs:', error);
    }
  }

  public async clearUserDefined() {
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      await eventUtils.invoke('storage-remove', {
        scope: 'startupTabs',
        query: { isUserDefined: true },
        multi: true
      });
      this.list = this.list.filter((x) => !x.isUserDefined);
      console.log('[StartupTabsStore] Cleared user defined tabs via IPC');
    } catch (error) {
      console.error('[StartupTabsStore] Error clearing user defined tabs:', error);
    }
  }

  public async clearStartupTabs(removePinned: boolean, removeUserDefined: boolean) {
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      if (removePinned && removeUserDefined) {
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: {},
          multi: true
        });
        this.list = [];
      } else if (!removePinned) {
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: { pinned: false },
          multi: true
        });
        this.list = this.list.filter((x) => x.pinned);
      } else if (!removeUserDefined) {
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: { isUserDefined: false },
          multi: true
        });
        this.list = this.list.filter((x) => x.isUserDefined);
      } else {
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: { isUserDefined: false, pinned: false },
          multi: true
        });
        this.list = this.list.filter((x) => x.isUserDefined || x.pinned);
      }
      console.log('[StartupTabsStore] Cleared startup tabs via IPC');
    } catch (error) {
      console.error('[StartupTabsStore] Error clearing startup tabs:', error);
    }
  }
}
