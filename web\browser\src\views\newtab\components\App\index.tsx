import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { cn } from '@browser/utils/tailwind-helpers';
import { TopSites } from '../TopSites';
import { Preferences } from '../Preferences';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';
import {
  ICON_TUNE,
  ICON_SETTINGS,
  ICON_HISTORY,
  ICON_BOOKMARKS,
  ICON_FIRE,
  ICON_DOWNLOAD,
  ICON_EXTENSIONS,
} from '@mario-ai/shared';
import { WebUIStyle } from '@browser/core/styles/default-styles';
import { getWebUIURL } from '@browser/core/utils/webui';

window.addEventListener('mousedown', (e) => {
  console.log('[NewTab] Global mousedown triggered, target:', e.target);
  console.log('[NewTab] Current dashboardSettingsVisible:', store.dashboardSettingsVisible);
  store.dashboardSettingsVisible = false;
  console.log('[NewTab] Set dashboardSettingsVisible to false');
});

const onIconClick = (name: string) => () => {
  window.location.href = getWebUIURL(name);
};

const onTuneClick = () => {
  store.dashboardSettingsVisible = !store.dashboardSettingsVisible;
};

const onRefreshClick = () => {
  store.image = '';
  setTimeout(() => {
    localStorage.setItem('imageDate', '');
    store.loadImage();
  }, 50);
};

// IconItem 组件 - Tailwind 版本
interface IconItemProps {
  icon: string;
  title: string;
  imageSet?: boolean;
  onClick?: () => void;
  onMouseDown?: (e: React.MouseEvent) => void;
}

const IconItem: React.FC<IconItemProps> = ({
  icon,
  title,
  imageSet,
  onClick,
  onMouseDown
}) => {
  const iconClasses = cn(
    'w-[45px] h-[45px] opacity-80 z-[3] cursor-pointer',
    'rounded-full relative',
    // 底部菜单项的间距
    'mx-2',
    // hover 效果
    'hover:opacity-100 hover:bg-white hover:bg-opacity-10',
    imageSet ? 'hover:backdrop-blur-[2.5px]' : ''
  );

  return (
    <div
      className={iconClasses}
      onClick={onClick}
      onMouseDown={onMouseDown}
      title={title}
    >
      {/* 图标通过内联样式实现 */}
      <div
        className="absolute inset-0 bg-center bg-no-repeat bg-contain"
        style={{
          backgroundImage: `url(${icon})`,
          backgroundSize: '24px',
          // 直接在内联样式中应用 filter，确保生效
          filter: imageSet ? 'invert(100%)' : 'none',
        }}
      />
    </div>
  );
};

export default observer(() => {
  // 初始化主题
  React.useEffect(() => {
    console.log('[NewTab] Initializing theme:', store.settings.theme, 'themeAuto:', store.settings.themeAuto);
    TailwindThemeManager.setThemeWithAuto(store.settings.theme, store.settings.themeAuto);
  }, []);

  // 监听主题变化
  React.useEffect(() => {
    const currentTheme = store.settings.theme;
    const isAuto = store.settings.themeAuto;
    console.log('[NewTab] Theme changed to:', currentTheme, 'themeAuto:', isAuto);
    TailwindThemeManager.setThemeWithAuto(currentTheme, isAuto);
  }, [store.settings.theme, store.settings.themeAuto]);

  // 初始化时设置主题
  React.useEffect(() => {
    console.log('[NewTab] Initializing theme');
    TailwindThemeManager.setThemeWithAuto(store.settings.theme, store.settings.themeAuto);
  }, []);

  // Wrapper 样式 - Tailwind 版本，支持前卫主题背景
  const wrapperClasses = cn(
    'flex items-center relative overflow-hidden',
    store.fullSizeImage ? 'h-screen' : 'h-auto'
  );

  // 前卫主题背景样式
  const futuristicBgStyle: React.CSSProperties = {
    background: 'var(--home-bg, transparent)',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  };

  // Image 样式 - Tailwind 版本
  const imageClasses = cn(
    'absolute z-[1] bg-cover bg-center bg-no-repeat',
    'left-0 top-0 right-0 bottom-0',
    'transition-all duration-300 ease-out',
    // 动态样式通过内联样式处理
  );

  const imageStyle: React.CSSProperties = {
    opacity: store.imageVisible && store.image ? 1 : 0,
    transform: store.imageVisible && store.image ? 'scale(1)' : 'scale(1.05)',
    backgroundImage: store.imageVisible && store.image ? `url(${store.image})` : 'none',
    backgroundAttachment: 'fixed',
  };

  // Content 样式 - Tailwind 版本
  const contentClasses = cn('flex-1 z-[3] relative');

  // RightBar 样式 - Tailwind 版本
  const rightBarClasses = cn(
    'absolute right-8 z-[3] flex flex-col h-full pt-8'
  );

  // Menu 样式 - Tailwind 版本 (底部菜单)
  const menuClasses = cn(
    'flex absolute flex-row justify-center w-full bottom-8 z-[3]'
  );

  return (
    <div>
      <WebUIStyle />

      <Preferences />

      <div className={wrapperClasses}>
        {/* 前卫主题背景 */}
        <div style={futuristicBgStyle} />

        <div
          className={imageClasses}
          style={imageStyle}
        >
          {/* 渐变遮罩层 - 使用内联样式实现复杂渐变 */}
          <div
            className="absolute inset-0 z-[2]"
            style={{
              backgroundImage: `
                radial-gradient(rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%),
                radial-gradient(rgba(0, 0, 0, 0) 33%, rgba(0, 0, 0, 0.3) 166%)
              `
            }}
          />
        </div>

        <div className={contentClasses}>
          {store.topSitesVisible && <TopSites />}
        </div>

        <div className={rightBarClasses}>
          <IconItem
            imageSet={store.imageVisible && store.image}
            title="主页设置"
            icon={ICON_TUNE}
            onMouseDown={(e) => e.stopPropagation()}
            onClick={onTuneClick}
          />
        </div>

        {store.quickMenuVisible && (
          <div className={menuClasses}>
            <IconItem
              imageSet={store.imageVisible && store.image}
              title="历史记录"
              icon={ICON_HISTORY}
              onClick={onIconClick('history')}
            />
            <IconItem
              imageSet={store.imageVisible && store.image}
              title="书签"
              icon={ICON_BOOKMARKS}
              onClick={onIconClick('bookmarks')}
            />
            <IconItem
              imageSet={store.imageVisible && store.image}
              title="设置"
              icon={ICON_SETTINGS}
              onClick={onIconClick('settings')}
            />
          </div>
        )}
      </div>
    </div>
  );
});
