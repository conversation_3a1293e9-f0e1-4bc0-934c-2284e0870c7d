// 阿里云API服务占位符
export const AVAILABLE_MODELS = [
  'qwen-plus',
  'qwen-turbo',
  'qwen-max'
];

export const getResponse = async (params: any) => {
  console.log('🤖 Getting response from <PERSON><PERSON> (placeholder)', params);
  return {
    content: '这是一个测试回复。阿里云API集成正在开发中...',
    usage: {
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0
    }
  };
};

export const getStreamingResponse = async (params: any, onChunk: (chunk: string) => void) => {
  console.log('🌊 Getting streaming response from <PERSON><PERSON> (placeholder)', params);
  
  // 模拟流式响应
  const message = '这是一个模拟的流式回复。阿里云API集成正在开发中...';
  const words = message.split('');
  
  for (let i = 0; i < words.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 50));
    onChunk(words[i]);
  }
  
  return {
    usage: {
      promptTokens: 0,
      completionTokens: words.length,
      totalTokens: words.length
    }
  };
};
