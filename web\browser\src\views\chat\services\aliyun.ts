// 阿里云API服务
export const AVAILABLE_MODELS = [
  'qwen-plus',
  'qwen-turbo',
  'qwen-max'
];

interface ChatParams {
  message: string;
  model?: string;
  systemPrompt?: string;
  apiKey?: string;
  endpoint?: string;
  conversationHistory?: Array<{role: string; content: string}>;
}

interface ChatResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export const getResponse = async (params: ChatParams): Promise<ChatResponse> => {
  console.log('🤖 Getting response from Aliyun', params);

  const { message, model = 'qwen-plus', systemPrompt, apiKey, endpoint, conversationHistory = [] } = params;

  if (!apiKey) {
    throw new Error('API Key is required');
  }

  try {
    // 构建消息数组
    const messages = [];

    // 添加系统提示
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    }

    // 添加对话历史
    conversationHistory.forEach(msg => {
      messages.push(msg);
    });

    // 添加当前消息
    messages.push({
      role: 'user',
      content: message
    });

    // 调用阿里云API
    const response = await fetch(endpoint || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'X-DashScope-SSE': 'disable'
      },
      body: JSON.stringify({
        model: model,
        input: {
          messages: messages
        },
        parameters: {
          temperature: 0.7,
          top_p: 0.8,
          max_tokens: 2000,
          result_format: 'message'
        }
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.code) {
      throw new Error(`API error: ${data.code} - ${data.message}`);
    }

    return {
      content: data.output?.choices?.[0]?.message?.content || '抱歉，我无法生成回复。',
      usage: {
        promptTokens: data.usage?.input_tokens || 0,
        completionTokens: data.usage?.output_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0
      }
    };

  } catch (error) {
    console.error('Error calling Aliyun API:', error);

    // 返回错误信息而不是抛出异常
    return {
      content: `抱歉，调用AI服务时出现错误：${error instanceof Error ? error.message : String(error)}。请检查API配置或稍后重试。`,
      usage: {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      }
    };
  }
};

export const getStreamingResponse = async (
  params: ChatParams,
  onChunk: (chunk: string) => void
): Promise<ChatResponse> => {
  console.log('🌊 Getting streaming response from Aliyun', params);

  const { message, model = 'qwen-plus', systemPrompt, apiKey, endpoint, conversationHistory = [] } = params;

  if (!apiKey) {
    throw new Error('API Key is required');
  }

  try {
    // 构建消息数组
    const messages = [];

    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    }

    conversationHistory.forEach(msg => {
      messages.push(msg);
    });

    messages.push({
      role: 'user',
      content: message
    });

    // 调用流式API
    const response = await fetch(endpoint || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'X-DashScope-SSE': 'enable',
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify({
        model: model,
        input: {
          messages: messages
        },
        parameters: {
          temperature: 0.7,
          top_p: 0.8,
          max_tokens: 2000,
          result_format: 'message',
          incremental_output: true
        }
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Failed to get response reader');
    }

    let fullContent = '';
    let usage = { promptTokens: 0, completionTokens: 0, totalTokens: 0 };

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.output?.choices?.[0]?.message?.content) {
                const content = data.output.choices[0].message.content;
                fullContent += content;
                onChunk(content);
              }

              if (data.usage) {
                usage = {
                  promptTokens: data.usage.input_tokens || 0,
                  completionTokens: data.usage.output_tokens || 0,
                  totalTokens: data.usage.total_tokens || 0
                };
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return {
      content: fullContent,
      usage
    };

  } catch (error) {
    console.error('Error calling Aliyun streaming API:', error);

    // 对于流式响应，我们通过onChunk发送错误信息
    const errorMessage = `抱歉，调用AI服务时出现错误：${error instanceof Error ? error.message : String(error)}。请检查API配置或稍后重试。`;
    onChunk(errorMessage);

    return {
      content: errorMessage,
      usage: {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      }
    };
  }
};
