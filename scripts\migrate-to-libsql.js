#!/usr/bin/env node

/**
 * LibSQL迁移工具
 * 用于管理从NeDB到LibSQL的数据迁移过程
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;

class MigrationCLI {
  constructor() {
    this.electronPath = path.join(__dirname, '../electron');
  }

  async run() {
    const command = process.argv[2];
    
    switch (command) {
      case 'migrate':
        await this.runMigration();
        break;
      case 'validate':
        await this.validateMigration();
        break;
      case 'backup':
        await this.createBackup();
        break;
      case 'help':
      default:
        this.showHelp();
        break;
    }
  }



  async runMigration() {
    console.log('🚀 开始数据迁移...');
    
    try {
      // 确认用户意图
      const confirmed = await this.confirmMigration();
      if (!confirmed) {
        console.log('❌ 迁移已取消');
        return;
      }
      
      // 构建项目
      await this.buildProject();
      
      // 执行迁移
      const migrationScript = `
        const { runDataMigration } = require('./build/main/services/database/migration-service.js');
        runDataMigration().then(() => {
          console.log('🎉 数据迁移完成！');
          process.exit(0);
        }).catch(error => {
          console.error('❌ 迁移失败:', error);
          process.exit(1);
        });
      `;
      
      await this.runNodeScript(migrationScript);
    } catch (error) {
      console.error('❌ 迁移执行失败:', error);
      process.exit(1);
    }
  }

  async validateMigration() {
    console.log('🔍 验证迁移结果...');
    
    try {
      await this.buildProject();
      
      const validationScript = `
        const { runLibSQLIntegrationTest } = require('./build/main/services/database/integration-test.js');
        runLibSQLIntegrationTest().then(() => {
          console.log('✅ 验证通过！');
          process.exit(0);
        }).catch(error => {
          console.error('❌ 验证失败:', error);
          process.exit(1);
        });
      `;
      
      await this.runNodeScript(validationScript);
    } catch (error) {
      console.error('❌ 验证执行失败:', error);
      process.exit(1);
    }
  }

  async createBackup() {
    console.log('📦 创建数据备份...');
    
    try {
      const backupScript = `
        const path = require('path');
        const fs = require('fs').promises;
        const os = require('os');
        
        async function createBackup() {
          const userDataPath = path.join(os.homedir(), '.mario-ai'); // 假设的用户数据路径
          const backupPath = path.join(process.cwd(), 'backup-' + Date.now());
          
          await fs.mkdir(backupPath, { recursive: true });
          
          const dbFiles = ['bookmarks.db', 'history.db', 'favicons.db', 'formfill.db', 'startupTabs.db', 'permissions.db'];
          
          for (const dbFile of dbFiles) {
            try {
              const sourcePath = path.join(userDataPath, dbFile);
              const destPath = path.join(backupPath, dbFile);
              await fs.copyFile(sourcePath, destPath);
              console.log('✅ 备份', dbFile);
            } catch (error) {
              console.log('⚠️ ', dbFile, '不存在，跳过');
            }
          }
          
          console.log('📦 备份完成，路径:', backupPath);
        }
        
        createBackup().catch(console.error);
      `;
      
      await this.runNodeScript(backupScript);
    } catch (error) {
      console.error('❌ 备份失败:', error);
      process.exit(1);
    }
  }

  async buildProject() {
    console.log('🔨 构建项目...');
    
    return new Promise((resolve, reject) => {
      const buildProcess = spawn('pnpm', ['build'], {
        cwd: this.electronPath,
        stdio: 'inherit'
      });
      
      buildProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ 项目构建完成');
          resolve();
        } else {
          reject(new Error(`构建失败，退出码: ${code}`));
        }
      });
    });
  }

  async runNodeScript(script) {
    return new Promise((resolve, reject) => {
      const nodeProcess = spawn('node', ['-e', script], {
        cwd: this.electronPath,
        stdio: 'inherit'
      });
      
      nodeProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`脚本执行失败，退出码: ${code}`));
        }
      });
    });
  }

  async confirmMigration() {
    console.log('⚠️  警告：此操作将迁移您的数据到新的数据库系统');
    console.log('   建议在迁移前创建备份：npm run migrate:backup');
    console.log('');
    
    // 在实际环境中，这里应该有用户交互确认
    // 为了自动化，这里直接返回true
    return true;
  }

  showHelp() {
    console.log(`
🚀 LibSQL迁移工具

用法:
  node scripts/migrate-to-libsql.js <command>

命令:
  migrate     执行数据迁移（从NeDB到LibSQL）
  validate    验证迁移结果
  backup      创建数据备份
  help        显示此帮助信息

示例:
  node scripts/migrate-to-libsql.js backup
  node scripts/migrate-to-libsql.js migrate
  node scripts/migrate-to-libsql.js validate

注意:
  - 迁移前建议先运行 backup 命令创建备份
  - 迁移过程中请勿关闭应用程序
  - 迁移完成后可以运行 validate 命令验证结果
`);
  }
}

// 运行CLI
const cli = new MigrationCLI();
cli.run().catch(console.error);
