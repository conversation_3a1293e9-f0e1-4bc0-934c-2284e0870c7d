// Pure CSS positioning utilities
export const POSITIONING_STYLES = {
  centerHorizontal: {
    left: '50%',
    transform: 'translateX(-50%)'
  },
  centerBoth: {
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -50%)'
  },
  centerVertical: {
    top: '50%',
    transform: 'translateY(-50%)'
  }
};

// CSS string versions for direct injection
export const centerHorizontalCSS = `
  left: 50%;
  transform: translateX(-50%);
`;

export const centerBothCSS = `
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
`;

export const centerVerticalCSS = `
  top: 50%;
  transform: translateY(-50%);
`;
