{"name": "@mario-ai/browser", "version": "1.1.2", "license": "MIT", "description": "<PERSON> Browser - Web Frontend", "type": "module", "packageManager": "pnpm@8.15.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@mario-ai/shared": "workspace:*", "@types/animejs": "^3.1.3", "@types/react": "17.0.11", "@types/react-dom": "17.0.8", "animejs": "^3.2.1", "clsx": "^2.1.1", "mobx": "6.3.2", "mobx-react-lite": "3.2.0", "pretty-bytes": "5.6.0", "react": "17.0.2", "react-dom": "17.0.2", "react-windows-controls": "1.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "15.12.5", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.21", "eslint": "^7.29.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.24.0", "postcss": "^8.5.6", "prettier": "2.3.2", "rimraf": "^3.0.2", "tailwindcss": "^3.4.17", "typescript": "^4.3.4", "vite": "^4.4.0"}}