// 声明全局类型
declare global {
  interface Window {
    _appNameCache?: string;
  }
}

/**
 * 轻量级平台工具 - 为简单页面提供最小化的平台抽象
 * 避免完整平台服务的开销，保持多入口架构的性能优势
 *
 * 数据库策略：
 * - 开发环境：优先使用localStorage，快速启动，无需复杂设置
 * - 生产环境：自动检测并使用Electron数据库，提供完整功能
 * - 降级机制：当Electron数据库不可用时，自动降级到localStorage
 *
 * 使用建议：
 * - 开发时：localStorage足够满足大部分需求，数据在浏览器中持久化
 * - 生产时：Electron环境会自动使用真实数据库，提供完整ACID特性
 * - 数据迁移：可以通过导入/导出功能在不同存储间迁移数据
 */

import { toJS } from 'mobx';
import { getWebUIURL } from './webui';

/**
 * 安全的IPC调用包装器
 * 防止传递不可序列化的对象
 */
const safeIpcInvoke = async (channel: string, ...args: any[]): Promise<any> => {
  if (!ipcRenderer) {
    throw new Error('IPC not available');
  }

  try {
    // 深度清理参数，移除不可序列化的内容
    const cleanArgs = args.map(arg => {
      if (arg === null || arg === undefined) return arg;
      if (typeof arg === 'function') return '[Function]';
      if (typeof arg === 'symbol') return '[Symbol]';
      if (arg instanceof Error) return { message: arg.message, stack: arg.stack };

      // 对于对象，进行深度清理
      if (typeof arg === 'object') {
        try {
          return JSON.parse(JSON.stringify(arg));
        } catch (error) {
          console.warn('[Platform] Failed to serialize argument:', arg, error);
          return '[Unserializable Object]';
        }
      }

      return arg;
    });

    return await ipcRenderer.invoke(channel, ...cleanArgs);
  } catch (error) {
    console.error('[Platform] IPC call failed:', channel, error);
    throw error;
  }
};

// 轻量级数据库接口
interface LiteDatabase<T> {
  get(query?: any): Promise<T[]>;
  insert(item: T): Promise<T>;
  update(query: any, update: T): Promise<void>;
  remove(query: any, multi?: boolean): Promise<void>;
}

// 动态获取electron模块，避免在非electron环境出错
let ipcRenderer: any;
let remote: any;
let clipboard: any;

try {
  if (typeof window !== 'undefined' && (window as any).require) {
    const electron = (window as any).require('electron');
    ipcRenderer = electron.ipcRenderer;
    clipboard = electron.clipboard;
    remote = (window as any).require('@electron/remote');
  }
} catch (error) {
  // 在非electron环境中静默失败
}

// 获取窗口ID的工具函数
const getWindowId = (): number => {
  // 从全局变量或其他方式获取windowId
  // 这里需要根据实际情况调整
  return (window as any).windowId || 1;
};

/**
 * 轻量级窗口控制工具
 * 只包含基本功能，无复杂抽象层
 */
export const windowControls = {
  close: () => {
    if (ipcRenderer) {
      ipcRenderer.send(`window-close-${getWindowId()}`);
    } else {
      console.log('[Lite] Close window');
    }
  },

  minimize: () => {
    if (ipcRenderer) {
      ipcRenderer.send(`window-minimize-${getWindowId()}`);
    } else {
      console.log('[Lite] Minimize window');
    }
  },

  maximize: () => {
    if (ipcRenderer) {
      ipcRenderer.send(`window-toggle-maximize-${getWindowId()}`);
    } else {
      console.log('[Lite] Maximize window');
    }
  },

  toggleFullscreen: () => {
    if (remote) {
      windowControls.getCurrentWindow().setFullScreen(false);
    } else {
      console.log('[Lite] Toggle fullscreen');
    }
  },

  setAlwaysOnTop: (alwaysOnTop: boolean) => {
    if (ipcRenderer) {
      ipcRenderer.send('set-always-on-top', alwaysOnTop);
    } else {
      console.log('[Lite] Set always on top:', alwaysOnTop);
    }
  },

  getAppName: (): string => {
    if (ipcRenderer) {
      // 使用缓存避免重复调用
      if (!window._appNameCache) {
        try {
          // 使用异步invoke，但提供同步fallback
          ipcRenderer.invoke('get-app-name').then(name => {
            window._appNameCache = name || 'Mario AI';
          });
          return 'Mario AI'; // 首次调用返回默认值
        } catch (error) {
          console.warn('[Platform] Failed to get app name:', error);
          return 'Mario AI';
        }
      }
      return window._appNameCache;
    } else {
      return 'Mario AI';
    }
  }
};

/**
 * Shell 操作工具
 */
export const shellUtils = {
  openPath: (path: string) => {
    if (ipcRenderer) {
      return ipcRenderer.invoke('shell-open-path', path);
    } else {
      console.log('[Lite] Open path:', path);
      // 在非Electron环境中的替代方案
      if (typeof window !== 'undefined') {
        window.open(`file://${path}`, '_blank');
      }
    }
  },

  showItemInFolder: (path: string) => {
    if (ipcRenderer) {
      return ipcRenderer.invoke('shell-show-item-in-folder', path);
    } else {
      console.log('[Lite] Show item in folder:', path);
    }
  },

  trashItem: (path: string): Promise<boolean> => {
    if (ipcRenderer) {
      return ipcRenderer.invoke('shell-trash-item', path);
    } else {
      console.log('[Lite] Trash item:', path);
      return Promise.resolve(false);
    }
  }
};

/**
 * 对话框工具
 */
export const dialogUtils = {
  showMessageBox: (options: {
    title: string;
    message: string;
    type?: 'info' | 'warning' | 'error';
    buttons?: string[];
  }) => {
    if (ipcRenderer) {
      return ipcRenderer.invoke('dialog-show-message-box', options);
    } else {
      console.log('[Lite] Show message box:', options);
      // 在非Electron环境中使用浏览器alert
      alert(`${options.title}\n\n${options.message}`);
    }
  }
};

/**
 * 剪贴板工具
 */
export const clipboardUtils = {
  write: (text: string) => {
    if (clipboard) {
      clipboard.writeText(text);
    } else if (navigator.clipboard) {
      navigator.clipboard.writeText(text);
    } else {
      console.log('[Lite] Write to clipboard:', text);
    }
  },

  read: (): string => {
    if (clipboard) {
      return clipboard.readText();
    } else {
      console.log('[Lite] Read from clipboard');
      return '';
    }
  }
};

/**
 * 轻量级搜索工具
 */
export const searchUtils = {
  show: (params: { text: string; x: number; y: number; width: number }) => {
    if (ipcRenderer) {
      ipcRenderer.send(`search-show-${getWindowId()}`, params);
    } else {
      console.log('[Lite] Show search:', params);
    }
  },

  hide: () => {
    if (ipcRenderer) {
      ipcRenderer.send(`search-hide-${getWindowId()}`);
    } else {
      console.log('[Lite] Hide search');
    }
  }
};

/**
 * 轻量级标签页工具
 */
export const tabUtils = {
  close: (tabId: string) => {
    if (ipcRenderer) {
      ipcRenderer.send(`view-destroy-${getWindowId()}`, tabId);
    } else {
      console.log('[Lite] Close tab:', tabId);
    }
  },

  show: (tabId: string) => {
    if (ipcRenderer) {
      ipcRenderer.send(`browserview-show-${getWindowId()}`, tabId);
    } else {
      console.log('[Lite] Show tab:', tabId);
    }
  },

  hidePreview: (tabId: string) => {
    if (ipcRenderer) {
      ipcRenderer.send(`hide-tab-preview-${getWindowId()}`);
    } else {
      console.log('[Lite] Hide tab preview:', tabId);
    }
  }
};

/**
 * 设置窗口ID的工具函数
 * 在页面初始化时调用
 */
export const setWindowId = (windowId: number) => {
  (window as any).windowId = windowId;
};

/**
 * 轻量级视图方法调用工具
 */
export const viewUtils = {
  callMethod: async (tabId: string, method: string, ...args: any[]): Promise<any> => {
    if (ipcRenderer) {
      try {
        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        return await eventUtils.invoke('web-contents-call', {
          webContentsId: parseInt(tabId),
          method: method,
          args: args
        });
      } catch (error) {
        console.warn(`[Platform] Web contents call ${method} failed, using fallback:`, error);
        // 降级到轻量级实现
      }
    }

    // 轻量级实现常用的view方法
    console.log('[Platform] Call view method (fallback):', tabId, method, args);

    switch (method) {
      case 'create':
        console.log(`[Platform] Create view for tab ${tabId}`);
        return Promise.resolve();

      case 'destroy':
        console.log(`[Platform] Destroy view for tab ${tabId}`);
        return Promise.resolve();

      case 'select':
        console.log(`[Platform] Select view for tab ${tabId}`);
        return Promise.resolve();

      case 'loadURL':
        console.log(`[Platform] Load URL in tab ${tabId}:`, args[0]);
        return Promise.resolve();

      case 'home':
        console.log(`[Platform] Navigate to home for tab ${tabId}`);
        // 实现首页导航功能
        const homeUrl = getWebUIURL('newtab');
        window.location.href = homeUrl;
        return Promise.resolve();

      default:
        return Promise.resolve();
    }
  },

  loadURL: (tabId: string, url: string) => {
    console.log('[ViewUtils] loadURL called with tabId:', tabId, 'url:', url);
    if (ipcRenderer) {
      console.log('[ViewUtils] Sending IPC message:', `view-load-url-${getWindowId()}`, tabId, url);
      ipcRenderer.send(`view-load-url-${getWindowId()}`, tabId, url);
    } else {
      console.log('[Lite] Load URL:', tabId, url);
    }
  },

  reload: async (tabId: string) => {
    if (ipcRenderer) {
      console.log('[Platform] Reloading tab:', tabId);
      try {
        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        await eventUtils.invoke('web-contents-call', {
          webContentsId: parseInt(tabId),
          method: 'reload'
        });
        console.log('[Platform] Successfully reloaded tab:', tabId);
      } catch (error) {
        console.error('[Platform] Failed to reload tab:', error);
      }
    } else {
      console.log('[Lite] Reload tab:', tabId);
    }
  },

  goBack: async (tabId: string) => {
    if (ipcRenderer) {
      console.log('[Platform] Going back in tab:', tabId);
      try {
        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        await eventUtils.invoke('web-contents-call', {
          webContentsId: parseInt(tabId),
          method: 'goBack'
        });
        console.log('[Platform] Successfully went back in tab:', tabId);
      } catch (error) {
        console.error('[Platform] Failed to go back in tab:', error);
      }
    } else {
      console.log('[Lite] Go back:', tabId);
    }
  },

  goForward: async (tabId: string) => {
    if (ipcRenderer) {
      console.log('[Platform] Going forward in tab:', tabId);
      try {
        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        await eventUtils.invoke('web-contents-call', {
          webContentsId: parseInt(tabId),
          method: 'goForward'
        });
        console.log('[Platform] Successfully went forward in tab:', tabId);
      } catch (error) {
        console.error('[Platform] Failed to go forward in tab:', error);
      }
    } else {
      console.log('[Lite] Go forward:', tabId);
    }
  },

  stop: async (tabId: string) => {
    if (ipcRenderer) {
      console.log('[Platform] Stopping loading in tab:', tabId);
      try {
        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        await eventUtils.invoke('web-contents-call', {
          webContentsId: parseInt(tabId),
          method: 'stop'
        });
        console.log('[Platform] Successfully stopped loading in tab:', tabId);
      } catch (error) {
        console.error('[Platform] Failed to stop loading in tab:', error);
      }
    } else {
      console.log('[Lite] Stop loading:', tabId);
    }
  },

  findInPage: async (tabId: string, text: string, options?: any) => {
    if (ipcRenderer) {
      return await ipcRenderer.invoke(`view-find-${getWindowId()}`, tabId, text, options);
    } else {
      console.log('[Lite] Find in page:', tabId, text, options);
      return Promise.resolve();
    }
  },

  stopFindInPage: (tabId: string, action?: string) => {
    if (ipcRenderer) {
      ipcRenderer.send(`view-stop-find-${getWindowId()}`, tabId, action);
    } else {
      console.log('[Lite] Stop find in page:', tabId, action);
    }
  },

  // 添加callViewMethod别名，保持向后兼容
  callViewMethod: async (tabId: string | number, method: string, ...args: any[]): Promise<any> => {
    if (ipcRenderer) {
      return await ipcRenderer.invoke(`view-method-${getWindowId()}`, tabId, method, ...args);
    } else {
      console.log('[Lite] Call view method:', tabId, method, args);
      return Promise.resolve();
    }
  }
};

/**
 * 轻量级事件管理工具（最终版本）
 */
export const eventUtils = {
  on: (channel: string, callback: (...args: any[]) => void) => {
    if (ipcRenderer) {
      ipcRenderer.on(channel, callback);
    } else {
      console.log('[Lite] Register event listener:', channel);
    }
  },

  send: (channel: string, ...args: any[]) => {
    if (ipcRenderer) {
      try {
        // 序列化MobX对象，确保IPC通信安全
        const serializedArgs = args.map(arg => toJS(arg));
        ipcRenderer.send(channel, ...serializedArgs);
      } catch (error) {
        console.error('[EventUtils] Error sending event:', error);
      }
    }
  },

  invoke: async (channel: string, ...args: any[]): Promise<any> => {
    if (ipcRenderer) {
      try {
        // ✅ 重构: 安全的序列化处理，避免IPC序列化错误 (修复根本问题)
        const serializedArgs = args.map(arg => {
          if (arg === null || arg === undefined) return arg;
          if (typeof arg === 'function') return '[Function]';
          if (typeof arg === 'symbol') return '[Symbol]';
          if (arg instanceof Error) return { message: arg.message, stack: arg.stack };

          // 对于对象，先尝试toJS，然后进行JSON序列化验证
          if (typeof arg === 'object') {
            try {
              const mobxSerialized = toJS(arg);
              // 验证是否可以JSON序列化
              JSON.stringify(mobxSerialized);
              return mobxSerialized;
            } catch (error) {
              console.warn(`[Lite] Failed to serialize argument for ${channel}:`, error);
              return '[Unserializable Object]';
            }
          }

          return arg;
        });

        return await ipcRenderer.invoke(channel, ...serializedArgs);
      } catch (error) {
        console.warn(`[Lite] IPC invoke failed for ${channel}, using fallback:`, error);
        // 降级到轻量级实现
      }
    }

    // 轻量级实现常用的IPC调用
    switch (channel) {
      case 'get-command-line-args':
        console.log('[Lite] Get command line args (mock)');
        return []; // 返回空的命令行参数

      case 'file-exists':
        console.log('[Lite] File exists check (mock):', args[0]);
        return false; // 在轻量级环境中，文件检查总是返回false

      case 'extension-read-icon':
        console.log('[Lite] Extension read icon (mock):', args);
        return null; // 返回空的图标数据

      case 'get-extensions':
        console.log('[Lite] Get extensions (mock)');
        return []; // 返回空的扩展列表

      case 'bookmarks-get':
        console.log('[Lite] Get bookmarks - using mock data in fallback');
        // 在fallback中使用mock数据，避免无限递归
        return [];

      case 'bookmarks-get-folders':
        console.log('[Lite] Get bookmark folders - forwarding to main process');
        if (ipcRenderer) {
          try {
            const result = await ipcRenderer.invoke('bookmarks-get-folders');
            console.log('[Lite] Bookmark folders result:', result);
            return result;
          } catch (error) {
            console.error('[Lite] Error getting bookmark folders:', error);
            return [];
          }
        }
        console.error('[Lite] ipcRenderer not available for folders');
        return [];

      case 'bookmarks-add':
        console.log('[Lite] Add bookmark - forwarding to main process');
        console.log('[Lite] ipcRenderer available:', !!ipcRenderer);
        console.log('[Lite] Bookmark data:', args[0]);
        if (ipcRenderer) {
          try {
            const result = await ipcRenderer.invoke('bookmarks-add', args[0]);
            console.log('[Lite] Bookmark add result:', result);
            return result;
          } catch (error) {
            console.error('[Lite] Error adding bookmark:', error);
            throw error;
          }
        }
        console.error('[Lite] ipcRenderer not available');
        return null;



      default:
        console.log('[Lite] Invoke:', channel, args);

        // 为特定的调用返回正确的默认值，避免null错误
        if (channel === 'storage-get') {
          return Promise.resolve([]); // 返回空数组而不是null
        }
        if (channel.startsWith('views-create-')) {
          // 为批量创建Tab返回模拟的ID数组
          const count = args[0]?.length || 1;
          const ids = Array.from({ length: count }, (_, i) => Date.now() + i);
          console.log('[Lite] Mock tabs creation (plural), returning ID array:', ids);
          return Promise.resolve(ids); // 总是返回数组
        }
        if (channel.startsWith('view-create-')) {
          // 为单个创建Tab返回模拟的ID
          const id = Date.now();
          console.log('[Lite] Mock tab creation (singular), returning ID:', id);
          return Promise.resolve(id); // 返回单个ID
        }
        if (channel === 'get-command-line-args') {
          return Promise.resolve([]); // 返回空的命令行参数数组
        }
        if (channel === 'file-exists') {
          return Promise.resolve(false); // 文件不存在
        }

        return Promise.resolve(null);
    }
  },

  sendSync: (channel: string, ...args: any[]): any => {
    if (ipcRenderer) {
      return ipcRenderer.sendSync(channel, ...args);
    } else {
      console.log('[Lite] Send sync:', channel, args);
      // 智能默认返回值
      if (channel.includes('is-incognito')) return false;
      if (channel.includes('get-')) return null;
      return false;
    }
  },

  removeAllListeners: (channel: string) => {
    if (ipcRenderer) {
      ipcRenderer.removeAllListeners(channel);
    } else {
      console.log('[Lite] Remove all listeners:', channel);
    }
  }
};

/**
 * 轻量级下载管理工具
 */
export const downloadUtils = {
  pause: (id: string) => {
    if (ipcRenderer) {
      ipcRenderer.send('download-pause', id);
    } else {
      console.log('[Lite] Pause download:', id);
    }
  },

  resume: (id: string) => {
    if (ipcRenderer) {
      ipcRenderer.send('download-resume', id);
    } else {
      console.log('[Lite] Resume download:', id);
    }
  },

  cancel: (id: string) => {
    if (ipcRenderer) {
      ipcRenderer.send('download-cancel', id);
    } else {
      console.log('[Lite] Cancel download:', id);
    }
  },

  remove: (id: string) => {
    if (ipcRenderer) {
      ipcRenderer.send('download-remove', id);
    } else {
      console.log('[Lite] Remove download:', id);
    }
  }
};

/**
 * 轻量级书签管理工具
 */
export const bookmarkUtils = {
  get: async () => {
    if (ipcRenderer) {
      return await ipcRenderer.invoke('bookmarks-get');
    } else {
      console.log('[Lite] Get bookmarks');
      return Promise.resolve([]);
    }
  },

  add: (bookmark: any) => {
    if (ipcRenderer) {
      // 确保传递的对象是可序列化的
      const serializableBookmark = {
        title: bookmark.title,
        url: bookmark.url,
        favicon: bookmark.favicon,
        parent: bookmark.parent,
        isFolder: bookmark.isFolder || false,
        children: bookmark.children || [],
        static: bookmark.static,
        order: bookmark.order
      };
      ipcRenderer.send('bookmarks-add', serializableBookmark);
    } else {
      console.log('[Lite] Add bookmark:', bookmark);
    }
  },

  remove: (id: string) => {
    if (ipcRenderer) {
      ipcRenderer.send('bookmarks-remove', id);
    } else {
      console.log('[Lite] Remove bookmark:', id);
    }
  },

  edit: (id: string, bookmark: any) => {
    if (ipcRenderer) {
      // 确保传递的对象是可序列化的
      const serializableBookmark = {
        title: bookmark.title,
        url: bookmark.url,
        favicon: bookmark.favicon,
        parent: bookmark.parent,
        isFolder: bookmark.isFolder || false,
        children: bookmark.children || [],
        static: bookmark.static,
        order: bookmark.order
      };
      ipcRenderer.send('bookmarks-edit', id, serializableBookmark);
    } else {
      console.log('[Lite] Edit bookmark:', id, bookmark);
    }
  },
};


/**
 * 轻量级设置工具
 */
export const settingsUtils = {
  save: (settings: any) => {
    if (ipcRenderer) {
      ipcRenderer.send('save-settings', settings);
    } else {
      console.log('[Lite] Save settings:', settings);
    }
  }
};

/**
 * 检查是否在electron环境中
 */
export const isElectronEnvironment = (): boolean => {
  return typeof window !== 'undefined' && 
         (window as any).require !== undefined &&
         process?.versions?.electron !== undefined;
};

/**
 * 轻量级菜单工具
 */
export const menuUtils = {
  showContextMenu: (
    menuItems: Array<{
      label: string;
      click: () => void;
      type?: string;
      enabled?: boolean;
    }>,
    position?: { x: number; y: number }
  ) => {
    if (ipcRenderer) {
      // 发送菜单数据到主进程，让主进程创建原生菜单
      ipcRenderer.send('show-context-menu', {
        items: menuItems.map(item => ({
          label: item.label,
          type: item.type || 'normal',
          enabled: item.enabled !== false
        })),
        position
      });

      // 监听菜单点击事件
      const handleMenuClick = (event: any, index: number) => {
        if (menuItems[index] && menuItems[index].click) {
          menuItems[index].click();
        }
        ipcRenderer.removeListener('context-menu-click', handleMenuClick);
      };

      ipcRenderer.once('context-menu-click', handleMenuClick);
    } else {
      console.log('[Lite] Show context menu:', menuItems, position);
      // 在非Electron环境中，可以显示HTML菜单或其他替代方案
    }
  }
};

/**
 * 轻量级数据库工具
 * 在electron环境中使用真实数据库，在浏览器环境中使用Mock
 */
// 环境检测和配置
interface PlatformConfig {
  forceLocalStorage: boolean;
  enableElectronFeatures: boolean;
  environment: 'browser' | 'electron-lite' | 'electron-full';
}

const detectEnvironment = (): PlatformConfig => {
  const isElectron = !!(window.process?.versions?.electron);
  const hasIpcRenderer = !!ipcRenderer;

  // 检查是否强制使用轻量级模式（仅用于开发调试）
  const forceLocalStorage = localStorage.getItem('mario-ai-force-lite') === 'true';

  if (!isElectron) {
    console.log('[Platform] Detected browser environment');
    return {
      forceLocalStorage: true,
      enableElectronFeatures: false,
      environment: 'browser'
    };
  }

  if (forceLocalStorage) {
    console.log('[Platform] Detected Electron environment with forced lite mode');
    return {
      forceLocalStorage: true,
      enableElectronFeatures: false,
      environment: 'electron-lite'
    };
  }

  if (!hasIpcRenderer) {
    console.log('[Platform] Detected Electron environment without IPC (fallback to lite)');
    return {
      forceLocalStorage: true,
      enableElectronFeatures: false,
      environment: 'electron-lite'
    };
  }

  console.log('[Platform] Detected full Electron environment');
  return {
    forceLocalStorage: false,
    enableElectronFeatures: true,
    environment: 'electron-full'
  };
};

const platformConfig = detectEnvironment();
console.log('[Platform] Detected environment:', platformConfig);

export class LiteDatabaseImpl<T> implements LiteDatabase<T> {
  private dbName: string;
  private config: PlatformConfig;

  constructor(dbName: string) {
    this.dbName = dbName;
    this.config = platformConfig;
  }

  async get(query: any = {}): Promise<T[]> {
    // 基于配置决定使用哪种实现
    if (this.config.enableElectronFeatures && !this.config.forceLocalStorage) {
      try {
        console.log(`[Platform] Using Electron storage for: ${this.dbName}`, query);
        // 序列化MobX对象，确保IPC通信安全
        const serializedQuery = toJS(query);
        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        return await eventUtils.invoke('storage-get', {
          scope: this.dbName,
          query: serializedQuery
        });
      } catch (error) {
        console.warn(`[Platform] Electron storage failed, falling back to localStorage:`, error);
        // 临时降级到localStorage，但不改变配置
      }
    }

    // 使用localStorage实现
    console.log(`[Platform] Using localStorage for: ${this.dbName}`, query);

    try {
      const stored = localStorage.getItem(`db_${this.dbName}`);
      if (stored) {
        let data = JSON.parse(stored);
        if (!Array.isArray(data)) return [];

        // 简单的查询过滤
        if (Object.keys(query).length > 0) {
          data = data.filter((item: any) => {
            return Object.keys(query).every(key => {
              if (key === '_id') return item._id === query._id;
              if (key === 'parent') return item.parent === query.parent;
              if (key === 'static') return item.static === query.static;
              return item[key] === query[key];
            });
          });
        }

        return data;
      }
    } catch (error) {
      console.warn(`[Platform] Failed to read from localStorage:`, error);
    }

    return [];
  }

  async insert(item: T): Promise<T> {
    // 基于配置决定使用哪种实现
    if (this.config.enableElectronFeatures && !this.config.forceLocalStorage) {
      try {
        console.log(`[Platform] Using Electron storage insert for: ${this.dbName}`, item);
        // 序列化MobX对象，确保IPC通信安全
        const serializedItem = toJS(item);
        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        return await eventUtils.invoke('storage-insert', {
          scope: this.dbName,
          item: serializedItem
        });
      } catch (error) {
        console.warn(`[Platform] Electron storage insert failed, falling back to localStorage:`, error);
      }
    }

    // 使用localStorage实现
    console.log(`[Platform] Using localStorage insert for: ${this.dbName}`, item);

    try {
      const stored = localStorage.getItem(`db_${this.dbName}`);
      let data = [];
      if (stored) {
        data = JSON.parse(stored);
        if (!Array.isArray(data)) data = [];
      }

      // 生成唯一ID
      const newItem = {
        ...item,
        _id: (item as any)._id || `lite_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      } as T;

      data.push(newItem);
      localStorage.setItem(`db_${this.dbName}`, JSON.stringify(data));

      return newItem;
    } catch (error) {
      console.warn(`[Platform] Failed to write to localStorage:`, error);
      return { ...item, _id: `lite_${Date.now()}_${Math.random().toString(36).substring(2, 11)}` } as T;
    }
  }

  async update(query: any, update: T): Promise<void> {
    // 基于配置决定使用哪种实现
    if (this.config.enableElectronFeatures && !this.config.forceLocalStorage) {
      try {
        console.log(`[Platform] Using Electron storage update for: ${this.dbName}`, query, update);

        // 序列化MobX对象，确保IPC通信安全
        const serializedQuery = toJS(query);
        const serializedUpdate = toJS(update);

        // 检查数据是否可序列化
        try {
          JSON.stringify({query: serializedQuery, update: serializedUpdate});
        } catch (serializeError) {
          console.error(`[Platform] Data not serializable for ${this.dbName}:`, serializeError);
          console.error('[Platform] Query:', serializedQuery);
          console.error('[Platform] Update:', serializedUpdate);
          throw new Error(`Data not serializable: ${serializeError.message}`);
        }

        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        await eventUtils.invoke('storage-update', {
          scope: this.dbName,
          query: serializedQuery,
          value: serializedUpdate,
          multi: false
        });
        return;
      } catch (error) {
        console.warn(`[Platform] Electron storage update failed, falling back to localStorage:`, error);
      }
    }

    // 使用localStorage实现
    console.log(`[Platform] Using localStorage update for: ${this.dbName}`, query, update);

    try {
      const stored = localStorage.getItem(`db_${this.dbName}`);
      if (stored) {
        let data = JSON.parse(stored);
        // 简单的更新逻辑：根据_id匹配
        if (query._id) {
          data = data.map((item: any) => item._id === query._id ? { ...item, ...update } : item);
          localStorage.setItem(`db_${this.dbName}`, JSON.stringify(data));
        }
      }
    } catch (error) {
      console.warn(`[Platform] Failed to update localStorage:`, error);
    }
  }

  async remove(query: any, multi: boolean = false): Promise<void> {
    // 基于配置决定使用哪种实现
    if (this.config.enableElectronFeatures && !this.config.forceLocalStorage) {
      try {
        console.log(`[Platform] Using Electron storage remove for: ${this.dbName}`, query, multi);
        // 序列化MobX对象，确保IPC通信安全
        const serializedQuery = toJS(query);
        // ✅ 重构: 使用统一的eventUtils.invoke替代复杂的safeIpcInvoke (借鉴原始工程)
        await eventUtils.invoke('storage-remove', {
          scope: this.dbName,
          query: serializedQuery,
          multi: multi
        });
        return;
      } catch (error) {
        console.warn(`[Platform] Electron storage remove failed, falling back to localStorage:`, error);
      }
    }

    // 使用localStorage实现
    console.log(`[Platform] Using localStorage remove for: ${this.dbName}`, query, multi);

    try {
      const stored = localStorage.getItem(`db_${this.dbName}`);
      if (stored) {
        let data = JSON.parse(stored);
        // 简单的删除逻辑：根据_id匹配
        if (query._id) {
          data = data.filter((item: any) => item._id !== query._id);
          localStorage.setItem(`db_${this.dbName}`, JSON.stringify(data));
        }
      }
    } catch (error) {
      console.warn(`[Platform] Failed to remove from localStorage:`, error);
    }
  }
}





// 导出轻量级数据库工具
export const databaseUtils = {
  create: <T>(dbName: string): LiteDatabase<T> => {
    return new LiteDatabaseImpl<T>(dbName);
  },

  // 获取当前平台配置
  getConfig: () => platformConfig,

  // 强制使用轻量级模式（开发时有用）
  forceLiteMode: () => {
    localStorage.setItem('mario-ai-force-lite', 'true');
    console.log('[Platform] Forced lite mode enabled. Reload to take effect.');
  },

  // 恢复自动检测模式
  enableAutoMode: () => {
    localStorage.removeItem('mario-ai-force-lite');
    console.log('[Platform] Auto mode enabled. Reload to take effect.');
  },

  // 检查当前环境支持的功能
  getCapabilities: () => {
    return {
      environment: platformConfig.environment,
      database: platformConfig.forceLocalStorage ? 'localStorage' : 'electron+localStorage',
      ipc: !!ipcRenderer,
      electron: !!window.process?.versions?.electron
    };
  },
};

/**
 * 轻量级对话框基类
 * 替代 DialogStore，提供基本的对话框状态管理
 */
export class LiteDialogStore {
  public visible = false;
  public hideOnBlur = true;
  public visibilityWrapper = true;
  public onUpdateTabInfo: (tabId: number, data: any) => void = () => {};
  public theme: any = {
    'dialog.lightForeground': false,
    // 添加其他默认主题属性
  };
  private _webContentsId: number | null = null;
  private _windowId: number = 1;

  constructor(options: { hideOnBlur?: boolean; visibilityWrapper?: boolean; persistent?: boolean } = {}) {
    this.hideOnBlur = options.hideOnBlur !== false;
    this.visibilityWrapper = options.visibilityWrapper !== false;

    // 初始化windowId
    this._windowId = getWindowId();
    console.log('[LiteDialogStore] Initialized windowId:', this._windowId);

    // 尝试获取 webContentsId
    try {
      // 检查是否在Electron环境中
      if (typeof window !== 'undefined' && (window as any).require) {
        const remote = (window as any).require('@electron/remote');
        if (remote && remote.getCurrentWebContents) {
          this._webContentsId = remote.getCurrentWebContents().id;
          console.log('[LiteDialogStore] Got webContentsId:', this._webContentsId);
        }
      } else {
        console.log('[LiteDialogStore] Not in Electron environment, skipping webContentsId');
      }
    } catch (error) {
      console.warn('[LiteDialogStore] Failed to get webContentsId:', error);
    }


  }

  // 发送带webContentsId的事件（模拟原始DialogStore的send方法）
  public send(channel: string, ...args: any[]) {
    if (this._webContentsId !== null) {
      const eventChannel = `${channel}-${this._webContentsId}`;
      console.log('[LiteDialogStore] Sending event with webContentsId:', eventChannel);
      eventUtils.send(eventChannel, ...args);
    } else {
      console.log('[LiteDialogStore] No webContentsId, sending plain event:', channel);
      eventUtils.send(channel, ...args);
    }
  }

  // 获取webContentsId
  public get id() {
    return this._webContentsId;
  }

  // 获取windowId
  public get windowId() {
    return this._windowId;
  }

  public show() {
    this.visible = true;
  }

  public hide() {
    console.log('[LiteDialogStore] Hide called, webContentsId:', this._webContentsId);
    this.visible = false;

    // 发送hide消息给主进程，使用统一的eventUtils通信层
    setTimeout(() => {
      if (this._webContentsId) {
        console.log('[LiteDialogStore] Sending hide message:', `hide-${this._webContentsId}`);
        // 直接使用导入的eventUtils，而不是window上的
        eventUtils.send(`hide-${this._webContentsId}`);
      } else {
        console.log('[LiteDialogStore] No webContentsId, sending generic hide-dialog');
        eventUtils.send('hide-dialog');
      }
    });
  }

  public toggle() {
    this.visible = !this.visible;
  }

  /**
   * 获取当前 WebContents ID，用于与主进程通信
   */
  public getWebContentsId(): number | null {
    return this._webContentsId;
  }
}

/**
 * 轻量级扩展通信工具
 */
export const extensionUtils = {
  getInvoker: () => ({
    uninstall: (id: string) => {
      if (ipcRenderer) {
        ipcRenderer.invoke('extension-uninstall', id);
      } else {
        console.log('[Lite] Extension uninstall:', id);
      }
    },
    inspectBackgroundPage: (id: string) => {
      if (ipcRenderer) {
        ipcRenderer.invoke('extension-inspect-background', id);
      } else {
        console.log('[Lite] Extension inspect background:', id);
      }
    }
  })
};

/**
 * 轻量级网络通信工具
 */
export const networkUtils = {
  getInvoker: () => ({
    request: async (url: string, options?: any) => {
      if (ipcRenderer) {
        return await ipcRenderer.invoke('network-request', url, options);
      } else {
        console.log('[Lite] Network request:', url, options);
        return { data: null, error: 'Mock response' };
      }
    }
  })
};

/**
 * 轻量级设置常量
 */
export const LITE_DEFAULT_SETTINGS = {
  theme: 'wexond-light',
  darkContents: false,
  shield: true,
  multrin: true,
  animations: true,
  bookmarksBar: false,
  suggestions: true,
  themeAuto: true,
  searchEngines: [
    {
      name: 'Google',
      url: 'https://www.google.com/search?q=%s',
      keywordsUrl: 'http://google.com/complete/search?client=chrome&q=%s',
      keyword: 'google.com',
      icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAA...'
    }
  ],
  searchEngine: 0,
  startupBehavior: { type: 'empty' },
  warnOnQuit: false,
  version: 2,
  downloadsDialog: false,
  downloadsPath: '',
  doNotTrack: true,
  topBarVariant: 'default',
  autofill: true,
  newtab: ""
};

// 导出ipcRenderer供其他模块使用
export { ipcRenderer };
