import React, { useState, useEffect } from 'react';
import { Button } from '../../../shared/components/Button';
import { ScrollArea } from '../../../shared/components/ScrollArea';
import { cn } from '../../../shared/utils/cn';
import { useChatHistory } from '../hooks/useChatHistory';

interface ChatHistorySidebarProps {
  onNewChat: () => void;
  onChatSelect?: (chatId: string) => void;
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface ChatSession {
  id: string;
  title: string;
  timestamp: Date;
  messageCount: number;
}

interface ChatGroup {
  title: string;
  sessions: ChatSession[];
}

export const ChatHistorySidebar: React.FC<ChatHistorySidebarProps> = ({
  onNewChat,
  onChatSelect,
  className,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const { currentMessages } = useChatHistory();
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({
    今天: true,
    昨天: true,
    本周: false,
    本月: false,
    更早: false,
  });

  const [searchQuery, setSearchQuery] = useState('');
  const [chatGroups, setChatGroups] = useState<ChatGroup[]>([]);

  // 模拟聊天历史数据
  useEffect(() => {
    const mockSessions: ChatSession[] = [
      {
        id: 'current',
        title: currentMessages.length > 0 ? '当前对话' : '新对话',
        timestamp: new Date(),
        messageCount: currentMessages.length
      }
    ];

    const groups: ChatGroup[] = [
      {
        title: '今天',
        sessions: mockSessions
      }
    ];

    setChatGroups(groups);
  }, [currentMessages]);

  const toggleGroup = (groupTitle: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupTitle]: !prev[groupTitle]
    }));
  };

  const handleChatClick = (chatId: string) => {
    if (onChatSelect) {
      onChatSelect(chatId);
    }
  };

  const handleDeleteChat = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('确定要删除这个对话吗？')) {
      // TODO: 实现删除逻辑
      console.log('删除对话:', chatId);
    }
  };

  if (isCollapsed) {
    return (
      <div className={cn("w-16 h-full border-l border-mario-border bg-mario-page flex flex-col items-center py-4", className)}>
        <Button
          size="icon"
          variant="outline"
          onClick={onToggleCollapse}
          className="mb-4"
          title="展开侧边栏"
        >
          <span className="text-sm">📋</span>
        </Button>
        <Button
          size="icon"
          variant="outline"
          onClick={onNewChat}
          title="新建对话"
        >
          <span className="text-sm">➕</span>
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("w-80 h-full border-l border-mario-border bg-mario-page flex flex-col", className)}>
      {/* Header */}
      <div className="p-4 border-b border-mario-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-mario-page-text">对话历史</h2>
          <Button
            size="icon"
            variant="outline"
            onClick={onToggleCollapse}
            title="收起侧边栏"
          >
            <span className="text-sm">📋</span>
          </Button>
        </div>
        
        <Button
          onClick={onNewChat}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
        >
          <span className="mr-2">➕</span>
          新建对话
        </Button>
      </div>

      {/* Search */}
      <div className="p-4 border-b border-mario-border">
        <div className="relative">
          <input
            type="text"
            placeholder="搜索对话..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-3 py-2 bg-mario-dialog border border-mario-border rounded-md text-mario-page-text placeholder:text-mario-page-text placeholder:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <span className="absolute right-3 top-2.5 text-mario-page-text opacity-50">🔍</span>
        </div>
      </div>

      {/* Chat Groups */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {chatGroups.map((group) => (
            <div key={group.title} className="mb-4">
              <button
                onClick={() => toggleGroup(group.title)}
                className="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-mario-page-text hover:bg-mario-hover rounded-md"
              >
                <span>{group.title}</span>
                <span className={cn(
                  "transition-transform",
                  expandedGroups[group.title] ? "rotate-90" : ""
                )}>
                  ▶
                </span>
              </button>
              
              {expandedGroups[group.title] && (
                <div className="mt-2 space-y-1">
                  {group.sessions.map((session) => (
                    <div
                      key={session.id}
                      onClick={() => handleChatClick(session.id)}
                      className="group flex items-center justify-between px-3 py-2 text-sm text-mario-page-text hover:bg-mario-hover rounded-md cursor-pointer"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="truncate font-medium">{session.title}</div>
                        <div className="text-xs opacity-60">
                          {session.messageCount} 条消息
                        </div>
                      </div>
                      <button
                        onClick={(e) => handleDeleteChat(session.id, e)}
                        className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-100 rounded transition-opacity"
                        title="删除对话"
                      >
                        <span className="text-xs text-red-500">🗑️</span>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
          
          {chatGroups.length === 0 && (
            <div className="text-center py-8 text-mario-page-text opacity-60">
              <div className="text-2xl mb-2">💬</div>
              <p className="text-sm">暂无对话历史</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default ChatHistorySidebar;
