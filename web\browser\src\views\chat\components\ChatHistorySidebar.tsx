import React, { useState, useEffect } from 'react';
import { Button } from '../../../shared/components/Button';
import { ScrollArea } from '../../../shared/components/ScrollArea';
import { cn } from '../../../shared/utils/cn';
import { useChatHistory, ChatGroup } from '../hooks/useChatHistory';

interface ChatHistorySidebarProps {
  onNewChat: () => void;
  onChatSelect?: (chatId: string) => void;
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export const ChatHistorySidebar: React.FC<ChatHistorySidebarProps> = ({
  onNewChat,
  onChatSelect,
  className,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const { getChatGroups, loadChatSession, deleteChatSession, isLoading } = useChatHistory();

  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({
    今天: true,
    昨天: true,
    本周: false,
    本月: false,
    更早: false,
  });

  const [searchQuery, setSearchQuery] = useState('');
  const [chatGroups, setChatGroups] = useState<ChatGroup[]>([]);

  useEffect(() => {
    console.log('📊 ChatHistorySidebar: Updating chat groups, isLoading:', isLoading);
    const groups = getChatGroups();
    console.log('📊 ChatHistorySidebar: Got groups:', groups.length, groups);
    setChatGroups(groups);
  }, [getChatGroups, isLoading]);

  const toggleSidebar = () => {
    if (onToggleCollapse) {
      onToggleCollapse();
    }
  };

  const toggleGroup = (groupTitle: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupTitle]: !prev[groupTitle]
    }));
  };

  const handleChatClick = (chatId: string) => {
    loadChatSession(chatId);
    if (onChatSelect) {
      onChatSelect(chatId);
    }
  };

  const handleDeleteChat = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('确定要删除这个对话吗？')) {
      deleteChatSession(chatId);
      // Refresh chat groups
      const groups = getChatGroups();
      setChatGroups(groups);
    }
  };

  const filteredGroups = chatGroups.map(group => ({
    ...group,
    chats: group.chats.filter(chat =>
      chat.title.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(group => group.chats.length > 0);

  const sidebarContent = (
    <div className="flex h-full flex-col bg-mario-page text-mario-page-text">
      {/* Header with New Chat and Toggle */}
      <div className={cn("flex-shrink-0 border-b border-mario-border", isCollapsed ? "p-2" : "p-4")}>
        {isCollapsed ? (
          <div className="flex flex-col items-center gap-2">
            {/* New Chat Button */}
            <button
              onClick={onNewChat}
              className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600 hover:bg-blue-700 text-white shadow-sm transition-all duration-200"
              aria-label="新建对话"
            >
              <span className="text-sm">➕</span>
            </button>
            
            {/* Toggle Button */}
            <button
              onClick={toggleSidebar}
              className="flex h-10 w-10 items-center justify-center rounded-lg bg-mario-dialog hover:bg-mario-hover text-mario-page-text border border-mario-border shadow-sm transition-all duration-200"
              aria-label="展开侧边栏"
            >
              <span className="text-sm">📋</span>
            </button>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center justify-between gap-2">
              <button
                onClick={onNewChat}
                className="flex-1 flex items-center gap-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 shadow-sm transition-all duration-200"
              >
                <span className="text-sm">➕</span>
                <span className="font-medium">新建对话</span>
              </button>
              
              <button
                onClick={toggleSidebar}
                className="flex h-10 w-10 items-center justify-center rounded-lg bg-mario-dialog hover:bg-mario-hover text-mario-page-text border border-mario-border shadow-sm transition-all duration-200"
                aria-label="收起侧边栏"
              >
                <span className="text-sm">📋</span>
              </button>
            </div>

            {/* Search */}
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-mario-page-text opacity-50">🔍</span>
              <input
                type="text"
                placeholder="搜索对话..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full rounded-lg border border-sidebar-border bg-sidebar-accent/50 pl-9 pr-3 py-2 text-sm placeholder:text-muted-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary/20"
              />
            </div>
          </div>
        )}
      </div>

      {/* Chat History */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className={cn("p-2", isCollapsed && "px-1")}>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex flex-col items-center gap-2 text-mario-page-text opacity-60">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-current"></div>
                  {!isCollapsed && <span className="text-sm">加载聊天记录...</span>}
                </div>
              </div>
            ) : isCollapsed ? (
              <div className="flex flex-col items-center gap-2">
                {chatGroups.slice(0, 5).map((group) => (
                  group.chats.slice(0, 3).map((chat) => (
                    <button
                      key={chat.id}
                      onClick={() => handleChatClick(chat.id)}
                      className="flex h-10 w-10 items-center justify-center rounded-lg hover:bg-mario-hover text-mario-page-text transition-all duration-200"
                      title={chat.title}
                    >
                      <span className="text-sm">💬</span>
                    </button>
                  ))
                ))}
                {chatGroups.length === 0 && (
                  <div className="text-xs text-mario-page-text opacity-60 text-center py-4">
                    暂无聊天记录
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredGroups.length === 0 && !isLoading && (
                  <div className="text-center py-8 text-mario-page-text opacity-60">
                    <span className="text-2xl opacity-50 block mb-2">💬</span>
                    <p className="text-sm">暂无聊天记录</p>
                    <p className="text-xs mt-1">开始新对话来创建聊天记录</p>
                  </div>
                )}
                {filteredGroups.map((group) => (
                  <div key={group.title}>
                    <button
                      onClick={() => toggleGroup(group.title)}
                      className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium text-mario-page-text hover:bg-mario-hover transition-colors duration-200"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm">📅</span>
                        <span>{group.title}</span>
                        <span className="text-xs text-muted-foreground">({group.chats.length})</span>
                      </div>
                      <span className={cn(
                        "transition-transform text-sm",
                        expandedGroups[group.title] ? "rotate-90" : ""
                      )}>
                        ▶
                      </span>
                    </button>

                    {expandedGroups[group.title] && (
                      <div className="ml-6 space-y-1">
                        {group.chats.map((chat) => (
                          <div
                            key={chat.id}
                            className="group flex items-center gap-2 rounded-lg px-3 py-2 text-sm hover:bg-mario-hover transition-colors duration-200"
                          >
                            <button
                              onClick={() => handleChatClick(chat.id)}
                              className="flex-1 flex items-center gap-2 text-left"
                              title={chat.title}
                            >
                              <span className="text-sm flex-shrink-0">💬</span>
                              <span className="truncate text-mario-page-text">{chat.title}</span>
                            </button>
                            <button
                              onClick={(e) => handleDeleteChat(chat.id, e)}
                              className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-100 text-red-500 transition-all duration-200"
                              title="删除对话"
                            >
                              <span className="text-xs">🗑️</span>
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );

  return (
    <div className={cn("relative flex h-full", className)}>
      <aside className={cn(
        "h-full border-l border-mario-border bg-mario-page text-mario-page-text transition-all duration-300 flex-shrink-0",
        isCollapsed ? "w-16" : "w-80"
      )}>
        {sidebarContent}
      </aside>
    </div>
  );
};

export default ChatHistorySidebar;
