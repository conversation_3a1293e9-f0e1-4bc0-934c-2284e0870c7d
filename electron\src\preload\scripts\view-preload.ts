import {ipc<PERSON><PERSON><PERSON>, webFrame} from 'electron';
import { toJS } from 'mobx';

// ✅ 临时移除全局IPC调试代码，排查序列化问题
// const originalSend = ipcRenderer.send;
// ipcRenderer.send = function(channel: string, ...args: any[]) {
//   console.log(`[IPC-DEBUG] Sending to channel: ${channel}`, args);
//   return originalSend.call(this, channel, ...args);
// };

import AutoComplete from '@electron/preload/models/auto-complete';
import {ELECTRON_ERROR_PROTOCOL, ELECTRON_WEBUI_BASE_URL, getTheme} from '../preload-deps';
import {injectChromeWebstoreInstallButton} from './chrome-webstore';

const tabId = ipcRenderer.sendSync('get-webcontents-id');

export const windowId: number = ipcRenderer.sendSync('get-window-id');

const goBack = () => {
  ipcRenderer.invoke(`web-contents-call`, {
    webContentsId: tabId,
    method: 'goBack',
  });
};

const goForward = () => {
  ipcRenderer.invoke(`web-contents-call`, {
    webContentsId: tabId,
    method: 'goForward',
  });
};

window.addEventListener('mouseup', (e) => {
  if (e.button === 3) {
    e.preventDefault();
    goBack();
  } else if (e.button === 4) {
    e.preventDefault();
    goForward();
  }
});

let beginningScrollLeft: number = null;
let beginningScrollRight: number = null;
let horizontalMouseMove = 0;
let verticalMouseMove = 0;

const resetCounters = () => {
  beginningScrollLeft = null;
  beginningScrollRight = null;
  horizontalMouseMove = 0;
  verticalMouseMove = 0;
};

function getScrollStartPoint(x: number, y: number) {
  let left = 0;
  let right = 0;

  let n = document.elementFromPoint(x, y);

  while (n) {
    if (n.scrollLeft !== undefined) {
      left = Math.max(left, n.scrollLeft);
      right = Math.max(right, n.scrollWidth - n.clientWidth - n.scrollLeft);
    }
    n = n.parentElement;
  }
  return {left, right};
}

document.addEventListener('wheel', (e) => {
  verticalMouseMove += e.deltaY;
  horizontalMouseMove += e.deltaX;

  if (beginningScrollLeft === null || beginningScrollRight === null) {
    const result = getScrollStartPoint(e.deltaX, e.deltaY);
    beginningScrollLeft = result.left;
    beginningScrollRight = result.right;
  }
});

ipcRenderer.on('scroll-touch-end', () => {
  if (
    horizontalMouseMove - beginningScrollRight > 150 &&
    Math.abs(horizontalMouseMove / verticalMouseMove) > 2.5
  ) {
    if (beginningScrollRight < 10) {
      goForward();
    }
  }

  if (
    horizontalMouseMove + beginningScrollLeft < -150 &&
    Math.abs(horizontalMouseMove / verticalMouseMove) > 2.5
  ) {
    if (beginningScrollLeft < 10) {
      goBack();
    }
  }

  resetCounters();
});

if (process.env.ENABLE_AUTOFILL) {
  window.addEventListener('load', AutoComplete.loadForms);
  window.addEventListener('mousedown', AutoComplete.onWindowMouseDown);
}

const postMsg = (data: any, res: any) => {
  window.postMessage(
    {
      id: data.id,
      result: res,
      type: 'result',
    },
    '*',
  );
};

let hostname = window.location.href.substr(ELECTRON_WEBUI_BASE_URL.length);
window.addEventListener('DOMContentLoaded', () => {
  if (
    process.env.ENABLE_EXTENSIONS &&
    window.location.href.startsWith("https://microsoftedge.microsoft.com/addons")
  ) {
    injectChromeWebstoreInstallButton();
  }
});

const settings = ipcRenderer.sendSync('get-settings-sync');
//console.log("preload", window.location.href, hostname);
if (window.location.href == "about:blank") {
  hostname = "newtab";
}
if (
  window.location.href.startsWith(ELECTRON_WEBUI_BASE_URL) ||
  window.location.protocol === `${ELECTRON_ERROR_PROTOCOL}:` ||
  window.location.href == "about:blank"
) {
  (async function () {
    const w = await webFrame.executeJavaScript('window');
    w.settings = settings;
    w.require = (id: string) => {
      if (id === 'electron') {
        return {ipcRenderer};
      }
      return undefined;
    };
    w['xiu-app-version'] = process.env.VERSION_CODE;

    if (window.location.pathname.startsWith('//network-error')) {
      w.theme = getTheme(w.settings.theme);
      w.errorURL = await ipcRenderer.invoke(`get-error-url-${tabId}`);
    } else {
      w.getHistory = async () => {
        try {
          return await ipcRenderer.invoke(`history-get`);
        } catch (error) {
          console.error('[Preload] Error getting history:', error);
          return [];
        }
      };
      w.removeHistory = (ids: string[]) => {
        try {
          // ✅ 重构: 确保参数序列化，避免IPC序列化错误 (修复根本问题)
          const serializedIds = Array.isArray(ids) ? ids.map(id => String(id)) : [];
          ipcRenderer.send(`history-remove`, serializedIds);
        } catch (error) {
          console.error('[Preload] Error removing history:', error);
        }
      };
      w.getTopSites = async (count: number) => {
        try {
          // ✅ 重构: 确保参数序列化，避免IPC序列化错误 (修复根本问题)
          const serializedCount = Number(count) || 8;
          return await ipcRenderer.invoke(`topsites-get`, serializedCount);
        } catch (error) {
          console.error('[Preload] Error getting top sites:', error);
          return [];
        }
      };
    }
    if (window.location.href == "about:blank" && settings.doNotTrack) {
      const w = await webFrame.executeJavaScript('window');
      Object.defineProperty(w.navigator, 'doNotTrack', {value: 1});
    }
  })();
} else {
  (async function () {
    if (settings.doNotTrack) {
      const w = await webFrame.executeJavaScript('window');
      Object.defineProperty(w.navigator, 'doNotTrack', {value: 1});
    }
  })();
}

if (window.location.href.startsWith(ELECTRON_WEBUI_BASE_URL) ||
  window.location.href == "about:blank") {
  window.addEventListener('DOMContentLoaded', () => {
    hostname = window.location.href.substr(ELECTRON_WEBUI_BASE_URL.length);
    if (hostname.startsWith('settings')) document.title = '设置';
    else if (hostname.startsWith('history')) document.title = '历史记录';
    else if (hostname.startsWith('bookmarks')) document.title = '书签';
    else if (hostname.startsWith('extensions')) document.title = '扩展程序';
    else if (hostname.startsWith('newtab')) {
      document.title = '新标签页';
    }
  });

  // ✅ 重构: 恢复必要的postMessage处理，特别是save-settings (修复设置保存问题)
  window.addEventListener('message', async ({data}) => {
    if (data.type === 'save-settings') {
      try {
        // 确保数据已经是字符串格式（从设置页面发送过来的）
        let settingsData;
        if (typeof data.data === 'string') {
          settingsData = JSON.parse(data.data);
        } else {
          settingsData = data.data;
        }

        console.log('[Preload] Saving settings via IPC:', settingsData);
        const result = await ipcRenderer.invoke('save-settings', settingsData);
        console.log('[Preload] Settings save result:', result);
      } catch (error) {
        console.error('[Preload] Error processing save-settings message:', error);
      }
    } else if (data.type === 'show-adblock-rule') {
      ipcRenderer.send(`show-adblock-rule-${windowId}`, data.buttonPosition);
    } else if (data.type === 'storage') {
      // ✅ 重构: 恢复storage处理器，WebUI页面依赖此机制 (修复序列化错误)
      try {
        // 序列化数据，确保IPC通信安全
        const serializedData = data.data ? JSON.parse(JSON.stringify(data.data)) : {};
        const res = await ipcRenderer.invoke(`storage-${data.operation}`, {
          scope: data.scope,
          ...serializedData,
        });
        postMsg(data, res);
      } catch (error) {
        console.error('[Preload] Error processing storage message:', error);
        postMsg(data, null);
      }
    } else if (data.type === 'credentials-get-password') {
      // ✅ 重构: 恢复credentials处理器，WebUI页面可能依赖此机制 (修复序列化错误)
      try {
        const res = await ipcRenderer.invoke('credentials-get-password', data.data);
        postMsg(data, res);
      } catch (error) {
        console.error('[Preload] Error processing credentials message:', error);
        postMsg(data, null);
      }
    }
  });

  ipcRenderer.on('update-settings', async (e, data) => {
    const w = await webFrame.executeJavaScript('window');
    if (w.updateSettings) {
      w.updateSettings(data);
    }
  });

  ipcRenderer.on('credentials-insert', (e, data) => {
    window.postMessage(
      {
        type: 'credentials-insert',
        data,
      },
      '*',
    );
  });

  ipcRenderer.on('credentials-update', (e, data) => {
    window.postMessage(
      {
        type: 'credentials-update',
        data,
      },
      '*',
    );
  });

  ipcRenderer.on('credentials-remove', (e, data) => {
    window.postMessage(
      {
        type: 'credentials-remove',
        data,
      },
      '*',
    );
  });
  if(window.location.href == "about:blank") {
    window.addEventListener('message', async ({data}) => {
      if (data.type === 'xiu-video-created') {
        //console.log("xiu-video-created message", data);
        const tabId = ipcRenderer.sendSync('get-webcontents-id');
        ipcRenderer.send(`xiu-video-created-${tabId}`, {
          url: data.src,
        });
      }
    });
  }
} else {
  window.addEventListener('message', async ({data}) => {
    if (data.type === 'xiu-video-created') {
      //console.log("xiu-video-created message", data);
      const tabId = ipcRenderer.sendSync('get-webcontents-id');
      ipcRenderer.send(`xiu-video-created-${tabId}`, {
        url: data.src,
      });
    }
  });
}
