import { RendererToMainChannel } from '@wexond/rpc-electron';

export interface ExtensionMainService {
  uninstall(id: string): void;
  inspectBackgroundPage(id: string): void;
}

let _extensionMainChannel: RendererToMainChannel<ExtensionMainService>;

export const getExtensionMainChannel = () => {
  if (!_extensionMainChannel) {
    _extensionMainChannel = new RendererToMainChannel<ExtensionMainService>(
      'ExtensionMainService',
    );
  }
  return _extensionMainChannel;
};

// 为了向后兼容
export const extensionMainChannel = {
  getReceiver: () => getExtensionMainChannel().getReceiver(),
};
