import * as React from 'react';
import { observer } from 'mobx-react-lite';
import { ToolbarButton } from '../ToolbarButton';
import { IBrowserAction } from '../../models';
import { eventUtils } from '@browser/core/utils/platform-lite';
import store from '../../store';
import { extensionUtils, menuUtils } from '@browser/core/utils/platform-lite';

interface Props {
  data: IBrowserAction;
}

const showPopup = (
  data: IBrowserAction,
  left: number,
  top: number,
  devtools: boolean,
) => {
  store.extensions.currentlyToggledPopup = data.extensionId;
  eventUtils.send(
    `show-extension-popup-${store.windowId}`,
    left,
    top,
    data.popup,
    devtools,
    data.extensionId
  );
};

let canOpenPopup = true;

const onClick = (data: IBrowserAction) => (
  e: React.MouseEvent<HTMLDivElement>,
) => {
  if (data.tabId) {
    // TODO:
    //extensionsRenderer.browserAction.onClicked(data.extensionId, data.tabId);
  }

  if (canOpenPopup) {
    if(!data.popup) {
      onContextMenu(data)(e);
    } else {
      const { left, top } = e.currentTarget.getBoundingClientRect();
      showPopup(data, left, top, false);
    }
  }
};

const onContextMenu = (data: IBrowserAction) => (
  e: React.MouseEvent<HTMLDivElement>,
) => {
  const { target } = e;
  const menu = menuUtils.showContextMenu([
    {
      label: '卸载扩展',
      click: () => {
        store.extensions.uninstallExtension(data.extensionId);
      },
    },
    {
      label: '检查',
      click: () => {
        const { left, top } = (target as any).getBoundingClientRect();
        showPopup(data, left, top, true);
      },
    },
    {
      label: '检查后台页面',
      click: () => {
        extensionUtils
          .getInvoker()
          .inspectBackgroundPage(data.extensionId);
      },
    },
  ]);

  menu.popup();
};

const onMouseDown = (data: IBrowserAction) => async (e: any) => {
  canOpenPopup =
    !store.dialogsVisibility['extension-popup'] ||
    data.extensionId !== store.extensions.currentlyToggledPopup;
  // eventUtils.send(`hide-extension-popup-${store.windowId}`);
};

export const BrowserAction = observer(({ data }: Props) => {
  const {
    icon,
    badgeText,
    badgeBackgroundColor,
    badgeTextColor,
    extensionId,
  } = data;

  // StyledBrowserAction 样式 - Tailwind 版本 (简单的相对定位容器)
  return (
    <div className="relative">
      <ToolbarButton
        onClick={onClick(data)}
        onMouseDown={onMouseDown(data)}
        onContextMenu={onContextMenu(data)}
        opacity={1}
        autoInvert={false}
        size={16}
        toggled={
          store.dialogsVisibility['extension-popup'] &&
          store.extensions.currentlyToggledPopup === extensionId
        }
        icon={icon}
        badge={badgeText.trim() !== ''}
        badgeBackground={badgeBackgroundColor}
        badgeTextColor={badgeTextColor}
        badgeText={badgeText}
      ></ToolbarButton>
    </div>
  );
});
