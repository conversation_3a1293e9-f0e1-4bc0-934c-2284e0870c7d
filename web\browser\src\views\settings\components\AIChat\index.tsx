import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { Title, Row, Control, Header } from '../App/style';
import { Button } from '@browser/core/components/Button';
import { Textfield } from '@browser/core/components/Textfield';
import { Dropdown } from '@browser/core/components/Dropdown';
import store from '../../store';
import { ipcRenderer } from 'electron';
import { AVAILABLE_MODELS } from '../../../chat/services/aliyun';

const onSave = () => {
  store.save();
};

const onTestConnection = async () => {
  const apiKey = store.settings.aliyunApiKey;
  const endpoint = store.settings.aliyunEndpoint;
  
  if (!apiKey) {
    alert('请先输入API Key');
    return;
  }
  
  try {
    // 测试API连接
    const response = await fetch(endpoint || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bear<PERSON> ${apiKey}`,
        'X-DashScope-SSE': 'disable'
      },
      body: JSON.stringify({
        model: store.settings.chatModel || 'qwen-plus',
        input: {
          messages: [
            {
              role: 'user',
              content: '你好'
            }
          ]
        },
        parameters: {
          temperature: 0.7,
          max_tokens: 10,
          result_format: 'message'
        }
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      if (data.code) {
        alert(`连接失败: ${data.code} - ${data.message}`);
      } else {
        alert('连接成功！API配置正确。');
      }
    } else {
      alert(`连接失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    alert(`连接失败: ${error.message}`);
  }
};

export const AIChat = observer(() => {
  return (
    <>
      <Header>AI 对话设置</Header>
      
      <Row>
        <Title>阿里云 API Key</Title>
        <Control>
          <Textfield
            placeholder="请输入阿里云DashScope API Key"
            value={store.settings.aliyunApiKey || ''}
            style={{ width: '100%' }}
            onChange={(e: any) => {
              store.settings.aliyunApiKey = e.target.value;
            }}
            onKeyDown={(e: any) => {
              if (e.key === 'Enter') {
                onSave();
              }
            }}
          />
          <div style={{ 
            fontSize: '12px', 
            color: 'var(--mario-page-text)', 
            opacity: 0.7, 
            marginTop: '4px' 
          }}>
            在 <a 
              href="https://dashscope.console.aliyun.com/apiKey" 
              target="_blank" 
              rel="noopener noreferrer"
              style={{ color: 'var(--mario-accent)' }}
            >
              阿里云控制台
            </a> 获取API Key
          </div>
        </Control>
      </Row>

      <Row>
        <Title>API 端点</Title>
        <Control>
          <Textfield
            placeholder="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
            value={store.settings.aliyunEndpoint || ''}
            style={{ width: '100%' }}
            onChange={(e: any) => {
              store.settings.aliyunEndpoint = e.target.value;
            }}
            onKeyDown={(e: any) => {
              if (e.key === 'Enter') {
                onSave();
              }
            }}
          />
          <div style={{ 
            fontSize: '12px', 
            color: 'var(--mario-page-text)', 
            opacity: 0.7, 
            marginTop: '4px' 
          }}>
            留空使用默认端点
          </div>
        </Control>
      </Row>

      <Row>
        <Title>默认模型</Title>
        <Control>
          <Dropdown
            value={store.settings.chatModel || 'qwen-plus'}
            onChange={(value: string) => {
              store.settings.chatModel = value;
              onSave();
            }}
            style={{ width: '200px' }}
          >
            {AVAILABLE_MODELS.map((model) => (
              <option key={model} value={model}>
                {model === 'qwen-plus' && 'Qwen Plus (推荐)'}
                {model === 'qwen-turbo' && 'Qwen Turbo (快速)'}
                {model === 'qwen-max' && 'Qwen Max (最强)'}
              </option>
            ))}
          </Dropdown>
        </Control>
      </Row>

      <Row>
        <Title>连接测试</Title>
        <Control>
          <Button
            onClick={onTestConnection}
            disabled={!store.settings.aliyunApiKey}
            style={{ marginRight: '8px' }}
          >
            测试连接
          </Button>
          <Button onClick={onSave}>
            保存设置
          </Button>
        </Control>
      </Row>

      <Row>
        <div style={{ 
          padding: '16px', 
          backgroundColor: 'var(--mario-dialog)', 
          border: '1px solid var(--mario-border)', 
          borderRadius: '8px',
          fontSize: '14px',
          color: 'var(--mario-page-text)'
        }}>
          <h4 style={{ margin: '0 0 8px 0', color: 'var(--mario-page-text)' }}>使用说明：</h4>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>需要阿里云账号并开通DashScope服务</li>
            <li>在阿里云控制台创建API Key</li>
            <li>配置完成后可在Chat页面使用AI对话功能</li>
            <li>支持流式对话和多轮对话</li>
          </ul>
        </div>
      </Row>
    </>
  );
});
