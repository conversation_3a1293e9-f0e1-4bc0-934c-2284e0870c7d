import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../../store';
import { IFormFillData } from '@mario-ai/shared';
import { Section, onMoreClick } from '../Section';
import { eventUtils } from '@browser/core/utils/platform-lite';
import { cn } from '@browser/utils/tailwind-helpers';
import { ICON_VISIBLE, ICON_INVISIBLE, ICON_MORE, transparency } from '@mario-ai/shared';
import { ICON_KEY } from '@mario-ai/shared';

const Item = observer(({ data }: { data: IFormFillData }) => {
  const { url, favicon, fields } = data;
  const [realPassword, setRealPassword] = React.useState<string>(null);

  const password = realPassword || '•'.repeat(fields.passLength);

  const onIconClick = async () => {
    // 轻量级密码获取 - 在electron环境中通过IPC获取，浏览器环境中使用Mock
    if (!realPassword) {
      try {
        // 使用eventUtils调用后端获取密码
        const pass = await eventUtils.invoke('get-user-password', data);
        setRealPassword(pass);
      } catch (error) {
        console.log('[Lite] Get user password:', data);
        setRealPassword('mock-password'); // 浏览器环境Mock
      }
    }
  };

  // TODO(xnerhu): favicons

  // Container 样式 - Tailwind 版本 (对应原始的 Wrapper)
  const containerClasses = cn(
    'w-full flex items-center h-12 px-4 border-b border-black/8',
    'hover:bg-black/4 transition-colors duration-200'
  );

  // Label 样式 - Tailwind 版本
  const labelClasses = cn(
    'flex-1 text-sm overflow-hidden text-ellipsis whitespace-nowrap'
  );

  // PasswordIcon 样式 - Tailwind 版本
  const passwordIconClasses = cn(
    'w-6 h-6 cursor-pointer bg-center bg-no-repeat bg-contain mr-2',
    'transition-opacity duration-200 hover:opacity-80',
    `opacity-[${transparency.icons.inactive}]`,
    // 过滤器根据主题
    store.theme['pages.lightForeground'] ? 'invert' : ''
  );

  // More 样式 - Tailwind 版本
  const moreClasses = cn(
    'w-6 h-6 cursor-pointer bg-center bg-no-repeat bg-contain',
    'transition-opacity duration-200 hover:opacity-80',
    `opacity-[${transparency.icons.inactive}]`,
    // 过滤器根据主题
    store.theme['pages.lightForeground'] ? 'invert' : ''
  );

  return (
    <div className={containerClasses}>
      {/* 网站列 */}
      <div className={labelClasses}>
        {/* <Icon icon={store.favicons.favicons.get(favicon)} /> */}
        <span style={{ marginLeft: 12 }}>{url}</span>
      </div>
      {/* 用户名列 */}
      <div className={labelClasses}>{fields.username}</div>
      {/* 密码列 */}
      <div className={labelClasses}>{password}</div>
      {/* 密码显示/隐藏按钮 */}
      <div
        className={passwordIconClasses}
        style={{
          backgroundImage: `url(${realPassword ? ICON_INVISIBLE : ICON_VISIBLE})`,
          backgroundSize: '16px'
        }}
        onClick={onIconClick}
      ></div>
      {/* 更多按钮 */}
      <div
        className={moreClasses}
        style={{
          backgroundImage: `url(${ICON_MORE})`,
          backgroundSize: '16px'
        }}
        onClick={onMoreClick(data)}
      ></div>
    </div>
  );
});

export const Passwords = observer(() => {
  // Container 样式 - Tailwind 版本
  const containerClasses = cn(
    'w-full'
  );

  // HeaderLabel 样式 - Tailwind 版本
  const headerClasses = cn(
    'flex items-center h-12 px-4 bg-black/2 border-b border-black/8'
  );

  const headerLabelClasses = cn(
    'flex-1 text-sm font-medium'
  );

  return (
    <Section label="网站密码" icon={ICON_KEY}>
      <div className={containerClasses}>
        <div className={headerClasses}>
          <div className={headerLabelClasses}>网站</div>
          <div className={headerLabelClasses}>用户名</div>
          <div className={headerLabelClasses}>密码</div>
          <div className="w-6"></div> {/* 密码图标占位 */}
          <div className="w-6"></div> {/* 更多按钮占位 */}
        </div>
        {store.autoFill.credentials.map((item) => (
          <Item key={item._id} data={item} />
        ))}
      </div>
    </Section>
  );
});
