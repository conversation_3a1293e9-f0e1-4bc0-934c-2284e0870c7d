import {BrowserWindow} from 'electron';
import {Application} from '@electron/main/core/application';
import {DIALOG_MARGIN_TOP, DIALOG_MARGIN} from '@electron/renderer/constants/design';
import {IBookmark} from '@electron/types';

export const showBlockDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
  data: {
    data: [{
      url: string;
      filter: string;
    }],
    count: number
  },
) => {
  console.log('[Main] showBlockDialog called with:', x, y, data);
  console.log('[Main] Application.instance:', !!Application.instance);
  console.log('[Main] Application.instance.dialogs:', !!Application.instance?.dialogs);

  let dialog: any = null;

  dialog = Application.instance.dialogs.show({
    name: 'show-block',
    browserWindow,
    getBounds: () => ({
      width: 466,
      height: 400, // 设置合理的高度
      x: x - 466 + DIALOG_MARGIN,
      y: y - DIALOG_MARGIN_TOP,
    }),
    onWindowBoundsUpdate: () => {
      // 现在可以安全地引用 dialog
      if (dialog) {
        dialog.hide();
      }
    },
  });

  console.log('[Main] Block dialog created:', dialog ? 'success' : 'failed');

  if (!dialog) return;

  dialog.on('loaded', (e) => {
    // 确保数据是可序列化的
    const serializedData = {
      data: data.data || [],
      count: data.count || 0
    };
    e.reply('data', serializedData);
  });
};
