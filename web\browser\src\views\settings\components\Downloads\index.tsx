import * as React from 'react';

import { Switch } from '@browser/core/components/Switch';
import { Title, Row, Control, Header, SecondaryText } from '../shared-styles';
import store from '../../store';
import { onSwitchChange } from '../../utils';
import { eventUtils } from '@browser/core/utils/platform-lite';
import { observer } from 'mobx-react-lite';
import { NormalButton } from '../App';

const AskToggle = observer(() => {
  const { downloadsDialog } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('downloadsDialog')}>
      <Title>下载前询问</Title>
      <Control>
        <Switch value={downloadsDialog} />
      </Control>
    </Row>
  );
});

const onChangeClick = async () => {
  try {
    const result = await eventUtils.invoke('downloads-path-change');
    if (result.success && result.path) {
      console.log('[Downloads] Path updated successfully:', result.path);
      // 直接更新设置 Store
      store.updateDownloadsPath(result.path);
    } else {
      console.log('[Downloads] Path update cancelled or failed');
    }
  } catch (error) {
    console.error('[Downloads] Error updating path:', error);
  }
};

const Location = observer(() => {
  return (
    <Row theme={store.theme}>
      <div>
        <Title>下载位置</Title>
        <SecondaryText>{store.settings.downloadsPath}</SecondaryText>
      </div>

      <Control>
        <NormalButton onClick={onChangeClick}>修改</NormalButton>
      </Control>
    </Row>
  );
});

export const Downloads = () => {
  return (
    <>
      <Header>下载</Header>
      <Location />
      <AskToggle />
    </>
  );
};
