import React, { useEffect, useState } from 'react';
import { render } from 'react-dom';
import ChatInterface from './components/ChatInterface';
import { ChatHistoryProvider } from './hooks/useChatHistory';
import { WebUIStyle } from '@browser/core/styles/default-styles';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';
import { getTheme } from '@browser/core/utils/themes';
import { ITheme } from '@mario-ai/shared';

// 映射主题名称到完整名称
const getFullThemeName = (name: string) => {
  switch (name) {
    case 'futuristic':
      return 'mario-futuristic';
    case 'wexond-dark':
      return 'wexond-dark';
    case 'wexond-light':
      return 'wexond-light';
    default:
      return 'mario-futuristic';
  }
};

const ChatPage: React.FC = () => {
  // 立即获取初始主题，不要等useEffect
  const initialThemeName = TailwindThemeManager.getCurrentTheme();
  const initialFullThemeName = getFullThemeName(initialThemeName);
  const initialThemeConfig = getTheme(initialFullThemeName);

  const [currentTheme, setCurrentTheme] = useState<string>(initialThemeName);
  const [theme, setTheme] = useState<ITheme | null>(initialThemeConfig);

  console.log('[Chat] Initial theme setup:', {
    initialThemeName,
    initialFullThemeName,
    initialThemeConfig: initialThemeConfig ? 'loaded' : 'null',
    themeState: theme ? 'loaded' : 'null'
  });

  useEffect(() => {
    // 强制设置主题到DOM
    TailwindThemeManager.setTheme(initialThemeName as any);

    // 确保主题已经设置
    if (!theme) {
      const themeName = TailwindThemeManager.getCurrentTheme();
      const fullThemeName = getFullThemeName(themeName);
      const themeConfig = getTheme(fullThemeName);
      setTheme(themeConfig);
      setCurrentTheme(themeName);

      console.log('[Chat] Theme fallback setup:', {
        themeName,
        fullThemeName,
        themeConfig: themeConfig ? 'loaded' : 'null'
      });
    }

    // 监听主题变化
    const handleThemeChange = (event: CustomEvent) => {
      const newThemeName = event.detail.theme;
      setCurrentTheme(newThemeName);

      // 更新主题配置
      const newFullThemeName = getFullThemeName(newThemeName);
      const newThemeConfig = getTheme(newFullThemeName);
      setTheme(newThemeConfig);

      console.log('[Chat] Theme changed:', {
        newThemeName,
        newFullThemeName,
        newThemeConfig: newThemeConfig ? 'loaded' : 'null'
      });
    };

    // 添加主题变化监听器
    window.addEventListener('theme-changed', handleThemeChange as EventListener);

    return () => {
      // 清理监听器
      window.removeEventListener('theme-changed', handleThemeChange as EventListener);
    };
  }, [theme]); // 依赖theme，确保在theme变化时重新设置监听器

  // 渲染前的最终检查
  console.log('[Chat] Rendering with theme:', {
    theme: theme ? 'loaded' : 'null',
    themeType: typeof theme,
    themeKeys: theme ? Object.keys(theme) : 'none'
  });

  return (
    <ChatHistoryProvider>
      <WebUIStyle theme={theme || undefined} />
      <div className="h-screen w-screen overflow-hidden bg-mario-page text-mario-page-text">
        <ChatInterface />
      </div>
    </ChatHistoryProvider>
  );
};

// 渲染到DOM (React 17 方式)
const container = document.getElementById('root');
if (container) {
  render(<ChatPage />, container);
}

export default ChatPage;
