import React, { useEffect, useState } from 'react';
import { render } from 'react-dom';
import ChatInterface from './components/ChatInterface';
import { ChatHistoryProvider } from './hooks/useChatHistory';
import { WebUIStyle } from '@browser/core/styles/default-styles';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';

const ChatPage: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState<string>('futuristic');

  useEffect(() => {
    // 获取当前主题
    const theme = TailwindThemeManager.getCurrentTheme();
    setCurrentTheme(theme);

    // 监听主题变化
    const handleThemeChange = (event: CustomEvent) => {
      setCurrentTheme(event.detail.theme);
    };

    // 添加主题变化监听器
    window.addEventListener('theme-changed', handleThemeChange as EventListener);

    return () => {
      // 清理监听器
      window.removeEventListener('theme-changed', handleThemeChange as EventListener);
    };
  }, []);

  return (
    <ChatHistoryProvider>
      <WebUIStyle />
      <div className="h-screen w-screen overflow-hidden bg-mario-page text-mario-page-text">
        <ChatInterface />
      </div>
    </ChatHistoryProvider>
  );
};

// 渲染到DOM (React 17 方式)
const container = document.getElementById('root');
if (container) {
  render(<ChatPage />, container);
}

export default ChatPage;
