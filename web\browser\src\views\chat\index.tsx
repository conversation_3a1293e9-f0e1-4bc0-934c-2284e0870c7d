import React, { useEffect, useState } from 'react';
import { render } from 'react-dom';
import ChatInterface from './components/ChatInterface';
import { ChatHistoryProvider } from './hooks/useChatHistory';
import { WebUIStyle } from '@browser/core/styles/default-styles';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';
import { getTheme } from '@browser/core/utils/themes';
import { ITheme } from '@mario-ai/shared';

// 映射主题名称到完整名称
const getFullThemeName = (name: string) => {
  switch (name) {
    case 'futuristic':
      return 'mario-futuristic';
    case 'wexond-dark':
      return 'wexond-dark';
    case 'wexond-light':
      return 'wexond-light';
    default:
      return 'mario-futuristic';
  }
};

const ChatPage: React.FC = () => {
  // 立即获取初始主题，不要等useEffect
  const initialThemeName = TailwindThemeManager.getCurrentTheme();
  const initialFullThemeName = getFullThemeName(initialThemeName);
  const initialThemeConfig = getTheme(initialFullThemeName);

  const [currentTheme, setCurrentTheme] = useState<string>(initialThemeName);
  const [theme, setTheme] = useState<ITheme | null>(initialThemeConfig);
  const [themeInitialized, setThemeInitialized] = useState<boolean>(false);

  // 只在组件首次挂载时打印日志
  React.useEffect(() => {
    if (!themeInitialized) {
      console.log('[Chat] Initial theme setup:', {
        initialThemeName,
        initialFullThemeName,
        initialThemeConfig: initialThemeConfig ? 'loaded' : 'null',
        themeState: theme ? 'loaded' : 'null'
      });
      setThemeInitialized(true);
    }
  }, []);

  useEffect(() => {
    // 只在首次挂载时设置主题
    if (!themeInitialized) {
      return;
    }

    // 确保DOM已经准备好，然后设置主题
    const setupTheme = () => {
      // 检查当前DOM状态
      const currentDataTheme = document.documentElement.getAttribute('data-theme');

      // 如果已经设置了正确的主题，就不要重复设置
      if (currentDataTheme === 'futuristic') {
        console.log('[Chat] Theme already set correctly:', currentDataTheme);
        return;
      }

      console.log('[Chat] Setting up theme in DOM...');

      // 手动设置DOM属性，避免触发事件循环
      document.documentElement.setAttribute('data-theme', 'futuristic');

      // 验证设置是否成功
      const dataTheme = document.documentElement.getAttribute('data-theme');
      console.log('[Chat] Theme setup result:', {
        requested: 'futuristic',
        actualDataTheme: dataTheme,
        success: dataTheme === 'futuristic'
      });
    };

    // 立即执行一次
    setupTheme();

    // 监听主题变化（但不要在初始化时触发）
    const handleThemeChange = (event: CustomEvent) => {
      const newThemeName = event.detail.theme;

      // 避免重复设置相同主题
      if (newThemeName === currentTheme) {
        return;
      }

      setCurrentTheme(newThemeName);

      // 更新主题配置
      const newFullThemeName = getFullThemeName(newThemeName);
      const newThemeConfig = getTheme(newFullThemeName);
      setTheme(newThemeConfig);

      console.log('[Chat] Theme changed:', {
        newThemeName,
        newFullThemeName,
        newThemeConfig: newThemeConfig ? 'loaded' : 'null'
      });
    };

    // 添加主题变化监听器
    window.addEventListener('theme-changed', handleThemeChange as EventListener);

    return () => {
      // 清理监听器
      window.removeEventListener('theme-changed', handleThemeChange as EventListener);
    };
  }, [themeInitialized, currentTheme]); // 依赖themeInitialized和currentTheme

  // 只在首次渲染或主题变化时打印日志
  React.useEffect(() => {
    if (themeInitialized) {
      console.log('[Chat] Rendering with theme:', {
        theme: theme ? 'loaded' : 'null',
        currentDataTheme: document.documentElement.getAttribute('data-theme')
      });
    }
  }, [theme, themeInitialized]);

  return (
    <ChatHistoryProvider>
      <WebUIStyle theme={theme || undefined} />
      <div className="h-screen w-screen overflow-hidden bg-mario-page text-mario-page-text">
        <ChatInterface />
      </div>
    </ChatHistoryProvider>
  );
};

// 渲染到DOM (React 17 方式)
const container = document.getElementById('root');
if (container) {
  render(<ChatPage />, container);
}

export default ChatPage;
