import React, { useEffect, useState } from 'react';
import { render } from 'react-dom';
import ChatInterface from './components/ChatInterface';
import { ChatHistoryProvider } from './hooks/useChatHistory';
import { WebUIStyle } from '@browser/core/styles/default-styles';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';
import { ITheme } from '@mario-ai/shared';

const ChatPage: React.FC = () => {
  const [theme, setTheme] = useState<ITheme | null>(null);

  useEffect(() => {
    // 初始化主题管理器
    const themeManager = TailwindThemeManager.getInstance();

    // 获取当前主题
    const currentTheme = themeManager.getCurrentTheme();
    setTheme(currentTheme);

    // 监听主题变化
    const handleThemeChange = (newTheme: ITheme) => {
      setTheme(newTheme);
    };

    // 这里可以添加主题变化监听器
    // themeManager.on('themeChanged', handleThemeChange);

    return () => {
      // 清理监听器
      // themeManager.off('themeChanged', handleThemeChange);
    };
  }, []);

  return (
    <ChatHistoryProvider>
      <WebUIStyle theme={theme} />
      <div className="h-screen w-screen overflow-hidden bg-mario-page text-mario-page-text">
        <ChatInterface />
      </div>
    </ChatHistoryProvider>
  );
};

// 渲染到DOM (React 17 方式)
const container = document.getElementById('root');
if (container) {
  render(<ChatPage />, container);
}

export default ChatPage;
