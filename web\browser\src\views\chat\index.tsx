import React, { useEffect, useState } from 'react';
import { render } from 'react-dom';
import ChatInterface from './components/ChatInterface';
import { ChatHistoryProvider } from './hooks/useChatHistory';
import { WebUIStyle } from '@browser/core/styles/default-styles';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';
import { getTheme } from '@browser/core/utils/themes';
import { ITheme } from '@mario-ai/shared';

const ChatPage: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState<string>('futuristic');
  const [theme, setTheme] = useState<ITheme | null>(null);

  useEffect(() => {
    // 获取当前主题
    const themeName = TailwindThemeManager.getCurrentTheme();
    setCurrentTheme(themeName);

    // 获取主题配置对象
    const themeConfig = getTheme(themeName);
    setTheme(themeConfig);

    // 监听主题变化
    const handleThemeChange = (event: CustomEvent) => {
      const newThemeName = event.detail.theme;
      setCurrentTheme(newThemeName);

      // 更新主题配置
      const newThemeConfig = getTheme(newThemeName);
      setTheme(newThemeConfig);
    };

    // 添加主题变化监听器
    window.addEventListener('theme-changed', handleThemeChange as EventListener);

    return () => {
      // 清理监听器
      window.removeEventListener('theme-changed', handleThemeChange as EventListener);
    };
  }, []);

  return (
    <ChatHistoryProvider>
      <WebUIStyle theme={theme || undefined} />
      <div className="h-screen w-screen overflow-hidden bg-mario-page text-mario-page-text">
        <ChatInterface />
      </div>
    </ChatHistoryProvider>
  );
};

// 渲染到DOM (React 17 方式)
const container = document.getElementById('root');
if (container) {
  render(<ChatPage />, container);
}

export default ChatPage;
