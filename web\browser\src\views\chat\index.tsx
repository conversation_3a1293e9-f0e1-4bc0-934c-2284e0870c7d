import React from 'react';
import { render } from 'react-dom';
import ChatInterface from '../../ai-modules/chat/components/ChatInterface';
import { ChatHistoryProvider } from '../../ai-modules/shared/contexts/ChatHistoryContext';

const ChatPage: React.FC = () => {
  return (
    <ChatHistoryProvider>
      <div className="h-full w-full">
        <ChatInterface />
      </div>
    </ChatHistoryProvider>
  );
};

// 渲染到DOM (React 17 方式)
const container = document.getElementById('root');
if (container) {
  render(<ChatPage />, container);
}

export default ChatPage;
