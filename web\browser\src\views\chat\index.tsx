import React, { useEffect } from 'react';
import { render } from 'react-dom';
import ChatInterface from './components/ChatInterface';
import { ChatHistoryProvider } from './hooks/useChatHistory';
import { WebUIStyle } from '@browser/core/styles/default-styles';
import { useOptimizedTheme } from '@browser/hooks/useOptimizedTheme';

const ChatPage: React.FC = () => {
  // 使用优化的主题Hook，确保主题正确初始化
  const { currentTheme, isDark, isInitialized, setTheme } = useOptimizedTheme();
  const [themeReady, setThemeReady] = React.useState(false);

  useEffect(() => {
    console.log('[Chat] Theme status:', {
      currentTheme,
      isDark,
      isInitialized,
      dataTheme: document.documentElement.getAttribute('data-theme')
    });

    // 如果主题系统已初始化但没有设置主题，设置默认主题
    if (isInitialized && !document.documentElement.getAttribute('data-theme')) {
      console.log('[Chat] Setting default theme...');
      setTheme('mario-futuristic');

      // 等待主题设置完成
      setTimeout(() => {
        const dataTheme = document.documentElement.getAttribute('data-theme');
        console.log('[Chat] Theme set result:', dataTheme);
        setThemeReady(true);
      }, 100);
    } else if (isInitialized && document.documentElement.getAttribute('data-theme')) {
      // 主题已经设置好了
      console.log('[Chat] Theme already set');
      setThemeReady(true);
    }
  }, [currentTheme, isDark, isInitialized, setTheme]);

  // 在主题准备好之前显示加载状态
  if (!themeReady) {
    return (
      <div style={{
        width: '100vw',
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#1a1a1a',
        color: '#ffffff'
      }}>
        <div>正在加载主题...</div>
      </div>
    );
  }

  return (
    <ChatHistoryProvider>
      <WebUIStyle />
      <div className="h-screen w-screen overflow-hidden bg-mario-page text-mario-page-text">
        <ChatInterface />
      </div>
    </ChatHistoryProvider>
  );
};

// 渲染到DOM (React 17 方式)
const container = document.getElementById('root');
if (container) {
  render(<ChatPage />, container);
}

export default ChatPage;
