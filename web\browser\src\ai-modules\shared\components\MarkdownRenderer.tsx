import React from 'react';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className }) => {
  // 简单的Markdown渲染器，后续可以替换为更完整的实现
  const renderContent = (text: string) => {
    // 基本的Markdown处理
    let html = text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // 斜体
      .replace(/`(.*?)`/g, '<code>$1</code>') // 行内代码
      .replace(/\n/g, '<br>'); // 换行

    return { __html: html };
  };

  return (
    <div 
      className={className}
      dangerouslySetInnerHTML={renderContent(content)}
    />
  );
};

export default MarkdownRenderer;
