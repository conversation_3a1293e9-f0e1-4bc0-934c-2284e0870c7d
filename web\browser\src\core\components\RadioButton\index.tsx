import * as React from 'react';
import { observer } from 'mobx-react-lite';
import { cn } from '@browser/utils/tailwind-helpers';
import { transparency, BLUE_500 } from '@mario-ai/shared';

interface Props {
  selected: boolean;
}

export const RadioButton = observer(({ selected }: Props) => {
  return (
    <div className={cn(
      'h-10 flex items-center justify-start cursor-pointer group'
    )}>
      <div className={cn(
        'm-[5px] cursor-pointer w-[18px] h-[18px] relative',
        // 伪元素样式
        'before:content-[""] before:rounded-full before:w-full before:h-full',
        'before:absolute before:top-0 before:box-border before:pointer-events-none before:z-0'
      )}>
        {/* 隐藏的 radio input */}
        <input
          type="radio"
          className={cn(
            'opacity-0 z-[2] absolute top-0 w-full h-full m-0 cursor-pointer',
            'focus:outline-none'
          )}
          defaultChecked={selected}
        />
        
        {/* 圆形指示器 */}
        <div className={cn(
          'rounded-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
          'transition-all duration-100 ease-in pointer-events-none z-[1]',
          // 选中状态的尺寸
          selected ? 'w-[calc(100%-8px)] h-[calc(100%-8px)]' : 'w-0 h-0',
          // 伪元素边框
          'radio-button-circle',
          'before:content-[""] before:w-[calc(20px-6px)] before:absolute before:h-[calc(20px-6px)]',
          'before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2',
          'before:border-2 before:rounded-full before:box-border'
        )}
        style={{
          backgroundColor: selected ? BLUE_500 : 'rgba(0, 0, 0, 0.54)',
          // 伪元素样式通过 CSS 变量传递
          '--before-border-color': selected 
            ? BLUE_500 
            : 'rgba(0, 0, 0, 0.54)', // 这里可以根据主题调整
          '--before-transition': selected ? 'opacity 0.5s ease' : 'none',
        } as React.CSSProperties & { 
          '--before-border-color': string;
          '--before-transition': string;
        }}
        />
        
        {/* Hover 效果 - 对应 Container 的 hover 样式 */}
        <div className={cn(
          'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
          'w-0 h-0 rounded-full opacity-0 pointer-events-none transition-all duration-100',
          'group-hover:w-9 group-hover:h-9 group-hover:opacity-8'
        )}
        style={{ backgroundColor: BLUE_500 }}
        />
      </div>
    </div>
  );
});

// 导出 Label 组件供其他组件使用
export const Label: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <div 
      className={cn('text-sm ml-2 font-roboto font-normal', className)}
      style={{ color: `rgba(0, 0, 0, ${transparency.text.high})` }}
    >
      {children}
    </div>
  );
};
