import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { transparency, ICON_SEARCH } from '@mario-ai/shared';

// NavigationDrawerItem 组件
interface NavigationDrawerItemProps {
  children: any;
  selected?: boolean;
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  icon?: string;
  iconOnly?: boolean;
}

const NavigationDrawerItem: React.FC<NavigationDrawerItemProps> = ({
  children,
  selected = false,
  onClick,
  icon,
  iconOnly = false
}) => {
  const itemClasses = cn(
    'flex rounded items-center relative cursor-pointer',
    // 确保文本不换行
    'whitespace-nowrap',
    // Hover 效果
    'hover:bg-black hover:bg-opacity-4 dark:hover:bg-white dark:hover:bg-opacity-6',
    // 选中状态的左侧指示器通过伪元素实现
    'before:content-[""] before:absolute before:left-0 before:rounded-[2px] before:w-[3px] before:h-[18px]',
    'before:bg-black dark:before:bg-white',
    selected ? 'before:opacity-100' : 'before:opacity-0',
    // iconOnly 模式下居中对齐
    iconOnly ? 'justify-center' : ''
  );

  // 确保菜单项有正确的尺寸
  const itemStyle: React.CSSProperties = {
    height: '40px', // h-10 = 40px
    padding: '4px 16px', // py-1 px-4
  };

  const iconClasses = cn(
    'h-6 w-6 min-w-6 bg-center bg-no-repeat',
    'filter-mario-icon', // 使用主题过滤器
    iconOnly ? '' : 'mr-4' // iconOnly 模式下不需要右边距
  );

  const iconStyle: React.CSSProperties = {
    opacity: transparency.icons.inactive,
    backgroundImage: icon ? `url(${icon})` : undefined,
    backgroundSize: '20px',
  };

  return (
    <div
      className={itemClasses}
      style={itemStyle}
      title={children}
      onClick={onClick}
    >
      {icon && (
        <div
          className={iconClasses}
          style={iconStyle}
        />
      )}
      {!iconOnly && children}
    </div>
  );
};

// 主 NavigationDrawer 组件
interface NavigationDrawerProps {
  children?: any;
  title?: string;
  search?: boolean;
  onSearchInput?: (event: React.FormEvent<HTMLInputElement>) => void;
  searchValue?: string;
  onBackClick?: (e?: React.MouseEvent<HTMLDivElement>) => void;
  style?: any;
  dense?: boolean;
  iconOnly?: boolean;
}

export const NavigationDrawer: React.FC<NavigationDrawerProps> & {
  Item: typeof NavigationDrawerItem;
} = ({
  children,
  title,
  search,
  onSearchInput,
  searchValue = '',
  style,
  dense = false,
  iconOnly = false
}) => {
  // 主容器样式 - 移除可能导致缩放的transition
  const containerClasses = cn(
    // 基础布局：高度、定位、flex 方向
    'h-full flex flex-col',
    // 关键修复：确保不收缩，固定宽度
    'flex-shrink-0',
    // 背景色根据 dense 模式
    dense ? 'bg-mario-nav-drawer1' : 'bg-mario-nav-drawer2'
  );

  // 动态样式 - 根据模式调整宽度
  const containerStyle: React.CSSProperties = {
    width: iconOnly ? '64px' : (dense ? '200px' : '320px'), // iconOnly 模式最窄
    padding: iconOnly ? '0 8px' : (dense ? '0 16px' : '0 32px'), // iconOnly 模式最小内边距
    ...style
  };

  // Header 样式
  const headerClasses = cn(
    'flex items-center'
  );

  // Header 动态样式
  const headerStyle: React.CSSProperties = {
    marginTop: '32px', // mt-8 = 32px
  };

  // Title 样式
  const titleClasses = cn(
    'font-light'
  );

  // Title 动态样式
  const titleStyle: React.CSSProperties = {
    fontSize: '24px', // text-2xl = 24px
  };

  // Search 容器样式
  const searchClasses = cn(
    'mt-6 h-[42px] rounded-[30px] relative search-container',
    // 搜索图标伪元素
    'after:content-[""] after:absolute after:left-4 after:top-1/2 after:-translate-y-1/2',
    'after:w-4 after:h-4 after:bg-center after:bg-no-repeat',
    'after:filter-mario-icon'
  );

  // 检查是否为深色主题
  const isDarkTheme = document.documentElement.getAttribute('data-theme') === 'dark';

  // 搜索框样式 - 图标和背景色
  const searchStyle: React.CSSProperties = {
    // 根据主题设置背景色 - 浅色主题用更明显的灰色
    backgroundColor: isDarkTheme
      ? 'rgba(255, 255, 255, 0.12)'  // 深色主题：白色半透明
      : 'rgba(0, 0, 0, 0.12)',       // 浅色主题：黑色半透明（比原来的0.04更明显）
    '--after-bg-image': `url(${ICON_SEARCH})`,
    '--after-bg-size': '16px',
  } as React.CSSProperties & {
    '--after-bg-image': string;
    '--after-bg-size': string;
  };

  // Input 样式
  const inputClasses = cn(
    'border-none outline-none w-full pl-[42px] bg-transparent h-full text-sm',
    'text-mario-page-text',
    'placeholder:text-mario-page-text placeholder:opacity-54'
  );

  // MenuItems 样式
  const menuItemsClasses = cn(
    'flex flex-col flex-1 pb-6 overflow-hidden overflow-y-auto',
    // 自定义滚动条样式
    'scrollbar-thin scrollbar-track-black scrollbar-track-opacity-4 scrollbar-thumb-black scrollbar-thumb-opacity-12',
    // iconOnly 模式下从顶部开始，否则有上边距
    iconOnly ? 'mt-4' : 'mt-6'
  );

  return (
    <div className={containerClasses} style={containerStyle}>
      {!iconOnly && title !== '' && (
        <div className={headerClasses} style={headerStyle}>
          <div className={titleClasses} style={titleStyle}>{title}</div>
        </div>
      )}

      {!iconOnly && search && (
        <div
          className={searchClasses}
          style={searchStyle}
        >
          <input
            className={inputClasses}
            placeholder="搜索"
            value={searchValue}
            onInput={onSearchInput}
            onChange={onSearchInput}
          />
        </div>
      )}
      
      <div className={menuItemsClasses}>
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child) && child.type === NavigationDrawerItem) {
            return React.cloneElement(child, { iconOnly } as any);
          }
          return child;
        })}
      </div>
    </div>
  );
};

// 添加 Item 属性
NavigationDrawer.Item = NavigationDrawerItem;
