import React, { useRef, useState } from 'react';
import { Button } from '../../../shared/components/Button';
import { cn } from '../../../shared/utils/cn';

interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
  className?: string;
}

interface UploadedFile {
  file: File;
  id: string;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

// File type validation
const IMAGE_TYPES = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
const DOCUMENT_TYPES = ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf', '.csv', '.xlsx', '.xls'];
const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

const validateFile = (file: File): { isValid: boolean; error?: string } => {
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();

  if (IMAGE_TYPES.includes(extension)) {
    if (file.size > MAX_IMAGE_SIZE) {
      return { isValid: false, error: `图片文件大小不能超过 ${MAX_IMAGE_SIZE / 1024 / 1024}MB` };
    }
  } else if (DOCUMENT_TYPES.includes(extension)) {
    if (file.size > MAX_FILE_SIZE) {
      return { isValid: false, error: `文档文件大小不能超过 ${MAX_FILE_SIZE / 1024 / 1024}MB` };
    }
  } else {
    return { isValid: false, error: '不支持的文件类型' };
  }

  return { isValid: true };
};

const ChatFileUpload: React.FC<FileUploadProps> = ({ onFilesSelected, className }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const newFiles: UploadedFile[] = [];
    const validFiles: File[] = [];

    Array.from(files).forEach((file) => {
      const validation = validateFile(file);
      const uploadedFile: UploadedFile = {
        file,
        id: `${Date.now()}-${Math.random()}`,
        status: validation.isValid ? 'success' : 'error',
        error: validation.error,
      };

      newFiles.push(uploadedFile);
      if (validation.isValid) {
        validFiles.push(file);
      }
    });

    setUploadedFiles(prev => [...prev, ...newFiles]);
    if (validFiles.length > 0) {
      onFilesSelected(validFiles);
    }
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId);
      const validFiles = updated
        .filter(f => f.status === 'success')
        .map(f => f.file);
      onFilesSelected(validFiles);
      return updated;
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const getFileIcon = (file: File) => {
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (IMAGE_TYPES.includes(extension)) {
      return <span className="text-xs">🖼️</span>;
    }
    return <span className="text-xs">📎</span>;
  };

  const getStatusIcon = (status: UploadedFile['status']) => {
    switch (status) {
      case 'success':
        return <span className="text-xs text-green-500">✓</span>;
      case 'error':
        return <span className="text-xs text-red-500">✗</span>;
      case 'uploading':
        return <div className="h-3 w-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return null;
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      {/* File Upload Area */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-3 transition-colors cursor-pointer",
          isDragOver
            ? "border-blue-400 bg-blue-50 dark:bg-blue-950/20"
            : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="flex items-center justify-center space-x-2 text-sm text-mario-page-text opacity-70">
          <span>📤</span>
          <span>点击或拖拽文件到此处</span>
        </div>
        <p className="text-xs text-mario-page-text opacity-50 text-center mt-1">
          支持图片 (JPG, PNG, GIF, WebP, SVG) 和文档 (PDF, DOC, TXT, MD 等)
        </p>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={[...IMAGE_TYPES, ...DOCUMENT_TYPES].join(',')}
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-1">
          {uploadedFiles.map((uploadedFile) => (
            <div
              key={uploadedFile.id}
              className={cn(
                "flex items-center justify-between p-2 rounded-md text-xs",
                uploadedFile.status === 'error'
                  ? "bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800"
                  : "bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
              )}
            >
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                {getFileIcon(uploadedFile.file)}
                {getStatusIcon(uploadedFile.status)}

                <span className="truncate max-w-[80px]" title={uploadedFile.file.name}>
                  {uploadedFile.file.name}
                </span>

                {uploadedFile.error && (
                  <span className="text-red-600 dark:text-red-400 text-xs">
                    {uploadedFile.error}
                  </span>
                )}
              </div>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  removeFile(uploadedFile.id);
                }}
                className="hover:bg-accent rounded-full p-0.5 transition-colors text-xs"
              >
                ✕
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChatFileUpload;
