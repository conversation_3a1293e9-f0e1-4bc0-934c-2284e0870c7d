import { observer } from 'mobx-react-lite';
import * as React from 'react';

import { cn } from '@browser/utils/tailwind-helpers';
import { Titlebar } from '../Titlebar';
import { Toolbar } from '../Toolbar';
import store from '../../store';
import { UIStyle } from '@browser/core/styles/default-styles';
import { BookmarkBar } from '../BookmarkBar';
import { useGlobalThemeInit } from '@browser/hooks/useOptimizedTheme';
import { AIToolbar } from '../AIToolbar';


const onAppLeave = () => {
  store.barHideTimer = setTimeout(function () {
    if (
      Object.keys(store.dialogsVisibility).some(
        (k) => store.dialogsVisibility[k],
      )
    ) {
      onAppLeave();
    } else {
      store.titlebarVisible = false;
    }
  }, 500);
};

const onAppEnter = () => {
  clearTimeout(store.barHideTimer);
};

const onLineEnter = () => {
  store.titlebarVisible = true;
};



const App = observer(() => {
  console.log('[App] Rendering App component');

  // 使用优化的主题管理 - 解决主题切换延迟问题
  const isThemeInitialized = useGlobalThemeInit(
    store.settings.object.theme || 'mario-futuristic',
    store.settings.object.themeAuto
  );

  // 初始化默认主题设置
  React.useEffect(() => {
    console.log('[App] Initializing theme system');

    // 如果没有设置主题，默认使用前卫主题
    if (!store.settings.object.theme || store.settings.object.theme === 'wexond-light') {
      console.log('[App] Setting default futuristic theme');
      store.settings.updateSettings({
        ...store.settings.object,
        theme: 'mario-futuristic',
        themeAuto: false
      });
    }
  }, []);

  // 初始化启动Tab - 确保有初始Tab被创建
  React.useEffect(() => {
    console.log('[App] Initializing startup tabs...');
    store.startupTabs.load().catch(error => {
      console.error('[App] Failed to load startup tabs:', error);
    });
  }, []);



  // StyledApp 样式 - Tailwind 版本
  // AI工具栏现在由React组件实现，宽度为w-16 (64px)
  const aiToolbarWidth = store.aiToolbarVisible && !store.isFullscreen ? 64 : 0;
  const appClasses = cn(
    'flex flex-col bg-white overflow-hidden'
  );

  // Line 样式 - Tailwind 版本
  const lineClasses = cn(
    'h-px w-full z-[100] relative bg-black'
  );

  const appStyle: React.CSSProperties = {
    // 在浏览器环境中，始终显示标题栏，不隐藏
    height: 'auto',
    // 为AI工具栏预留左侧空间
    marginLeft: `${aiToolbarWidth}px`,
  };

  const lineStyle: React.CSSProperties = {
    // 在浏览器环境中，不需要这个line
    height: 0,
    // 为AI工具栏预留左侧空间
    marginLeft: `${aiToolbarWidth}px`,
  };

  return (
    <>
      {/* AI工具栏 - React组件实现 */}
      {store.aiToolbarVisible && !store.isFullscreen && <AIToolbar />}

      {/* 主应用内容 */}
      <div
        className={appClasses}
        style={appStyle}
        onMouseOver={store.isFullscreen ? onAppEnter : undefined}
        onMouseLeave={store.isFullscreen ? onAppLeave : undefined}
      >
        <UIStyle />
        <Titlebar />
        {store.settings.object.topBarVariant === 'default' && <Toolbar />}
        <BookmarkBar />
      </div>

      <div
        className={lineClasses}
        style={lineStyle}
        onMouseOver={onLineEnter}
      />
    </>
  );
});

export default App;
