{"name": "@mario-ai/root", "version": "1.1.2", "license": "MIT", "description": "Extensible, fast and innovative web browser with material UI", "private": true, "packageManager": "pnpm@8.15.0", "scripts": {"dev": "pnpm --filter @mario-ai/shared build && node scripts/dev.js", "build": "pnpm --filter @mario-ai/shared build && pnpm --filter @mario-ai/browser build && pnpm --filter @mario-ai/electron build", "build:prod": "node scripts/build-platform.js", "build:win": "node scripts/build-platform.js win32", "build:mac": "node scripts/build-platform.js darwin", "build:linux": "node scripts/build-platform.js linux", "build:all": "node scripts/build-platform.js all", "start": "pnpm --filter @mario-ai/electron start", "clean": "pnpm --filter @mario-ai/shared clean && pnpm --filter @mario-ai/electron clean && pnpm --filter @mario-ai/browser clean", "compile-win32": "pnpm build && pnpm --filter @mario-ai/electron compile-win32", "compile-darwin": "pnpm build && pnpm --filter @mario-ai/electron compile-darwin", "compile-linux": "pnpm build && pnpm --filter @mario-ai/electron compile-linux", "migrate:backup": "node scripts/migrate-to-libsql.js backup", "migrate:run": "node scripts/migrate-to-libsql.js migrate", "migrate:validate": "node scripts/migrate-to-libsql.js validate", "migrate:help": "node scripts/migrate-to-libsql.js help"}, "devDependencies": {"concurrently": "^8.2.2", "glob": "^11.0.3", "wait-on": "^8.0.3"}, "dependencies": {"node-bookmarks-parser": "^2.0.0"}}