import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { viewUtils } from '@browser/core/utils/platform-lite';
import { ICON_UP, ICON_DOWN, ICON_CLOSE, ICON_SEARCH } from '@mario-ai/shared';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

const onInput = async () => {
  const { value } = store.findInputRef.current;

  store.findInfo.text = value;

  if (value === '') {
    viewUtils.stopFindInPage(store.tabId, 'clearSelection');
    store.findInfo.occurrences = '0/0';
  } else {
    await viewUtils.findInPage(store.tabId, value);
  }
};

const move = (forward: boolean) => async () => {
  const { value } = store.findInputRef.current;
  if (value === '') return;

  await viewUtils.findInPage(store.tabId, value, {
    forward,
    findNext: true,
  });
};

const onKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.key === 'Enter') {
    move(true)();
  }
};

const onKeyUp = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.key === 'Escape') {
    store.hide();
  }
};

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本
  const appClasses = cn(
    'm-4 mt-[3px] rounded-[10px] shadow-dialog bg-white',
    'bg-mario-dialog',
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black'
  );

  // StyledFind 样式
  const findClasses = cn(
    'rounded-[30px] h-10 items-center overflow-hidden flex',
    'app-region-no-drag' // -webkit-app-region: no-drag
  );

  // SearchIcon 样式
  const searchIconClasses = cn(
    'min-w-4 h-4 ml-3 opacity-54 bg-center bg-no-repeat bg-contain'
  );

  const searchIconStyle: React.CSSProperties = {
    backgroundImage: `url(${ICON_SEARCH})`,
    filter: store.theme['dialog.lightForeground'] ? 'invert(100%)' : 'none',
  };

  // Input 样式
  const inputClasses = cn(
    'w-full h-full text-sm mr-2 border-none outline-none bg-transparent ml-2 text-inherit'
  );

  // Button 样式
  const buttonClasses = cn(
    'w-6 h-6 opacity-54 relative bg-center bg-no-repeat bg-contain cursor-pointer',
    'after:content-[""] after:absolute after:rounded-full after:inset-0',
    'after:bg-black after:bg-opacity-8 after:opacity-0 after:transition-opacity after:duration-200',
    'hover:after:opacity-100'
  );

  // Buttons 样式
  const buttonsClasses = cn('flex mr-2');

  // Occurrences 样式
  const occurrencesClasses = cn('opacity-54 mr-1');

  // Button 组件
  const FindButton = ({ onClick, icon, size }: { onClick: () => void; icon: string; size: number }) => {
    const buttonStyle: React.CSSProperties = {
      backgroundImage: `url(${icon})`,
      backgroundSize: `${size}px`,
      filter: store.theme['dialog.lightForeground'] ? 'invert(100%)' : 'none',
    };

    return (
      <div
        className={buttonClasses}
        style={buttonStyle}
        onClick={onClick}
      />
    );
  };

  return (
    <div className={appClasses}>
      <UIStyle />
      <div className={findClasses} onKeyUp={onKeyUp}>
        <div
          className={searchIconClasses}
          style={searchIconStyle}
        />
        <input
          className={inputClasses}
          autoFocus
          value={store.findInfo.text}
          onKeyPress={onKeyPress}
          onChange={onInput}
          ref={store.findInputRef}
          placeholder="搜索页面内容"
        />
        <div className={occurrencesClasses}>
          {store.findInfo.occurrences}
        </div>
        <div className={buttonsClasses}>
          <FindButton onClick={move(false)} icon={ICON_UP} size={20} />
          <FindButton onClick={move(true)} icon={ICON_DOWN} size={20} />
          <FindButton onClick={() => store.hide()} icon={ICON_CLOSE} size={16} />
        </div>
      </div>
    </div>
  );
});
