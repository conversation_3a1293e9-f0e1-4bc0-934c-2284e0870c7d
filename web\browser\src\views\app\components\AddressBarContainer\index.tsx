import * as React from 'react';
import { observer } from 'mobx-react-lite';
import { AddressBar } from '../AddressBar';
import store from '../../store';
import { cn } from '@browser/utils/tailwind-helpers';
import { hexToRgb } from '@browser/core/utils/colors';

export const AddressBarContainer = observer(() => {
  const handleMouseDown = (e: React.MouseEvent) => {
    // 只有当点击的是容器本身时才失焦
    if (e.target === e.currentTarget) {
      store.inputRef?.blur();
    }
  };

  const visible = store.addressbarFocused || store.addressbarEditing;

  // StyledAddressBarContainer 样式 - 匹配64px侧边栏
  const containerClasses = cn(
    'absolute left-0 right-0 bottom-0 top-0 z-[999] items-center',
    'pl-16 pr-10 transition-all duration-100',
    '[&]:[-webkit-app-region:no-drag]',
    // 显示状态控制
    visible ? 'flex opacity-100 scale-100 pointer-events-auto' : 'table opacity-0 scale-105 pointer-events-none'
  );

  // 背景色计算
  const { r, g, b } = hexToRgb(store.theme['titlebar.backgroundColor']);
  const containerStyle = {
    backgroundColor: `rgba(${r}, ${g}, ${b}, 0.75)`
  };

  return (
    <div
      className={containerClasses}
      style={containerStyle}
      onMouseDown={handleMouseDown}
    >
      <AddressBar />
    </div>
  );
});
