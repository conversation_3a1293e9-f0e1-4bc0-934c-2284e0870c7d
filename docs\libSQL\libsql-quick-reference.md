# LibSQL快速参考 - <PERSON> AI浏览器

## 🚀 快速开始

### 基本使用

```typescript
// 获取存储服务实例
const storage = Application.instance.storage;

// 基础CRUD操作
const bookmarks = await storage.find({ scope: 'bookmarks', query: {} });
const newItem = await storage.insert({ scope: 'bookmarks', item: data });
await storage.update({ scope: 'bookmarks', query: { _id: id }, update: data });
await storage.remove({ scope: 'bookmarks', query: { _id: id } });
```

## 📊 支持的数据类型

| Scope | 描述 | 主要字段 |
|-------|------|----------|
| `bookmarks` | 书签数据 | title, url, isFolder, parent |
| `history` | 浏览历史 | title, url, visitTime |
| `favicons` | 网站图标 | url, favicon |
| `settings` | 应用设置 | 各种配置项 |
| `formfill` | 表单填充 | domain, fields |
| `startupTabs` | 启动标签 | url, pinned, windowId |
| `permissions` | 网站权限 | origin, permissions |

## 🔍 查询示例

### 基础查询

```typescript
// 查询所有书签
const allBookmarks = await storage.find({
  scope: 'bookmarks',
  query: {}
});

// 查询文件夹
const folders = await storage.find({
  scope: 'bookmarks', 
  query: { isFolder: true }
});

// 查询特定父级下的书签
const childBookmarks = await storage.find({
  scope: 'bookmarks',
  query: { parent: 'folder-id' }
});

// 查询最近访问的历史
const recentHistory = await storage.find({
  scope: 'history',
  query: {},
  sort: { visitTime: -1 },
  limit: 50
});
```

### 复杂查询

```typescript
// 使用复杂SQL查询
const popularBookmarks = await storage.complexQuery(`
  SELECT b.*, COUNT(h.id) as visit_count
  FROM bookmarks b
  LEFT JOIN history h ON b.url = h.url
  WHERE b.is_folder = 0
  GROUP BY b.id
  ORDER BY visit_count DESC
  LIMIT 20
`);

// 全文搜索
const searchResults = await storage.fullTextSearch({
  table: 'bookmarks',
  query: '搜索关键词',
  fields: ['title', 'url']
});
```

## ✏️ 数据操作

### 插入数据

```typescript
// 添加书签
const bookmark = await storage.insert({
  scope: 'bookmarks',
  item: {
    title: '新书签',
    url: 'https://example.com',
    isFolder: false,
    parent: 'main-folder-id'
  }
});

// 添加文件夹
const folder = await storage.insert({
  scope: 'bookmarks',
  item: {
    title: '新文件夹',
    isFolder: true,
    parent: 'main-folder-id'
  }
});

// 添加历史记录
const historyItem = await storage.insert({
  scope: 'history',
  item: {
    title: '网页标题',
    url: 'https://example.com',
    visitTime: Date.now()
  }
});
```

### 更新数据

```typescript
// 更新书签标题
await storage.update({
  scope: 'bookmarks',
  query: { _id: 'bookmark-id' },
  update: { title: '新标题' }
});

// 批量更新
await storage.update({
  scope: 'bookmarks',
  query: { parent: 'old-folder-id' },
  update: { parent: 'new-folder-id' },
  multi: true
});

// 更新设置
await storage.updateSettings({
  theme: 'dark',
  language: 'zh-CN'
});
```

### 删除数据

```typescript
// 删除单个书签
await storage.remove({
  scope: 'bookmarks',
  query: { _id: 'bookmark-id' }
});

// 批量删除
await storage.remove({
  scope: 'history',
  query: { visitTime: { $lt: Date.now() - 30 * 24 * 60 * 60 * 1000 } },
  multi: true
});

// 清空表
await storage.remove({
  scope: 'formfill',
  query: {},
  multi: true
});
```

## 🎯 IPC接口

### 前端调用

```typescript
import { eventUtils } from '@browser/core/utils/platform-lite';

// 获取书签
const bookmarks = await eventUtils.invoke('get-bookmarks');

// 添加书签
const result = await eventUtils.invoke('add-bookmark', {
  title: '标题',
  url: 'https://example.com'
});

// 搜索书签
const results = await eventUtils.invoke('search-bookmarks', '关键词');

// 获取设置
const settings = await eventUtils.invoke('get-settings');

// 更新设置
await eventUtils.invoke('update-settings', { theme: 'dark' });
```

### 监听事件

```typescript
// 监听设置更新
eventUtils.on('update-settings', (event, settings) => {
  console.log('设置已更新:', settings);
});

// 监听书签变化
eventUtils.on('bookmarks-updated', (event, bookmarks) => {
  console.log('书签已更新:', bookmarks);
});
```

## 📈 性能优化

### 缓存使用

```typescript
// 获取缓存统计
const cacheStats = await storage.getCacheStats();
console.log('缓存命中率:', cacheStats.hitRate);

// 清理缓存
await storage.clearCache();

// 预热缓存
await storage.preloadCache(['bookmarks', 'settings']);
```

### 批量操作

```typescript
// 批量插入（推荐）
await storage.batchInsert('bookmarks', bookmarkArray);

// 事务操作
await storage.transaction(async (tx) => {
  await tx.insert('bookmarks', bookmark1);
  await tx.insert('bookmarks', bookmark2);
  await tx.update('folders', { id: folderId }, { count: newCount });
});
```

## 🔧 调试和监控

### 性能监控

```typescript
// 获取性能统计
const perfStats = await storage.getPerformanceStats();
console.log(perfStats);
// 输出：
// {
//   find_cache_hit: { count: 1250, avgMs: 0.12 },
//   find_db_query: { count: 98, avgMs: 2.34 },
//   insert: { count: 45, avgMs: 1.89 }
// }
```

### 健康检查

```typescript
// 系统健康检查
const health = await storage.healthCheck();
console.log(health);
// 输出：
// {
//   database: 'healthy',
//   cache: 'healthy',
//   performance: 'good'
// }
```

### 数据备份

```typescript
// 创建备份
await storage.createBackup();

// 恢复数据
await storage.restoreFromBackup('/path/to/backup.db');
```

## 🚨 错误处理

### 常见错误

```typescript
try {
  const result = await storage.find({ scope: 'bookmarks', query: {} });
} catch (error) {
  switch (error.code) {
    case 'DATABASE_LOCKED':
      // 数据库被锁定，等待重试
      await new Promise(resolve => setTimeout(resolve, 100));
      break;
      
    case 'INVALID_QUERY':
      // 查询参数错误
      console.error('查询参数错误:', error.message);
      break;
      
    case 'CACHE_ERROR':
      // 缓存错误，降级到数据库查询
      console.warn('缓存错误，使用数据库查询');
      break;
      
    default:
      console.error('未知错误:', error);
  }
}
```

### 降级策略

```typescript
// 自动降级示例
async function safeQuery(queryParams) {
  try {
    // 尝试从缓存获取
    return await storage.findFromCache(queryParams);
  } catch (cacheError) {
    try {
      // 降级到数据库查询
      return await storage.find(queryParams);
    } catch (dbError) {
      // 最终降级到默认值
      console.error('所有查询方式都失败:', dbError);
      return [];
    }
  }
}
```

## 🤖 AI功能扩展

### AI字段使用

```typescript
// 查询带AI标签的书签
const aiBookmarks = await storage.find({
  scope: 'bookmarks',
  query: { aiTags: { $ne: null } }
});

// 更新AI字段
await storage.update({
  scope: 'bookmarks',
  query: { _id: 'bookmark-id' },
  update: {
    aiTags: JSON.stringify(['技术', '前端', 'React']),
    aiSummary: 'React官方文档，包含完整的API参考',
    aiCategory: '技术文档'
  }
});

// AI搜索（语义搜索）
const semanticResults = await storage.complexQuery(`
  SELECT * FROM bookmarks 
  WHERE ai_tags LIKE ? OR ai_summary LIKE ?
  ORDER BY ai_relevance_score DESC
`, ['%React%', '%组件%']);
```

## 📋 最佳实践

### ✅ 推荐做法

```typescript
// 1. 使用缓存优先策略
const bookmarks = await storage.find({ scope: 'bookmarks', query: {} });

// 2. 批量操作
await storage.batchInsert('bookmarks', bookmarkArray);

// 3. 事务保证一致性
await storage.transaction(async (tx) => {
  // 相关操作
});

// 4. 合理的错误处理
try {
  const result = await storage.operation();
} catch (error) {
  // 具体的错误处理逻辑
}
```

### ❌ 避免的做法

```typescript
// 1. 避免频繁的单个操作
for (const item of items) {
  await storage.insert({ scope: 'bookmarks', item }); // ❌
}

// 2. 避免忽略错误
await storage.operation(); // ❌ 没有错误处理

// 3. 避免不必要的复杂查询
await storage.complexQuery('SELECT * FROM bookmarks'); // ❌ 应该用find
```

## 🔗 相关链接

- [完整架构指南](./libsql-architecture-guide.md)
- [迁移指南](./libsql-migration-guide.md)
- [实现总结](./libsql-implementation-summary.md)
