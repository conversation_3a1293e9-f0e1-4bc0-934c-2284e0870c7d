import { eventUtils } from '@browser/core/utils/platform-lite';
import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { Textfield } from '@browser/core/components/Textfield';
import { PasswordInput } from '@browser/core/components/PasswordInput';
import { Button } from '@browser/core/components/Button';
import List from '../List';
import { BLUE_500 } from '@mario-ai/shared';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

const onSave = () => {
  const username = store.usernameRef.current.value.trim();
  const password = store.passwordRef.current.value.trim();

  eventUtils.send(`credentials-hide-${store.windowId}`);

  // eventUtils.send(`credentials-save-${store.windowId}`, {
  //   username,
  //   password,
  //   update: store.content === 'update',
  //   oldUsername: store.oldUsername,
  // });
};

const onClose = () => {
  eventUtils.send(`credentials-hide-${store.windowId}`);
};

const Fields = observer(() => {
  return (
    <div style={{ display: store.content !== 'list' ? 'block' : 'none' }}>
      <Textfield ref={store.usernameRef} label="用户名" />
      <PasswordInput ref={store.passwordRef} />
    </div>
  );
});

export const App = observer(() => {
  let title = '';

  if (store.content === 'list') {
    title = store.list.length
      ? 'Saved passwords for this site'
      : 'No passwords saved for this site';
  } else {
    title = store.content === 'save' ? 'Save password?' : 'Update password?';
  }

  // StyledApp 样式 - Tailwind 版本
  const appClasses = cn(
    'm-4 p-4 rounded-lg bg-white',
    'shadow-[0_3px_6px_rgba(0,0,0,0.16),0_3px_6px_rgba(0,0,0,0.23)]'
  );

  // Title 样式
  const titleClasses = cn('text-base'); // font-size: 16px

  // Container 样式
  const containerClasses = cn(
    'py-2', // padding: 8px 0px
    // 子元素 textfield 的 margin-top
    '[&_.textfield]:mt-3' // margin-top: 12px
  );

  // Buttons 样式
  const buttonsClasses = cn(
    'w-full flex mt-4 float-right' // margin-top: 16px
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <div className={titleClasses}>{title}</div>
      <div className={containerClasses}>
        <Fields />
        <List />
      </div>
      <div className={buttonsClasses}>
        {store.content !== 'list' && (
          <Button
            onClick={onSave}
            foreground="black"
            background="rgba(0, 0, 0, 0.08)"
            style={{ marginLeft: 'auto' }}
          >
            保存
          </Button>
        )}
        {store.content === 'list' && (
          <Button
            foreground={BLUE_500}
            background="transparent"
            style={{ marginRight: 'auto', padding: '0px 12px' }}
            onClick={onClose}
          >
            管理密码
          </Button>
        )}
        <Button
          foreground="black"
          background="rgba(0, 0, 0, 0.08)"
          onClick={onClose}
          style={{ marginLeft: 8 }}
        >
          取消
        </Button>
      </div>
      <div style={{ clear: 'both' }}></div>
    </div>
  );
});
