import * as React from 'react';
import {observer} from 'mobx-react-lite';

import store from '../../store';
import {isURL} from '@mario-ai/shared';
import { viewUtils, clipboardUtils, searchUtils } from '@browser/core/utils/platform-lite';
import {ToolbarButton} from '../ToolbarButton';
import {ICON_SEARCH} from '@mario-ai/shared';
import {SiteButtons} from '../SiteButtons';
import {DEFAULT_TITLEBAR_HEIGHT} from '@mario-ai/shared';
import MyInput from "./input";
import { cn } from '@browser/utils/tailwind-helpers';
// 移除electron remote依赖

const onMouseDown = (e: React.MouseEvent<HTMLInputElement>) => {
  console.log('[AddressBar] onMouseDown triggered');
  e.stopPropagation();

  // 安全地获取焦点
  const target = e.currentTarget;
  if (target) {
    setTimeout(() => {
      try {
        target.focus();
      } catch (error) {
        console.warn('[AddressBar] Failed to focus input:', error);
      }
    }, 0);
  }

  if (!store.isCompact) {
    // 即使不是compact模式，也要处理焦点
    store.addressbarTextVisible = false;
    store.addressbarFocused = true;
    return;
  }

  store.addressbarTextVisible = false;
  store.addressbarFocused = true;
};

const onFocus = (e: React.FocusEvent<HTMLInputElement>) => {
  console.log('[AddressBar] onFocus triggered');
  store.addressbarTextVisible = false;
  store.addressbarFocused = true;

  if (store.tabs.selectedTab) {
    store.tabs.selectedTab.addressbarFocused = true;
  }

  if (store.isCompact) {
    // eventUtils.send(`window-fix-dragging-${store.windowId}`);
    e.currentTarget.select();
  }
};

const onSelect = (e: React.MouseEvent<HTMLInputElement>) => {
  if (store.tabs.selectedTab) {
    store.tabs.selectedTab.addressbarSelectionRange = [
      e.currentTarget.selectionStart || 0,
      e.currentTarget.selectionEnd || 0,
    ];
  }
};

const onMouseUp = (e: React.MouseEvent<HTMLInputElement>) => {
  const selection = window.getSelection();
  if (
    !store.isCompact &&
    selection && selection.toString().length === 0 &&
    !store.mouseUpped
  ) {
    e.currentTarget.select();
  }

  store.setMouseUpped(true);
};

const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
  console.log('[AddressBar] onKeyDown:', e.key, 'value:', e.currentTarget.value, 'defaultPrevented:', e.defaultPrevented);

  // 对于普通字符输入，不要阻止默认行为
  if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete') {
    // 让这些键正常工作
    return;
  }

  if (e.key === 'Escape') {
    if (store.tabs.selectedTab) {
      store.tabs.selectedTab.addressbarValue = '';
    }
  }

  if (e.key === 'Escape') {
    const target = e.currentTarget;
    requestAnimationFrame(() => {
      target.select();
    });
  }

  if (e.key === 'Enter') {
    enterNow(e.currentTarget.value, e.currentTarget);
  }
};

const enterNow = (value: string, target: any) => {
  console.log('[AddressBar] enterNow called with value:', value);
  let url = value;
  if (isURL(value)) {
    url = value.indexOf('://') === -1 ? `http://${value}` : value;
    console.log('[AddressBar] Detected as URL, final URL:', url);
  } else {
    const searchUrl = store.settings?.searchEngine?.url || 'https://www.google.com/search?q=%s';
    url = searchUrl.replace('%s', value);
    console.log('[AddressBar] Detected as search, final URL:', url);
  }

  // 添加null检查
  if (store.tabs.selectedTab) {
    console.log('[AddressBar] Setting URL on tab:', store.tabs.selectedTabId);
    store.tabs.selectedTab.addressbarValue = url;
    viewUtils.loadURL(String(store.tabs.selectedTabId), url);
  } else {
    console.log('[AddressBar] No selected tab found!');
  }
  blurNow(target);
}

let addressbarRef: HTMLDivElement | null = null;
let isBlurring = false;

const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  console.log('[AddressBar] onChange triggered, value:', e.currentTarget.value);
  changeText(e.currentTarget.value, e.currentTarget.selectionStart, true);
};

const changeText = (t: string, cursorPos: any, showSearch: boolean) => {
  // 同时更新store和selectedTab的addressbarValue
  store.addressbarValue = t;
  if (store.tabs.selectedTab) {
    store.tabs.selectedTab.addressbarValue = t;
  }

  // 暂时禁用搜索功能来测试输入问题
  // if (addressbarRef) {
  //   const {left, width} = addressbarRef.getBoundingClientRect();

  //   if (t.trim() !== '' && showSearch) {
  //     searchUtils.show({
  //       text: t,
  //       x: left,
  //       y: !store.isCompact ? DEFAULT_TITLEBAR_HEIGHT : 0,
  //       width: width,
  //     });
  //     store.addressbarEditing = true;
  //   }
  // }
}
const blurNow = (target: any) => {
  if (isBlurring) return;
  isBlurring = true;

  console.log('[AddressBar] blurNow called');
  target.blur();
  const selection = window.getSelection();
  if (selection) {
    selection.removeAllRanges();
  }

  // 失焦时恢复显示当前页面的URL
  const {selectedTab} = store.tabs;
  if (selectedTab) {
    selectedTab.addressbarFocused = false;
    // 如果当前输入的值不是有效URL，恢复显示页面URL
    if (!isURL(store.addressbarValue) || store.addressbarValue === '') {
      store.addressbarValue = selectedTab.url || '';
      selectedTab.addressbarValue = selectedTab.url || '';
      // 更新URL分段显示
      store.addressbarUrlSegments = store.tabs.parseUrlSegments(selectedTab.url || '');
    }
  }

  store.addressbarTextVisible = true;
  store.addressbarFocused = false;
  store.mouseUpped = false;

  // if (store.isCompact && !store.addressbarEditing)
  //   eventUtils.send(`window-fix-dragging-${store.windowId}`);

  setTimeout(() => {
    isBlurring = false;
  }, 100);
};

const onBlur = (e: React.FocusEvent<HTMLInputElement>) => {
  if (!isBlurring) {
    console.log('[AddressBar] onBlur triggered');
    blurNow(e.currentTarget);
  }
};

const onContextMenu = async (e: React.MouseEvent<HTMLDivElement>) => {
  e.preventDefault();

  // 在轻量级环境中，使用浏览器原生的右键菜单
  // 或者可以实现一个简单的自定义菜单
  console.log('[Lite] Context menu requested for address bar');

  // 简单的复制功能
  if (store.addressbarValue) {
    try {
      clipboardUtils.write(store.addressbarValue);
      console.log('[Lite] Address copied to clipboard');
    } catch (error) {
      console.warn('[Lite] Failed to copy to clipboard:', error);
    }
  }
};

export const AddressBar = observer(() => {
  // StyledAddressBar 样式 - Tailwind 版本
  const addressBarClasses = cn(
    'h-[30px] flex-1 rounded-lg mx-[7px] flex items-center relative',
    'text-[15px] overflow-hidden border border-black/32',
    // 背景色和文本颜色根据主题
    'bg-mario-addressbar text-mario-addressbar-text'
  );

  // InputContainer 样式 - Tailwind 版本
  const inputContainerClasses = cn(
    'flex-1 relative h-full ml-0.5 overflow-hidden'
  );

  // Text 样式 - Tailwind 版本
  const textClasses = cn(
    'pointer-events-none absolute top-1/2 -translate-y-1/2 flex-1',
    'text-inherit -mt-px flex-nowrap whitespace-nowrap overflow-hidden',
    'text-sm z-[1]'
  );

  return (
    <div
      className={addressBarClasses}
      ref={(r) => (addressbarRef = r)}
    >
      <ToolbarButton
        toggled={false}
        icon={ICON_SEARCH}
        size={16}
        dense
        iconStyle={{transform: 'scale(-1,1)'}}
      />
      <div className={inputContainerClasses}>
        <MyInput
          ref={(r) => (store.inputRef = r)}
          spellCheck={false}
          tabIndex={0}
          autoFocus={false}
          onKeyDown={onKeyDown}
          onMouseDown={onMouseDown}
          onSelect={onSelect}
          onBlur={onBlur}
          onFocus={onFocus}
          onMouseUp={onMouseUp}
          onChange={onChange}
          placeholder="搜索或输入网址"
          onContextMenu={onContextMenu}
          value={store.addressbarValue}
          visible={!store.addressbarTextVisible}
        ></MyInput>
        <div
          className={cn(
            textClasses,
            store.addressbarTextVisible && store.addressbarValue !== '' ? 'flex' : 'hidden'
          )}
        >
          {store.addressbarUrlSegments.map((item, key) => (
            <div
              key={key}
              style={{
                opacity: item.grayOut ? 0.54 : 1,
              }}
            >
              {item.value}
            </div>
          ))}
        </div>
      </div>
      {!store.isCompact && <SiteButtons/>}
    </div>
  );
});
