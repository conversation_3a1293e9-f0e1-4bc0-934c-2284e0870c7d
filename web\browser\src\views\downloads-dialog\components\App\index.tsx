import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { DownloadItem } from '../DownloadItem';
import { eventUtils } from '@browser/core/utils/platform-lite';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

const dialogClicked = (e: React.MouseEvent<HTMLDivElement>) => {
  store.closeAllDownloadMenu();
};

export const App = observer(() => {
  const height =
    8 +
    Math.min(8, store.downloads.length) * (64 + 8) +
    (store.downloads.find((x) => x.menuIsOpen === true) ? 200 : 0);
  eventUtils.send(`height-${store.id}`, height);

  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  const appClasses = cn(
    'p-2 text-sm rounded-[10px] shadow-dialog bg-mario-dialog',
    'animate-[fadeIn_0.15s_ease-out]',
    // 自定义滚动条样式
    'scrollbar-thin app-region-no-drag',
    // 文本颜色根据主题
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black',
    // 滚动条颜色
    store.theme['dialog.lightForeground']
      ? 'scrollbar-thumb-white scrollbar-thumb-opacity-20 hover:scrollbar-thumb-opacity-30'
      : 'scrollbar-thumb-black scrollbar-thumb-opacity-20 hover:scrollbar-thumb-opacity-30',
    'scrollbar-track-transparent'
  );

  const appStyle: React.CSSProperties = {
    maxHeight: store.maxHeight,
    overflow: 'unset',
  };

  return (
    <div
      className={appClasses}
      style={appStyle}
      onClick={dialogClicked}
    >
      <UIStyle />
      {store.downloads
        .slice()
        .reverse()
        .map((item) => (
          <DownloadItem item={item} key={item.id}></DownloadItem>
        ))}
    </div>
  );
});
