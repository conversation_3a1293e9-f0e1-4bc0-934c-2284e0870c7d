import * as React from 'react';
import { observer } from 'mobx-react-lite';

import List from '../List';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本
  const appClasses = cn(
    'shadow-[0_3px_6px_rgba(0,0,0,0.16),0_3px_6px_rgba(0,0,0,0.23)]',
    'rounded bg-white m-2 overflow-hidden',
    '[&]:[-webkit-app-region:no-drag]'
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <List />
    </div>
  );
});
