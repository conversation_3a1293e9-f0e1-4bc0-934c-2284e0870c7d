import { observer } from 'mobx-react-lite';
import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
// 移除ToolbarButton导入，使用原生button元素
import store from '../../store';
import { eventUtils } from '@browser/core/utils/platform-lite';

interface AIToolbarProps {
  className?: string;
}

export const AIToolbar = observer(({ className }: AIToolbarProps) => {
  // 默认显示标签，不再支持切换
  const showLabels = true;

  // 处理工具点击 - 直接调用store方法，无需IPC
  const handleToolClick = React.useCallback((toolId: string) => {
    console.log('[AIToolbar] Tool clicked:', toolId);

    // 检查是否已经有对应的标签页
    const existingTab = store.tabs.list.find(tab => 
      tab.url.includes(`ai-${toolId}`)
    );

    if (existingTab) {
      // 切换到已存在的标签页
      store.tabs.selectTab(existingTab.id);
    } else {
      // 创建新的AI工具标签页
      eventUtils.send(`add-tab-${store.windowId}`, {
        url: `mario-ai://ai-${toolId}`,
        active: true,
      });
    }
  }, []);



  // 移除主题切换功能

  // 键盘快捷键处理
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey) {
        switch (e.key) {
          case '1':
            e.preventDefault();
            handleToolClick('notes');
            break;
          case '2':
            e.preventDefault();
            handleToolClick('memory');
            break;
          case '3':
            e.preventDefault();
            handleToolClick('clipboard');
            break;
          case '0':
            e.preventDefault();
            handleToolClick('settings');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleToolClick]);

  // 工具栏样式 - 标准宽度64px
  const toolbarClasses = cn(
    'fixed left-0 top-0 h-full flex flex-col',
    'border-r border-mario-border',
    'z-[999999]', // 极高的z-index确保在BrowserView之上
    'w-16', // 标准宽度，64px
    className
  );

  // 工具栏背景样式 - 支持渐变
  const toolbarStyle: React.CSSProperties = {
    background: 'var(--mario-ai-toolbar-bg)',
  };

  // Logo区域样式 - 小字体最粗显示
  const logoClasses = cn(
    'flex items-center justify-center h-10 mt-3 mb-3',
    'text-xs font-black opacity-90 text-center px-1 font-extrabold'
  );

  // 工具按钮区域样式
  const toolsClasses = cn(
    'flex-1 flex flex-col gap-2 px-2'
  );

  // 设置区域样式
  const settingsClasses = cn(
    'p-2 mb-2'
  );

  // 标签样式 - 始终显示
  const labelClasses = cn(
    'text-xs text-mario-text-secondary mt-1 text-center opacity-100'
  );

  return (
    <div className={toolbarClasses} style={toolbarStyle}>
      {/* Logo区域 */}
      <div className={logoClasses}>
        Mario
      </div>

      {/* 移除主题切换按钮 */}

      {/* 工具按钮区域 */}
      <div className={toolsClasses}>
        {/* 智能笔记 */}
        <div className="flex flex-col items-center">
          <button
            onClick={() => handleToolClick('notes')}
            title="智能笔记 - 创建和管理AI增强的笔记 (Alt+1)"
            className="w-10 h-10 rounded-lg flex items-center justify-center hover:bg-mario-hover active:bg-mario-active transition-all duration-200 hover:scale-110"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
          </button>
          <div className={labelClasses}>笔记</div>
        </div>

        {/* AI记忆 */}
        <div className="flex flex-col items-center">
          <button
            onClick={() => handleToolClick('memory')}
            title="AI记忆 - 存储和检索重要信息 (Alt+2)"
            className="w-10 h-10 rounded-lg flex items-center justify-center hover:bg-mario-hover active:bg-mario-active transition-all duration-200 hover:scale-110"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6"/>
              <path d="m15.5 3.5-1.5 1.5"/>
              <path d="m10 12-1.5 1.5"/>
              <path d="m15.5 20.5-1.5-1.5"/>
              <path d="m4 12 2-2"/>
              <path d="m20 12-2-2"/>
              <path d="m4 12 2 2"/>
              <path d="m20 12-2 2"/>
            </svg>
          </button>
          <div className={labelClasses}>记忆</div>
        </div>

        {/* 智能剪贴板 */}
        <div className="flex flex-col items-center">
          <button
            onClick={() => handleToolClick('clipboard')}
            title="智能剪贴板 - 管理剪贴板历史和内容 (Alt+3)"
            className="w-10 h-10 rounded-lg flex items-center justify-center hover:bg-mario-hover active:bg-mario-active transition-all duration-200 hover:scale-110"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
              <path d="M12 11h4"/>
              <path d="M12 16h4"/>
              <path d="M8 11h.01"/>
              <path d="M8 16h.01"/>
            </svg>
          </button>
          <div className={labelClasses}>剪贴板</div>
        </div>
      </div>

      {/* 设置按钮区域 */}
      <div className={settingsClasses}>
        <div className="flex flex-col items-center">
          <button
            onClick={() => handleToolClick('settings')}
            title="设置 (Alt+0)"
            className="w-10 h-10 rounded-lg flex items-center justify-center hover:bg-mario-hover active:bg-mario-active transition-all duration-200 hover:scale-110"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="3"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </svg>
          </button>
          <div className={labelClasses}>设置</div>
        </div>
      </div>
    </div>
  );
});

export default AIToolbar;
