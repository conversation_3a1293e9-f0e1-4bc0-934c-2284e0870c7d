import { observable, action, makeObservable } from 'mobx';

import { IFormFillData } from '@mario-ai/shared';
import { eventUtils } from '@browser/core/utils/platform-lite';

export class AutoFillStore {
  // ✅ 重构: 移除复杂的数据库抽象层，改为直接IPC调用 (借鉴原始工程)

  @observable
  public credentials: IFormFillData[] = [];

  @observable
  public addresses: IFormFillData[] = [];

  @observable
  public menuVisible = false;

  @observable
  public menuTop = 0;

  @observable
  public menuLeft = 0;

  @observable
  public selectedItem: IFormFillData = null;

  public constructor() {
    makeObservable(this);

    this.load();

    window.addEventListener('message', ({ data }) => {
      if (data.type === 'credentials-insert') {
        this.credentials.push(data.data);
      } else if (data.type === 'credentials-update') {
        const { _id, username, passLength } = data.data;
        const item = this.credentials.find((r) => r._id === _id);

        item.fields = {
          username,
          passLength,
        };
      } else if (data.type === 'credentials-remove') {
        const { _id } = data.data;
        this.credentials = this.credentials.filter((r) => r._id !== _id);
      }
    });
  }

  @action
  public async load() {
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      const items: IFormFillData[] = await eventUtils.invoke('storage-get', {
        scope: 'formfill',
        query: {}
      });

      this.credentials = items.filter((r) => r.type === 'password');
      this.addresses = items.filter((r) => r.type === 'address');
      console.log('[AutoFillStore] Loaded formfill data via IPC:', items.length);
    } catch (error) {
      console.error('[AutoFillStore] Error loading formfill data:', error);
      this.credentials = [];
      this.addresses = [];
    }
  }

  public async removeItem(data: IFormFillData) {
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      await eventUtils.invoke('storage-remove', {
        scope: 'formfill',
        query: { _id: data._id }
      });

      if (data.type === 'password') {
        this.credentials = this.credentials.filter((r) => r._id !== data._id);
      } else {
        this.addresses = this.addresses.filter((r) => r._id !== data._id);
      }
      console.log('[AutoFillStore] Removed formfill item via IPC:', data._id);
    } catch (error) {
      console.error('[AutoFillStore] Error removing formfill item:', error);
    }
  }
}
