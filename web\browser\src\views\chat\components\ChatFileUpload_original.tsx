import React, { useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Image, Paperclip, Upload, X, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@shared/components/button';
import { cn } from '@shared/lib/utils';

interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
  className?: string;
}

interface UploadedFile {
  file: File;
  id: string;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

// File type validation
const IMAGE_TYPES = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
const DOCUMENT_TYPES = ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf', '.csv', '.xlsx', '.xls'];
const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

const validateFile = (file: File): { isValid: boolean; error?: string } => {
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  
  if (IMAGE_TYPES.includes(extension)) {
    if (file.size > MAX_IMAGE_SIZE) {
      return { isValid: false, error: `图片文件大小不能超过 ${MAX_IMAGE_SIZE / 1024 / 1024}MB` };
    }
  } else if (DOCUMENT_TYPES.includes(extension)) {
    if (file.size > MAX_FILE_SIZE) {
      return { isValid: false, error: `文档文件大小不能超过 ${MAX_FILE_SIZE / 1024 / 1024}MB` };
    }
  } else {
    return { isValid: false, error: '不支持的文件类型' };
  }
  
  return { isValid: true };
};

const isImageFile = (file: File): boolean => {
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  return IMAGE_TYPES.includes(extension);
};

export const ChatFileUpload: React.FC<FileUploadProps> = ({ onFilesSelected, className }) => {
  const imageInputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelection = async (files: FileList | null, type: 'image' | 'file') => {
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    const validFiles: File[] = [];
    const errors: string[] = [];

    // Validate files
    fileArray.forEach(file => {
      const validation = validateFile(file);
      if (validation.isValid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    if (errors.length > 0) {
      // Show error notification
      console.error('File validation errors:', errors);
      // You can implement a toast notification here
      alert(errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setIsUploading(true);
      
      // Create uploaded file entries
      const newUploadedFiles: UploadedFile[] = validFiles.map(file => ({
        file,
        id: `${Date.now()}-${Math.random()}`,
        status: 'uploading'
      }));

      setUploadedFiles(prev => [...prev, ...newUploadedFiles]);

      // Simulate upload process (replace with actual upload logic)
      setTimeout(() => {
        setUploadedFiles(prev => 
          prev.map(uf => 
            newUploadedFiles.find(nuf => nuf.id === uf.id) 
              ? { ...uf, status: 'success' as const }
              : uf
          )
        );
        setIsUploading(false);
        onFilesSelected(validFiles);
      }, 1000);
    }
  };

  const removeFile = (id: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== id));
  };

  const handleImageUpload = () => {
    imageInputRef.current?.click();
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Hidden file inputs */}
      <input
        ref={imageInputRef}
        type="file"
        accept={IMAGE_TYPES.join(',')}
        multiple
        className="hidden"
        onChange={(e) => handleFileSelection(e.target.files, 'image')}
      />
      <input
        ref={fileInputRef}
        type="file"
        accept={[...IMAGE_TYPES, ...DOCUMENT_TYPES].join(',')}
        multiple
        className="hidden"
        onChange={(e) => handleFileSelection(e.target.files, 'file')}
      />

      {/* Image Upload Button */}
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-full hover:bg-accent/80 transition-colors duration-200"
          onClick={handleImageUpload}
          disabled={isUploading}
          title="上传图片 (JPG, PNG, GIF, WebP, SVG)"
        >
          <Image className="h-4 w-4" />
        </Button>
      </motion.div>

      {/* File Upload Button */}
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-full hover:bg-accent/80 transition-colors duration-200"
          onClick={handleFileUpload}
          disabled={isUploading}
          title="上传文件 (PDF, DOC, TXT, MD 等)"
        >
          <Paperclip className="h-4 w-4" />
        </Button>
      </motion.div>

      {/* Upload Status Indicator */}
      {isUploading && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center gap-1 text-xs text-muted-foreground"
        >
          <Upload className="h-3 w-3 animate-pulse" />
          上传中...
        </motion.div>
      )}

      {/* Uploaded Files Preview */}
      {uploadedFiles.length > 0 && (
        <div className="flex items-center gap-1 max-w-[200px] overflow-x-auto">
          {uploadedFiles.map((uploadedFile) => (
            <motion.div
              key={uploadedFile.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center gap-1 bg-accent/50 rounded-full px-2 py-1 text-xs"
            >
              {uploadedFile.status === 'uploading' && (
                <Upload className="h-3 w-3 animate-pulse text-blue-500" />
              )}
              {uploadedFile.status === 'success' && (
                <CheckCircle className="h-3 w-3 text-green-500" />
              )}
              {uploadedFile.status === 'error' && (
                <AlertCircle className="h-3 w-3 text-red-500" />
              )}
              
              <span className="truncate max-w-[80px]" title={uploadedFile.file.name}>
                {uploadedFile.file.name}
              </span>
              
              <button
                onClick={() => removeFile(uploadedFile.id)}
                className="hover:bg-accent rounded-full p-0.5 transition-colors"
              >
                <X className="h-2 w-2" />
              </button>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChatFileUpload;
