import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const bookmarks = sqliteTable('bookmarks', {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  url: text('url'),
  favicon: text('favicon'),
  isFolder: integer('is_folder', { mode: 'boolean' }).default(false),
  parentId: text('parent_id').references(() => bookmarks.id),
  orderIndex: integer('order_index').default(0),
  staticType: text('static_type'), // 'main', 'other', 'mobile'
  expanded: integer('expanded', { mode: 'boolean' }).default(false),
  // AI增强字段 (为未来预留)
  aiTags: text('ai_tags'), // JSON array
  aiSummary: text('ai_summary'),
  aiCategory: text('ai_category'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull()
});

export type Bookmark = typeof bookmarks.$inferSelect;
export type NewBookmark = typeof bookmarks.$inferInsert;
