import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { QuickMenu } from '../QuickMenu';
import store from '../../store';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  // 确保菜单有固定宽度，与主进程中的 menuWidth = 330 保持一致
  // 参考工程: border-radius: 10px, box-shadow: DIALOG_BOX_SHADOW
  const appClasses = cn(
    'w-[330px] rounded-[10px] bg-mario-dialog overflow-hidden relative',
    // 使用与参考工程一致的阴影: 0 12px 16px rgba(0, 0, 0, 0.12), 0 8px 10px rgba(0, 0, 0, 0.16)
    'shadow-[0_12px_16px_rgba(0,0,0,0.12),0_8px_10px_rgba(0,0,0,0.16)]',
    'animate-[fadeIn_0.15s_ease-out]'
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <QuickMenu />
    </div>
  );
});
