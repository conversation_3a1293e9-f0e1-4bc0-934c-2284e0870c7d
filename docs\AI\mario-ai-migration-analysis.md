# 🎯 MarioAI 项目迁移分析报告

**文档版本**: v1.0  
**创建日期**: 2025-08-03  
**源项目路径**: `C:\Users\<USER>\Desktop\package\MarioAI`  
**目标项目路径**: `C:\Users\<USER>\Desktop\mario-ai`

---

## 📊 项目概述

### 源项目分析
**源项目** (`C:\Users\<USER>\Desktop\package\MarioAI`) 是一个**完整的AI桌面助手**：

- **项目名称**: marioai-desktop-assistant
- **项目类型**: 独立的AI桌面助手应用
- **技术栈**: Electron + React + Node.js + Express
- **包管理**: PNPM Workspace (web + server + electron)
- **数据库**: LibSQL + Drizzle ORM
- **UI框架**: React + Vite + TailwindCSS + Radix UI

### 目标项目现状
**目标项目** (当前工程) 是一个**集成AI功能的浏览器**：

- **项目名称**: @mario-ai/root
- **项目类型**: 基于Wexond的AI浏览器
- **技术栈**: Electron + React + Vite
- **包管理**: PNPM Workspace (shared + electron + web/browser + web/koodo-reader)
- **数据库**: LibSQL + Drizzle ORM
- **特色功能**: 已实现轻量级AI工具栏

---

## 🧩 源项目核心功能模块

### AI功能模块结构
```
web/src/modules/
├── chat/              # 🤖 AI对话功能
│   ├── ChatInterface.tsx
│   ├── ChatFileUpload.tsx
│   └── SystemPromptInput.tsx
├── memory/            # 🧠 AI记忆管理
│   ├── AIMemoryModal.tsx
│   ├── MemoryManager.tsx
│   └── RichTextEditor.tsx
├── notes/             # 📝 智能笔记
│   ├── NotesManager.tsx
│   ├── BlockNoteEditor.tsx
│   └── BlockNoteTheme.ts
├── knowledge-base/    # 📚 知识库管理
├── clipboard/         # 📋 剪贴板管理
├── mcp/              # 🔌 MCP协议集成
├── prompt/           # 💭 提示词管理
├── browser/          # 🌐 浏览器自动化
├── python-sandbox/   # 🐍 Python沙盒
├── theme/            # 🎨 主题管理
└── settings/         # ⚙️ 设置管理
```

### 后端服务模块
```
server/modules/
├── browser/          # 浏览器自动化API
├── chat/             # 对话API服务
├── clipboard/        # 剪贴板API
├── knowledge-base/   # 知识库API
├── mcp/              # MCP协议API
├── memory/           # 记忆管理API
├── prompt/           # 提示词API
└── python-sandbox/   # Python执行API
```

### 关键技术依赖
- **AI功能**: `@google/genai`, `@modelcontextprotocol/sdk`, `mem0ai`
- **富文本**: `@blocknote/react`, `react-markdown`, `rehype-highlight`
- **UI组件**: `@radix-ui/*`, `class-variance-authority`, `tailwindcss-animate`
- **数据库**: `@libsql/client`, `drizzle-orm`
- **工具库**: `zod`, `tailwind-merge`, `react-textarea-autosize`

---

## 🎯 迁移策略分析

### 架构差异对比

| 维度 | 源项目 | 目标项目 | 迁移策略 |
|------|--------|----------|----------|
| **应用类型** | 独立AI助手 | AI集成浏览器 | 功能模块化集成 |
| **UI集成** | 独立窗口 | 浏览器标签页 | 标签页容器适配 |
| **后端服务** | Express服务器 | Electron主进程 | IPC通信替代 |
| **数据存储** | LibSQL独立 | LibSQL集成 | Schema统一 |
| **包结构** | 3包结构 | 4包结构 | 依赖重新分配 |

### 迁移优先级矩阵

| 模块 | 优先级 | 复杂度 | 用户价值 | 技术风险 | 迁移策略 |
|------|--------|--------|----------|----------|----------|
| **Chat** | 🔴 最高 | 中 | 极高 | 低 | 立即迁移 |
| **Memory** | 🟡 高 | 高 | 高 | 中 | 第二阶段 |
| **Notes** | 🟡 高 | 中 | 高 | 低 | 第二阶段 |
| **Clipboard** | 🟢 中 | 低 | 中 | 低 | 第三阶段 |
| **Knowledge Base** | 🟢 中 | 高 | 中 | 中 | 第三阶段 |
| **MCP** | 🟢 中 | 高 | 中 | 高 | 按需迁移 |
| **Python Sandbox** | 🔵 低 | 极高 | 低 | 极高 | 后期考虑 |
| **Browser Auto** | 🔵 低 | 高 | 低 | 中 | 后期考虑 |

---

## 🚀 三阶段迁移计划

### 阶段1: 基础设施准备 (1-2周)

#### 1.1 依赖分析与安装
```bash
# 核心AI依赖
pnpm add @blocknote/core @blocknote/react @blocknote/mantine
pnpm add @google/genai @modelcontextprotocol/sdk
pnpm add react-markdown rehype-highlight rehype-raw remark-gfm

# UI组件依赖
pnpm add @radix-ui/react-dialog @radix-ui/react-progress
pnpm add @radix-ui/react-checkbox @radix-ui/react-label
pnpm add class-variance-authority tailwindcss-animate
pnpm add react-textarea-autosize tailwind-merge

# 工具库
pnpm add zod browser-use
```

#### 1.2 目录结构创建
```
web/browser/src/ai-modules/
├── chat/
│   ├── components/
│   ├── hooks/
│   └── types/
├── memory/
│   ├── components/
│   ├── hooks/
│   └── types/
├── notes/
│   ├── components/
│   ├── hooks/
│   └── types/
├── shared/
│   ├── components/
│   ├── hooks/
│   ├── utils/
│   └── types/
└── types/
    └── index.ts
```

#### 1.3 共享组件迁移
- [ ] UI组件库适配
- [ ] 工具函数迁移
- [ ] 类型定义统一
- [ ] 主题系统集成

### 阶段2: 核心功能迁移 (3-4周)

#### 2.1 Chat模块迁移 (第4周)
**目标**: 实现完整的AI对话功能

**迁移清单**:
- [ ] `ChatInterface.tsx` → `web/browser/src/ai-modules/chat/components/`
- [ ] `ChatFileUpload.tsx` → 文件上传功能集成
- [ ] `SystemPromptInput.tsx` → 系统提示词管理
- [ ] 创建Chat标签页类型
- [ ] 在AI工具栏中添加Chat按钮
- [ ] 实现固定标签页机制

**技术要点**:
- 适配浏览器标签页系统
- 集成Google Gemini API
- 实现消息历史存储
- 支持文件上传和处理

#### 2.2 Memory模块迁移 (第5周)
**目标**: 实现AI记忆管理功能

**迁移清单**:
- [ ] `MemoryManager.tsx` → 记忆管理界面
- [ ] `AIMemoryModal.tsx` → 记忆弹窗组件
- [ ] `RichTextEditor.tsx` → 富文本编辑器
- [ ] 数据库schema适配
- [ ] 记忆API服务集成

#### 2.3 Notes模块迁移 (第6周)
**目标**: 实现智能笔记功能

**迁移清单**:
- [ ] `NotesManager.tsx` → 笔记管理界面
- [ ] `BlockNoteEditor.tsx` → BlockNote编辑器集成
- [ ] `BlockNoteTheme.ts` → 主题适配
- [ ] 笔记存储和同步
- [ ] 笔记搜索功能

### 阶段3: 高级功能集成 (2-3周)

#### 3.1 后端服务集成 (第7周)
**目标**: 将Express API集成到Electron主进程

**技术方案**:
- Express服务器 → Electron IPC通信
- HTTP API → IPC消息传递
- 数据库连接统一管理
- 错误处理和日志系统

#### 3.2 其他模块迁移 (第8-9周)
**按需迁移**:
- [ ] Clipboard Manager - 剪贴板历史管理
- [ ] Knowledge Base - 知识库功能
- [ ] Prompt Manager - 提示词模板
- [ ] Theme Manager - 主题管理增强

---

## 🔧 技术挑战与解决方案

### 主要技术挑战

#### 1. 架构集成挑战
**问题**: 独立应用 → 浏览器集成
**解决方案**:
- AI功能作为特殊标签页类型
- 工具栏按钮快速访问
- 状态管理统一

#### 2. 服务通信挑战
**问题**: Express HTTP API → Electron IPC
**解决方案**:
- 设计统一的IPC消息格式
- 实现API路由到IPC的映射
- 保持异步调用模式

#### 3. UI适配挑战
**问题**: 独立窗口 → 标签页容器
**解决方案**:
- 组件容器化设计
- 适配标签页生命周期
- 响应式布局优化

#### 4. 数据存储挑战
**问题**: 数据库schema差异
**解决方案**:
- 统一使用LibSQL
- 设计兼容的schema
- 实现数据迁移脚本

### 关键技术决策

1. **AI功能集成方式**: 特殊标签页 + 工具栏按钮
2. **数据存储策略**: 统一LibSQL数据库
3. **通信机制**: Electron IPC替代HTTP API
4. **UI框架选择**: 保持React + TailwindCSS一致性
5. **状态管理**: 使用MobX保持一致性

---

## 📈 预期收益分析

### 功能收益
- 🤖 **完整AI对话**: 支持多轮对话、文件上传、系统提示词
- 🧠 **智能记忆**: AI记忆存储、检索和上下文感知
- 📝 **现代笔记**: BlockNote富文本编辑器，支持Markdown
- 📚 **知识管理**: 文档存储、搜索和智能问答
- 🔧 **工具集成**: 剪贴板管理、提示词模板等

### 技术收益
- **代码复用率**: > 80%
- **开发效率**: 提升3-5倍
- **功能完整度**: 显著提升
- **用户体验**: 大幅改善
- **维护成本**: 降低

### 性能收益
- **内存优化**: 集成架构减少资源占用
- **启动速度**: 统一进程提升启动效率
- **响应性能**: IPC通信优于HTTP请求

---

## 🎯 实施建议

### 立即行动项
1. **依赖分析**: 详细分析源项目依赖，制定安装计划
2. **目录创建**: 建立ai-modules目录结构
3. **Chat模块**: 优先迁移Chat功能验证可行性
4. **工具栏集成**: 在AI工具栏添加功能入口

### 风险控制
1. **渐进迁移**: 按模块逐步迁移，每个模块都可独立回滚
2. **功能验证**: 每个阶段完成后进行完整功能测试
3. **性能监控**: 实时监控内存和性能指标
4. **备份策略**: 迁移前创建完整代码备份

### 成功指标
- [ ] Chat功能完全可用
- [ ] Memory管理正常工作
- [ ] Notes编辑体验良好
- [ ] 整体性能无明显下降
- [ ] 用户体验保持一致

---

## 📋 下一步行动

### 即将开始的任务
1. **任务2.1**: 创建AI模块目录结构
2. **任务2.2**: 安装和配置必要依赖
3. **任务4.1**: 迁移Chat模块核心组件
4. **任务4.2**: 实现Chat标签页集成

### 长期规划
- 完成所有核心AI功能迁移
- 优化性能和用户体验
- 添加更多AI工具和功能
- 建立完善的测试体系

---

**文档维护**: 请在迁移过程中及时更新此文档，记录实际进展和遇到的问题。


请帮我从源工程迁移功能到当前工程，我们首先迁移Chat功能。 注意： 1.  是迁移，不是让你自己编写； 2. 注意迁移过来的文件存放的目录要合理， 服务端api和electron的文件尤为要注意； 3. 对话功能要放在程序启动的首页，固定tab页签栏（不可删除），就是说当前浏览器启动后，是对话页，新增页面的时候，再打开nwetab页面； 4. 除了必须的依赖，不要随便引入依赖。 先迁移文件，我们后面要用当前工程的依赖重换一下迁移过来的文件的图标和动画依赖； 5.  不要随意增删文件，不能理解的找我解释。