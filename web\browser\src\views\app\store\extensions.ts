/* eslint @typescript-eslint/camelcase: 0 */

import {action, makeObservable, observable} from 'mobx';

import {IBrowserAction} from '../models';
import store from '.';
import { extensionUtils, eventUtils } from '@browser/core/utils/platform-lite';

// 轻量级Extension接口，避免依赖Electron类型
interface LiteExtension {
  id: string;
  path: string;
  manifest: {
    name: string;
    icons?: { [size: string]: string };
    action?: {
      default_popup?: string;
      default_title?: string;
      default_icon?: string | { [size: string]: string };
    };
    browser_action?: {
      default_popup?: string;
      default_title?: string;
      default_icon?: string | { [size: string]: string };
    };
  };
}

export class ExtensionsStore {
  public browserActions: IBrowserAction[] = [];

  public defaultBrowserActions: IBrowserAction[] = [];

  public currentlyToggledPopup = '';

  public constructor() {
    makeObservable(this, {
      browserActions: observable,
      defaultBrowserActions: observable,
      currentlyToggledPopup: observable,
      uninstallExtension: action,
    });

    this.load();

    eventUtils.on('load-browserAction', async (e, extension) => {
      await this.loadExtension(extension);
    });
  }

  public addBrowserActionToTab(tabId: number, browserAction: IBrowserAction) {
    const tabBrowserAction: IBrowserAction = Object.assign(
      Object.create(Object.getPrototypeOf(browserAction)),
      browserAction,
    );
    tabBrowserAction.tabId = tabId;
    this.browserActions.push(tabBrowserAction);
  }

  private findDefaultIcon(extension: LiteExtension): string | null {
    const {icons} = extension.manifest;
    if (icons) {
      const size = ["128", "96", "64", "48", "32", "16"];
      for (let s of size) {
        if (icons[s]) {
          return icons[s];
        }
      }
    }
    return null;
  }

  public async loadExtension(extension: LiteExtension) {
    console.log(extension);
    if (this.defaultBrowserActions.find((x) => x.extensionId === extension.id))
      return;

    if (extension.manifest.browser_action) {
      const {default_icon, default_title} = extension.manifest.browser_action;

      let icon1 = default_icon;

      if (typeof icon1 === 'object' && default_icon && typeof default_icon === 'object') {
        icon1 = Object.values(default_icon)[
        Object.keys(default_icon).length - 1
          ];
      }
      if(!icon1) {
        icon1 = this.findDefaultIcon(extension) || '';
      }

      // 在轻量级环境中，我们不能直接读取文件系统
      // 这个功能需要通过Electron环境的IPC来实现
      const data = await eventUtils.invoke('extension-read-icon', extension.id, icon1);

      if (
        this.defaultBrowserActions.find((x) => x.extensionId === extension.id)
      )
        return;

      const icon = window.URL.createObjectURL(new Blob([data]));
      const browserAction = new IBrowserAction({
        extensionId: extension.id,
        icon,
        title: default_title || extension.manifest.name,
        popup: extension.manifest?.browser_action?.default_popup || '',
      });

      this.defaultBrowserActions.push(browserAction);

      for (const tab of store.tabs.list) {
        this.addBrowserActionToTab(tab.id, browserAction);
      }
    } else if (extension.manifest.action) {
      const {default_icon, default_title} = extension.manifest.action;
      let icon1 = default_icon;
      if (typeof icon1 === 'object' && default_icon && typeof default_icon === 'object') {
        icon1 = Object.values(default_icon)[
        Object.keys(default_icon).length - 1
          ];
      }
      if(!icon1) {
        icon1 = this.findDefaultIcon(extension) || '';
      }

      // 在轻量级环境中，我们不能直接读取文件系统
      // 这个功能需要通过Electron环境的IPC来实现
      const data = await eventUtils.invoke('extension-read-icon', extension.id, icon1);

      if (
        this.defaultBrowserActions.find((x) => x.extensionId === extension.id)
      )
        return;

      const icon = window.URL.createObjectURL(new Blob([data]));
      const browserAction = new IBrowserAction({
        extensionId: extension.id,
        icon,
        title: default_title || extension.manifest.name,
        popup: extension.manifest?.action?.default_popup || '',
      });

      this.defaultBrowserActions.push(browserAction);

      for (const tab of store.tabs.list) {
        this.addBrowserActionToTab(tab.id, browserAction);
      }
    }
  }

  public async load() {
    if (!process.env.ENABLE_EXTENSIONS) return;

    const extensions: LiteExtension[] = await eventUtils.invoke(
      'get-extensions',
    );

    if (extensions && Array.isArray(extensions)) {
      await Promise.all(extensions.map((x) => this.loadExtension(x)));
    }
  }

  uninstallExtension(id: string) {
    this.browserActions = this.browserActions.filter(
      (x) => x.extensionId !== id,
    );
    this.defaultBrowserActions = this.defaultBrowserActions.filter(
      (x) => x.extensionId !== id,
    );

    extensionUtils.getInvoker().uninstall(id);
  }
}
