import { eventUtils } from '@browser/core/utils/platform-lite';
import { makeObservable, observable } from 'mobx';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public videoUrls: string[] = [];
  public tabId: number = 0;

  public constructor() {
    super();

    makeObservable(this, {
      videoUrls: observable,
      tabId: observable,
    });

    eventUtils.on('data', async (e, data) => {
      const { videoUrls, tabId } = data;
      this.videoUrls = videoUrls;
      this.tabId = tabId;
    });
  }
}

export default new Store();
