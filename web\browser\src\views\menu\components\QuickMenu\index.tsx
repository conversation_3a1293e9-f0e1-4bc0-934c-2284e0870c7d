import { observer } from 'mobx-react-lite';

import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';
import { eventUtils, windowControls } from '@browser/core/utils/platform-lite';
import { Switch } from '@browser/core/components/Switch';
import {
  ICON_FIRE,
  ICON_TOPMOST,
  ICON_HISTORY,
  ICON_BOOKMARKS,
  ICON_SETTINGS,
  ICON_EXTENSIONS,
  ICON_DOWNLOAD,
  ICON_FIND,
  ICON_DASHBOARD,
} from '@mario-ai/shared';
import { getWebUIURL } from '@browser/core/utils/webui';

const onPrintClick = () => {
  eventUtils.send('open-dev-tool', null);
  store.hide();
};

const onFindInPageClick = () => {
  eventUtils.send(`find-in-page-${store.windowId}`);
  store.hide();
};

const onAlwaysClick = () => {
  store.alwaysOnTop = !store.alwaysOnTop;
  windowControls.setAlwaysOnTop(store.alwaysOnTop);
};

const addNewTab = (url: string) => {
  console.log('[QuickMenu] Adding new tab:', url, 'windowId:', store.windowId);
  eventUtils.send(`add-tab-${store.windowId}`, {
    url,
    active: true,
  });
  store.hide();
};

const goToWebUIPage = (name: string) => () => {
  console.log('[QuickMenu] Going to WebUI page:', name);
  const url = getWebUIURL(name);
  console.log('[QuickMenu] Generated URL:', url);
  addNewTab(url);
};

const showDownloadDialog = () => {
  console.log('[QuickMenu] Showing download dialog');
  eventUtils.send("dialog-visibility-change", 'downloads-dialog', true);
}

const goToURL = (url: string) => () => {
  addNewTab(url);
};

const onUpdateClick = () => {
  // 检查更新功能 - 应该打开更新页面或触发更新检查
  console.log('[QuickMenu] Update clicked, checking for updates...');

  // 发送更新检查事件
  eventUtils.send('update-check');

  // 可以打开更新页面或显示更新对话框
  // 这里暂时打开一个通用的更新页面，实际应该根据应用的更新机制来实现
  const updateUrl = 'https://github.com/mario-ai/mario-ai-browser/releases';
  addNewTab(updateUrl);
};

export const QuickMenu = observer(() => {
  const appName = windowControls.getAppName();

  // Content 样式 - 修正为与原始工程一致
  const contentClasses = cn(
    'flex flex-col relative'
  );

  // MenuItems 样式 - 修正为与原始工程一致
  // 参考工程: padding-top: 4px; padding-bottom: 4px;
  const menuItemsClasses = cn(
    'flex-1 overflow-hidden pt-1 pb-1',
    'bg-mario-dialog text-mario-dialog-text'
  );

  // MenuItem 样式 - 修正为与原始工程一致
  // 参考工程: height: 36px; padding: 0 12px; font-size: 12px;
  const menuItemClasses = cn(
    'h-9 flex items-center cursor-pointer px-3 relative',
    'text-xs transition-colors duration-150',
    // Hover 效果根据主题
    'hover:bg-black/[0.03] dark:hover:bg-white/[0.06]'
  );

  // Icon 样式 - 修正为与原始工程一致
  const iconClasses = cn(
    'w-5 h-5 mr-3 opacity-80 bg-center bg-no-repeat bg-contain',
    // 过滤器根据主题
    'dark:invert'
  );

  // MenuItemTitle 样式 - 修正为与原始工程一致
  const menuItemTitleClasses = cn(
    'flex-1 text-xs'
  );

  // Shortcut 样式 - 修正为与原始工程一致
  const shortcutClasses = cn(
    'text-xs opacity-54 mr-[18px]'
  );

  // RightControl 样式 - 修正为与原始工程一致
  const rightControlClasses = cn(
    'mr-[18px]'
  );

  // Line 样式 - 修正为与原始工程一致
  // 参考工程: margin-top: 4px; margin-bottom: 4px;
  const lineClasses = cn(
    'h-px w-full mt-1 mb-1',
    'bg-mario-dialog-separator'
  );

  return (
    <div
      style={{
        display: 'flex',
        flexFlow: 'column',
      }}
    >
      <div className={contentClasses}>
        <div className={menuItemsClasses}>
          {store.updateAvailable && (
            <>
              <div className={menuItemClasses} onClick={onUpdateClick}>
                <div
                  className={iconClasses}
                  style={{ backgroundImage: `url(${ICON_FIRE})` }}
                ></div>
                <div className={menuItemTitleClasses}>{appName}有新版本了</div>
              </div>
              <div className={lineClasses} />
            </>
          )}
          <div className={menuItemClasses} onClick={onAlwaysClick}>
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_TOPMOST})` }}
            />
            <div className={menuItemTitleClasses}>置顶窗口</div>
            <div className={rightControlClasses}>
              <Switch dense value={store.alwaysOnTop}></Switch>
            </div>
          </div>
          <div className={lineClasses} />
          <div className={menuItemClasses} onClick={goToWebUIPage('history')}>
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_HISTORY})` }}
            />
            <div className={menuItemTitleClasses}>历史记录</div>
          </div>
          <div className={menuItemClasses} onClick={goToWebUIPage('bookmarks')}>
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_BOOKMARKS})` }}
            />
            <div className={menuItemTitleClasses}>书签</div>
          </div>
          <div className={menuItemClasses} onClick={showDownloadDialog}>
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_DOWNLOAD})` }}
            />
            <div className={menuItemTitleClasses}>下载</div>
          </div>
          <div className={lineClasses} />
          <div className={menuItemClasses} onClick={goToWebUIPage('settings')}>
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_SETTINGS})` }}
            />
            <div className={menuItemTitleClasses}>设置</div>
          </div>
          <div
            className={menuItemClasses}
            onClick={goToURL(
              'https://microsoftedge.microsoft.com/addons/Microsoft-Edge-Extensions-Home?hl=zh-CN',
            )}
          >
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_EXTENSIONS})` }}
            />
            <div className={menuItemTitleClasses}>扩展程序</div>
          </div>
          <div
            className={menuItemClasses}
            onClick={goToURL(
              getWebUIURL('web.koodoreader.com/index.html'),
            )}
          >
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_EXTENSIONS})` }}
            />
            <div className={menuItemTitleClasses}>电子书阅读器</div>
          </div>
          <div className={lineClasses} />
          <div className={menuItemClasses} onClick={onFindInPageClick}>
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_FIND})` }}
            />
            <div className={menuItemTitleClasses}>页内查找</div>
            <div className={shortcutClasses}>Ctrl+F</div>
          </div>
          <div className={menuItemClasses} onClick={onPrintClick}>
            <div
              className={iconClasses}
              style={{ backgroundImage: `url(${ICON_DASHBOARD})` }}
            />
            <div className={menuItemTitleClasses}>开发者工具</div>
            <div className={shortcutClasses}>Ctrl+Shift+I</div>
          </div>
        </div>
      </div>
    </div>
  );
});
