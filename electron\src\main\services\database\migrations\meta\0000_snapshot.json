{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "5", "dialect": "sqlite", "tables": {"bookmarks": {"name": "bookmarks", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": false}, "title": {"autoincrement": false, "name": "title", "type": "text", "primaryKey": false, "notNull": true}, "url": {"autoincrement": false, "name": "url", "type": "text", "primaryKey": false, "notNull": false}, "favicon": {"autoincrement": false, "name": "favicon", "type": "text", "primaryKey": false, "notNull": false}, "is_folder": {"default": "(FALSE)", "autoincrement": false, "name": "is_folder", "type": "numeric", "primaryKey": false, "notNull": false}, "parent_id": {"autoincrement": false, "name": "parent_id", "type": "text", "primaryKey": false, "notNull": false}, "order_index": {"default": 0, "autoincrement": false, "name": "order_index", "type": "integer", "primaryKey": false, "notNull": false}, "static_type": {"autoincrement": false, "name": "static_type", "type": "text", "primaryKey": false, "notNull": false}, "expanded": {"default": "(FALSE)", "autoincrement": false, "name": "expanded", "type": "numeric", "primaryKey": false, "notNull": false}, "ai_tags": {"autoincrement": false, "name": "ai_tags", "type": "text", "primaryKey": false, "notNull": false}, "ai_summary": {"autoincrement": false, "name": "ai_summary", "type": "text", "primaryKey": false, "notNull": false}, "ai_category": {"autoincrement": false, "name": "ai_category", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_bookmarks_static": {"name": "idx_bookmarks_static", "columns": ["static_type"], "isUnique": false}, "idx_bookmarks_parent": {"name": "idx_bookmarks_parent", "columns": ["parent_id"], "isUnique": false}}, "foreignKeys": {"bookmarks_parent_id_bookmarks_id_fk": {"name": "bookmarks_parent_id_bookmarks_id_fk", "tableFrom": "bookmarks", "tableTo": "bookmarks", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "history": {"name": "history", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": false}, "url": {"autoincrement": false, "name": "url", "type": "text", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "text", "primaryKey": false, "notNull": false}, "visit_time": {"autoincrement": false, "name": "visit_time", "type": "integer", "primaryKey": false, "notNull": true}, "favicon": {"autoincrement": false, "name": "favicon", "type": "text", "primaryKey": false, "notNull": false}, "visit_count": {"default": 1, "autoincrement": false, "name": "visit_count", "type": "integer", "primaryKey": false, "notNull": false}, "ai_insights": {"autoincrement": false, "name": "ai_insights", "type": "text", "primaryKey": false, "notNull": false}, "ai_relevance_score": {"autoincrement": false, "name": "ai_relevance_score", "type": "real", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_history_visit_time": {"name": "idx_history_visit_time", "columns": ["visit_time"], "isUnique": false}, "idx_history_url": {"name": "idx_history_url", "columns": ["url"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "favicons": {"name": "favicons", "columns": {"url": {"autoincrement": false, "name": "url", "type": "text", "primaryKey": true, "notNull": false}, "data": {"autoincrement": false, "name": "data", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"default": "'image/png'", "autoincrement": false, "name": "mime_type", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "form_fill_data": {"name": "form_fill_data", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": false}, "type": {"autoincrement": false, "name": "type", "type": "text", "primaryKey": false, "notNull": true}, "url": {"autoincrement": false, "name": "url", "type": "text", "primaryKey": false, "notNull": true}, "favicon": {"autoincrement": false, "name": "favicon", "type": "text", "primaryKey": false, "notNull": false}, "fields": {"autoincrement": false, "name": "fields", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "startup_tabs": {"name": "startup_tabs", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": false}, "title": {"autoincrement": false, "name": "title", "type": "text", "primaryKey": false, "notNull": false}, "url": {"autoincrement": false, "name": "url", "type": "text", "primaryKey": false, "notNull": true}, "favicon": {"autoincrement": false, "name": "favicon", "type": "text", "primaryKey": false, "notNull": false}, "is_user_defined": {"default": "(FALSE)", "autoincrement": false, "name": "is_user_defined", "type": "numeric", "primaryKey": false, "notNull": false}, "order_index": {"default": 0, "autoincrement": false, "name": "order_index", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "integer", "primaryKey": false, "notNull": true}, "pinned": {"default": 0, "autoincrement": false, "name": "pinned", "type": "integer", "primaryKey": false, "notNull": false}, "window_id": {"default": 1, "autoincrement": false, "name": "window_id", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "permissions": {"name": "permissions", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": false}, "origin": {"autoincrement": false, "name": "origin", "type": "text", "primaryKey": false, "notNull": true}, "permission_type": {"autoincrement": false, "name": "permission_type", "type": "text", "primaryKey": false, "notNull": true}, "granted": {"default": "(FALSE)", "autoincrement": false, "name": "granted", "type": "numeric", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_permissions_origin": {"name": "idx_permissions_origin", "columns": ["origin"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "settings": {"name": "settings", "columns": {"key": {"autoincrement": false, "name": "key", "type": "text", "primaryKey": true, "notNull": false}, "value": {"autoincrement": false, "name": "value", "type": "text", "primaryKey": false, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}