import { BrowserWindow } from 'electron';
import { Application } from '@electron/main/core/application';
import { DIALOG_MARGIN_TOP, DIALOG_MARGIN } from '@electron/renderer/constants/design';
import { IBookmark } from '@electron/types';

export const showAddBookmarkDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
  data?: {
    url: string;
    title: string;
    bookmark?: IBookmark;
    favicon?: string;
  },
) => {
  console.log('[Main] showAddBookmarkDialog called with:', x, y, data);

  if (!data) {
    const selected = Application.instance.windows.current?.viewManager?.selected;
    if (!selected) {
      console.warn('[Main] No selected view available for bookmark dialog');
      return;
    }

    const {
      url,
      title,
      bookmark,
      favicon,
    } = selected;
    data = {
      url,
      title,
      bookmark,
      favicon,
    };
    console.log('[Main] Generated bookmark data:', data);
  }

  console.log('[Main] Creating bookmark dialog...');
  console.log('[Main] Application.instance:', !!Application.instance);
  console.log('[Main] Application.instance.dialogs:', !!Application.instance?.dialogs);

  const bounds = {
    width: 366,
    height: 240,
    x: x - 366 + DIALOG_MARGIN,
    y: y - DIALOG_MARGIN_TOP,
  };

  console.log('[Main] Bookmark dialog bounds:', bounds);
  console.log('[Main] Input coordinates:', { x, y });
  console.log('[Main] Constants:', { DIALOG_MARGIN, DIALOG_MARGIN_TOP });

  const dialog = Application.instance.dialogs.show({
    name: 'add-bookmark',
    browserWindow,
    getBounds: () => bounds,
    onWindowBoundsUpdate: () => dialog.hide(),
  });

  console.log('[Main] Bookmark dialog created:', dialog ? 'success' : 'failed');

  if (!dialog) return;

  dialog.on('loaded', (e) => {
    console.log('[Main] Dialog loaded event, sending data:', data);
    e.reply('data', data);
  });
};
