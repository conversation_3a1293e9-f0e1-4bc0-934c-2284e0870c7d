import { eventUtils } from '@browser/core/utils/platform-lite';
import { makeObservable, observable } from 'mobx';
import { IBookmark } from '@mario-ai/shared';
import * as React from 'react';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public titleRef = React.createRef<HTMLInputElement>();

  public bookmark: IBookmark;

  // Observable

  public folders: IBookmark[] = [];

  public dialogTitle = '';

  public currentFolder: IBookmark = null;

  public constructor() {
    super();

    console.log('[AddBookmark] Store constructor called');

    (async () => {
      try {
        // 检查是否在真正的Electron环境中（有require函数）
        const isElectron = typeof window !== 'undefined' && (window as any).require;

        if (isElectron) {
          console.log('[AddBookmark] Running in Electron environment, using real database');
          this.folders = await eventUtils.invoke('bookmarks-get-folders');
          if (this.folders && Array.isArray(this.folders)) {
            this.currentFolder = this.folders.find((x) => x.static === 'main');
            console.log('[AddBookmark] Real folders loaded:', this.folders.length);
          } else {
            console.warn('[AddBookmark] No folders returned, using empty array');
            this.folders = [];
            this.currentFolder = null;
          }
        } else {
          console.log('[AddBookmark] Running in browser environment, using mock folders');
          // 在浏览器环境中使用mock数据
          this.folders = [
            {
              _id: 'main-folder-mock',
              title: 'Bookmarks bar',
              static: 'main',
              isFolder: true,
              children: [],
              order: 0
            },
            {
              _id: 'other-folder-mock',
              title: 'Other bookmarks',
              static: 'other',
              isFolder: true,
              children: [],
              order: 1
            }
          ];
          this.currentFolder = this.folders.find((x) => x.static === 'main');
          console.log('[AddBookmark] Mock folders loaded:', this.folders.length);
        }
      } catch (error) {
        console.error('[AddBookmark] Error loading folders:', error);
        this.folders = [];
        this.currentFolder = null;
      }
    })();

    makeObservable(this, {
      folders: observable,
      dialogTitle: observable,
      currentFolder: observable,
    });

    // 发送loaded事件，通知主进程对话框已准备就绪
    console.log('[AddBookmark] Sending loaded event');
    // 检查是否在Electron环境中 - 修复环境检测逻辑
    const isElectron = typeof window !== 'undefined' && (window as any).require;
    console.log('[AddBookmark] Environment check - isElectron:', isElectron);

    if (isElectron) {
      // 在Electron环境中，发送带webContentsId的loaded事件
      try {
        const remote = (window as any).require('@electron/remote');
        if (remote && remote.getCurrentWebContents) {
          const id = remote.getCurrentWebContents().id;
          eventUtils.send(`loaded-${id}`);
          console.log('[AddBookmark] Sent loaded event with webContentsId:', id);
        } else {
          console.warn('[AddBookmark] Remote module not available');
        }
      } catch (error) {
        console.warn('[AddBookmark] Failed to send loaded event:', error);
      }
    } else {
      console.log('[AddBookmark] Not in Electron environment, skipping loaded event');
    }

    eventUtils.on('data', async (_, data) => {
      console.log('[AddBookmark] Received data event:', data);
      const { bookmark, title, url, favicon } = data;

      this.dialogTitle = bookmark ? '编辑书签' : '添加书签';

      this.bookmark = bookmark;

      // 显示对话框
      this.show();
      console.log('[AddBookmark] Dialog shown, visible:', this.visible);

      // 检查是否在真正的Electron环境中（有require函数）
      const isElectron = typeof window !== 'undefined' && (window as any).require;

      try {
        if (isElectron) {
          // 在Electron环境中，使用真实的数据库操作
          console.log('[AddBookmark] Using real database in Electron environment');
          this.folders = await eventUtils.invoke('bookmarks-get-folders');
          if (!this.folders || !Array.isArray(this.folders)) {
            this.folders = [];
          }
        } else {
          // 在浏览器环境中，使用mock数据
          console.log('[AddBookmark] Using mock folders in browser environment');
          if (!this.folders || this.folders.length === 0) {
            this.folders = [
              {
                _id: 'main-folder-mock',
                title: 'Bookmarks bar',
                static: 'main',
                isFolder: true,
                children: [],
                order: 0
              }
            ];
          }
        }
      } catch (error) {
        console.error('[AddBookmark] Error loading folders in data event:', error);
        this.folders = [];
      }

      if (!this.bookmark) {
        // 按照原始工程的逻辑，立即创建书签
        this.dialogTitle = '书签已添加';
        const mainFolder = this.folders.find((x) => x.static === 'main');
        console.log('[AddBookmark] Main folder found:', !!mainFolder);

        if (mainFolder) {
          if (isElectron) {
            // 在Electron环境中，创建真实的书签
            console.log('[AddBookmark] Creating real bookmark in Electron environment');
            try {
              this.bookmark = await eventUtils.invoke('bookmarks-add', {
                title,
                url,
                favicon,
                parent: mainFolder._id,
              });
              console.log('[AddBookmark] Real bookmark created:', this.bookmark);
            } catch (error) {
              console.error('[AddBookmark] Error creating bookmark:', error);
            }
          } else {
            // 在浏览器环境中创建mock书签
            console.log('[AddBookmark] Creating mock bookmark in browser environment');
            this.bookmark = {
              _id: 'mock-bookmark-' + Date.now(),
              title,
              url,
              favicon,
              parent: mainFolder._id,
              order: 0,
              isFolder: false
            };
            console.log('[AddBookmark] Mock bookmark created:', this.bookmark);
          }
        } else {
          console.warn('[AddBookmark] No main folder found, cannot create bookmark');
          this.dialogTitle = '无法添加书签';
        }
      } else {
        this.dialogTitle = '编辑书签';
        console.log('[AddBookmark] Editing existing bookmark:', this.bookmark);
      }

      if (this.bookmark && this.bookmark.parent) {
        this.currentFolder = this.folders.find(
          (x) => x._id === this.bookmark.parent,
        );
      } else {
        this.currentFolder = this.folders.find((x) => x.static === 'main') || null;
      }

      if (this.titleRef.current) {
        this.titleRef.current.value = title;
        this.titleRef.current.focus();
        this.titleRef.current.select();
      }
    });
  }
}

export default new Store();
