import { useState, useEffect, useRef } from 'react';
import { OptimizedThemeManager } from '@browser/core/utils/optimized-theme-manager';

/**
 * 优化的主题Hook - 解决主题切换延迟问题
 */
export const useOptimizedTheme = () => {
  const [currentTheme, setCurrentTheme] = useState(OptimizedThemeManager.getCurrentTheme());
  const [isDark, setIsDark] = useState(OptimizedThemeManager.isDarkTheme());
  const isInitializedRef = useRef(false);

  useEffect(() => {
    // 只在第一次挂载时初始化
    if (!isInitializedRef.current) {
      OptimizedThemeManager.initialize();
      isInitializedRef.current = true;
    }

    const handleThemeChange = (event: CustomEvent) => {
      const newTheme = event.detail.theme;
      setCurrentTheme(newTheme);
      setIsDark(OptimizedThemeManager.isDarkTheme());
    };

    // 使用passive listener提高性能
    window.addEventListener('theme-changed', handleThemeChange as EventListener, { passive: true });
    
    return () => {
      window.removeEventListener('theme-changed', handleThemeChange as EventListener);
    };
  }, []);

  const setTheme = (themeName: string) => {
    OptimizedThemeManager.setTheme(themeName);
  };

  const setThemeWithAuto = (themeName: string, isAuto: boolean = false) => {
    OptimizedThemeManager.setThemeWithAuto(themeName, isAuto);
  };

  return {
    currentTheme,
    isDark,
    setTheme,
    setThemeWithAuto,
    isInitialized: isInitializedRef.current
  };
};

/**
 * 全局主题初始化Hook - 只在App组件中使用
 */
export const useGlobalThemeInit = (initialTheme: string, isAuto: boolean = false) => {
  const isInitializedRef = useRef(false);

  useEffect(() => {
    if (!isInitializedRef.current) {
      console.log('[useGlobalThemeInit] Initializing global theme system');
      OptimizedThemeManager.initialize();
      OptimizedThemeManager.setThemeWithAuto(initialTheme, isAuto);
      isInitializedRef.current = true;
    }
  }, []);

  useEffect(() => {
    if (isInitializedRef.current) {
      console.log('[useGlobalThemeInit] Theme changed:', initialTheme);
      OptimizedThemeManager.setThemeWithAuto(initialTheme, isAuto);
    }
  }, [initialTheme, isAuto]);

  return isInitializedRef.current;
};
