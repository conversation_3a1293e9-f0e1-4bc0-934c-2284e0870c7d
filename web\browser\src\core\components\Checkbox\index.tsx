import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { EASING_FUNCTION, BLUE_500, ICON_CHECK, transparency } from '@mario-ai/shared';

interface Props {
  children?: any;
}

interface State {
  toggled: boolean;
}

export default class Checkbox extends React.PureComponent<Props, State> {
  public state: State = {
    toggled: false,
  };

  private onClick = () => {
    this.value = !this.value;
  };

  public get value() {
    const { toggled } = this.state;
    return toggled;
  }

  public set value(toggled: boolean) {
    this.setState({ toggled });
  }

  render() {
    const { children } = this.props;
    const { toggled } = this.state;

    // 容器样式
    const containerClasses = cn(
      'h-10 flex items-center justify-start cursor-pointer',
      // Hover 效果通过 group 实现
      'group'
    );

    // Checkbox 样式
    const checkboxClasses = cn(
      'w-[18px] h-[18px] box-border relative rounded-[3px] border-2 border-solid',
      'transition-all duration-150',
      // 根据选中状态设置背景色和边框色
      toggled 
        ? 'border-[#2196F3]' // BLUE_500
        : 'border-black border-opacity-54',
      // 伪元素 hover 效果
      'before:content-[""] before:w-0 before:h-0 before:rounded-full before:block',
      'before:absolute before:pointer-events-none before:opacity-0',
      'before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2',
      'before:transition-all before:duration-100',
      // Group hover 效果
      'group-hover:before:w-10 group-hover:before:h-10 group-hover:before:opacity-8'
    );

    // Checkbox 动态样式
    const checkboxStyle: React.CSSProperties = {
      backgroundColor: toggled ? BLUE_500 : 'transparent',
      borderColor: toggled ? BLUE_500 : 'rgba(0, 0, 0, 0.54)',
    };

    // 伪元素样式
    const beforeStyle: React.CSSProperties = {
      backgroundColor: toggled ? BLUE_500 : '#000',
      transitionTimingFunction: EASING_FUNCTION,
    };

    // 图标样式
    const iconClasses = cn(
      'w-full h-full absolute top-0 left-0 transition-all duration-300',
      'bg-center bg-no-repeat',
      // 图标反色
      'invert'
    );

    const iconStyle: React.CSSProperties = {
      backgroundImage: `url(${ICON_CHECK})`,
      backgroundSize: '22px',
      clipPath: toggled ? 'inset(0 0 0 0)' : 'inset(100% 50% 0 50%)',
      transitionTimingFunction: EASING_FUNCTION,
      WebkitFontSmoothing: 'antialiased',
    };

    // 标签样式
    const labelClasses = cn(
      'text-sm ml-2 font-roboto font-normal'
    );

    const labelStyle: React.CSSProperties = {
      color: `rgba(0, 0, 0, ${transparency.text.high})`,
    };

    return (
      <div className={containerClasses}>
        <div
          className={cn(checkboxClasses, 'checkbox')}
          onClick={this.onClick}
          style={{
            ...checkboxStyle,
            // 伪元素样式通过 CSS 变量传递
            '--before-bg': toggled ? BLUE_500 : '#000',
          } as React.CSSProperties & { '--before-bg': string }}
        >
          {/* 伪元素 hover 效果 */}
          <div 
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-0 h-0 rounded-full opacity-0 pointer-events-none transition-all duration-100 group-hover:w-10 group-hover:h-10 group-hover:opacity-8"
            style={{ backgroundColor: toggled ? BLUE_500 : '#000', transitionTimingFunction: EASING_FUNCTION }}
          />
          
          {/* 勾选图标 */}
          <div
            className={iconClasses}
            style={iconStyle}
          />
        </div>
        
        {children && (
          <div className={labelClasses} style={labelStyle}>
            {children}
          </div>
        )}
      </div>
    );
  }
}
