import React, { useState, useEffect } from 'react';

interface SystemPromptInputProps {
  currentPrompt: string;
  onPromptChange: (newPrompt: string) => void;
}

const SystemPromptInput: React.FC<SystemPromptInputProps> = ({ currentPrompt, onPromptChange }) => {
  const [prompt, setPrompt] = useState(currentPrompt);

  useEffect(() => {
    setPrompt(currentPrompt);
  }, [currentPrompt]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value);
  };

  const handleApply = () => {
    onPromptChange(prompt);
  };

  return (
    <div className="mb-6">
      <label htmlFor="system-prompt" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-1">
        System Prompt
      </label>
      <textarea
        id="system-prompt"
        rows={4}
        value={prompt}
        onChange={handleChange}
        className="w-full p-2 border border-secondary-300 dark:border-secondary-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-secondary-700 text-secondary-800 dark:text-secondary-200 resize-none scrollbar-thin scrollbar-thumb-secondary-300 dark:scrollbar-thumb-secondary-700"
        placeholder="e.g., You are a pirate assistant."
      />
      <button
        onClick={handleApply}
        className="mt-2 w-full px-4 py-2 bg-secondary-600 hover:bg-secondary-700 text-white dark:bg-secondary-500 dark:hover:bg-secondary-600 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary-500 dark:focus:ring-offset-secondary-800"
      >
        Apply System Prompt
      </button>
      <p className="mt-1 text-xs text-secondary-500 dark:text-secondary-400">
        Note: Applying a new system prompt will start a new chat session.
      </p>
    </div>
  );
};

export default SystemPromptInput;
