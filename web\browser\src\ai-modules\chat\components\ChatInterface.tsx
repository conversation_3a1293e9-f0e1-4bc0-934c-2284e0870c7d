import type React from "react"
import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react"
// 移除外部图标库依赖，使用简单的文本图标

// 使用当前项目的组件
import { But<PERSON> } from '../../shared/components/button'
import { ScrollArea } from "../../shared/components/scroll-area"
import { Textarea } from "../../shared/components/textarea"
import { SuggestedQuestions } from "../../shared/components/suggested-questions"
import { ModelSelector } from "../../shared/components/model-selector"
import MarkdownRenderer from "../../shared/components/MarkdownRenderer"
import TypewriterText from "../../shared/components/TypewriterText"
import ChatFileUpload from "./ChatFileUpload"
import { cn } from '../../shared/lib/utils'
import { useChatHistory } from '../../shared/contexts/ChatHistoryContext'
import { getResponse, getStreamingResponse, AVAILABLE_MODELS } from "../../shared/lib/aliyun"
import { getLogoUrl } from '../../shared/utils/resource-url'
import { mem0Service } from '../../shared/services/mem0-service'
import { debugLogger, logMemoryOperation, logLLMRequest, logContextBuilding, logStateChange } from '../../shared/utils/debug-logger'

// Use Message type from context
import type { Message } from '../../shared/contexts/ChatHistoryContext'

interface ChatInterfaceProps {
  apiKey?: string
  endpoint?: string
  onNewChat?: () => void
  conversationRounds?: number
}

type ChatSession = {
  id: string
  title: string
  messages: Message[]
  timestamp: Date
}

const ChatInterface = forwardRef<{ createNewChat: () => void }, ChatInterfaceProps>(({ apiKey: propApiKey, endpoint: propEndpoint, onNewChat, conversationRounds = 6 }, ref) => {
  const { currentMessages, addMessage, updateMessage, createNewChat: createNewChatContext } = useChatHistory()
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [selectedModel, setSelectedModel] = useState("qwen-plus")
  const [collapsedThinking, setCollapsedThinking] = useState<Set<number>>(new Set())
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [mem0Available, setMem0Available] = useState(false)

  const [memoryModeEnabled, setMemoryModeEnabled] = useState(() => {
    // Load memory mode state from localStorage
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('memory_aware_mode');
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  })
  const [relevantMemories, setRelevantMemories] = useState<any[]>([])
  const [memorySearching, setMemorySearching] = useState(false)

  // Save memory mode state to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('memory_aware_mode', JSON.stringify(memoryModeEnabled));

      // Enhanced debugging for memory mode state changes
      logStateChange('memory_mode', {
        enabled: memoryModeEnabled,
        mem0Available,
        timestamp: new Date().toISOString(),
        localStorage: localStorage.getItem('memory_aware_mode')
      });

      console.log('🧠 Memory mode state saved to localStorage:', memoryModeEnabled);
      debugLogger.printUtf8Debug(); // Print UTF-8 debug info on state changes
    }
  }, [memoryModeEnabled]);

  // 使用传入的API key，如果没有传入则从localStorage读取
  const apiKey = propApiKey || (typeof window !== 'undefined' ? localStorage.getItem('aliyun_api_key') || '' : '')

  // Initialize mem0 service and check availability
  useEffect(() => {
    const initializeMem0 = async () => {
      try {
        await mem0Service.initialize();

        // Set API key if available
        if (apiKey) {
          await mem0Service.setApiKey(apiKey);
        }

        const availability = await mem0Service.isServiceAvailable();
        setMem0Available(availability.available);
        console.log('🧠 Mem0 service availability check:', {
          available: availability.available,
          success: availability.success,
          status: availability.status,
          error: availability.error,
          apiKey: apiKey ? `${apiKey.substring(0, 12)}...` : 'Not set'
        });
      } catch (error) {
        console.error('Failed to initialize mem0 service:', error);
        setMem0Available(false);
      }
    };

    initializeMem0();
  }, [apiKey]); // Re-run when API key changes

  // Build conversation context for API requests
  const buildConversationContext = (currentMessages: Message[]): string => {
    if (currentMessages.length === 0) return ''

    // Get the last N rounds of conversation (user + assistant pairs)
    const rounds = Math.min(conversationRounds, Math.floor(currentMessages.length / 2))
    const contextMessages = currentMessages.slice(-rounds * 2)

    let context = ''
    for (let i = 0; i < contextMessages.length; i += 2) {
      const userMsg = contextMessages[i]
      const assistantMsg = contextMessages[i + 1]

      if (userMsg && userMsg.role === 'user') {
        context += `用户: ${userMsg.content}\n`
      }
      if (assistantMsg && assistantMsg.role === 'assistant') {
        context += `助手: ${assistantMsg.content}\n`
      }
    }

    return context.trim()
  }

  // Expose createNewChat method to parent component
  useImperativeHandle(ref, () => ({
    createNewChat
  }))
  const endpoint = propEndpoint || (typeof window !== 'undefined' ? localStorage.getItem('aliyun_endpoint') || '' : '')

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [textareaFocused, setTextareaFocused] = useState(false)

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [currentMessages])

  // Focus management for textarea
  useEffect(() => {
    const handleFocusRecovery = () => {
      if (textareaRef.current && !textareaFocused) {
        console.log('🎯 Recovering textarea focus after modal interaction');
        setTimeout(() => {
          textareaRef.current?.focus();
        }, 100);
      }
    };

    // Listen for modal close events and recover focus
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleFocusRecovery();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [textareaFocused]);

  // Handle textarea focus/blur events
  const handleTextareaFocus = () => {
    console.log('🎯 Textarea focused');
    setTextareaFocused(true);
  };

  const handleTextareaBlur = () => {
    console.log('🎯 Textarea blurred');
    setTextareaFocused(false);
  };

  const createNewChat = () => {
    createNewChatContext()
    setInput("")
    setUploadedFiles([])
    setRelevantMemories([])
    onNewChat?.()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const toggleThinking = (messageIndex: number) => {
    setCollapsedThinking(prev => {
      const newSet = new Set(prev)
      if (newSet.has(messageIndex)) {
        newSet.delete(messageIndex)
      } else {
        newSet.add(messageIndex)
      }
      return newSet
    });
  };

  const handleSend = async () => {
    if (!input.trim() && uploadedFiles.length === 0) return

    // 检查API Key
    if (!apiKey) {
      alert('请先设置阿里云API Key')
      return
    }

    // Prepare message content with files
    let messageContent = input;
    if (uploadedFiles.length > 0) {
      const fileList = uploadedFiles.map(f => `📎 ${f.name}`).join('\n');
      messageContent = `${input}\n\n附件:\n${fileList}`;
    }

    // Add user message
    const userMessage = {
      id: `msg-${Date.now()}-user`,
      role: "user" as const,
      content: messageContent,
      timestamp: new Date(),
    }
    addMessage(userMessage)
    setInput("")
    setUploadedFiles([])

    // 调用阿里云模型
    setIsLoading(true)

    // Create assistant message placeholder for streaming
    const assistantMessageId = `msg-${Date.now()}-assistant`;
    const assistantMessage = {
      id: assistantMessageId,
      role: "assistant" as const,
      content: "",
      timestamp: new Date(),
      thinking: "正在思考中...",
      isStreaming: true,
    }
    addMessage(assistantMessage)

    // Track accumulated content for streaming
    let accumulatedContent = "";

    try {
      // TODO: Implement the rest of the handleSend logic
      // This is a placeholder for now
      console.log('Sending message:', messageContent);
      
      // Simulate response for now
      setTimeout(() => {
        updateMessage(assistantMessageId, {
          content: "这是一个测试回复。Chat功能正在迁移中...",
          thinking: undefined,
          isStreaming: false,
        });
        setIsLoading(false);
      }, 1000);

    } catch (error) {
      console.error('Error sending message:', error);
      updateMessage(assistantMessageId, {
        content: "抱歉，发生了错误。请稍后重试。",
        thinking: undefined,
        isStreaming: false,
      });
      setIsLoading(false);
    }
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Chat messages area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4 max-w-4xl mx-auto">
          {currentMessages.length === 0 ? (
            <div className="text-center py-8">
              <h2 className="text-2xl font-semibold mb-4">开始对话</h2>
              <p className="text-muted-foreground">输入您的问题开始与AI助手对话</p>
            </div>
          ) : (
            currentMessages.map((message, index) => (
              <div key={message.id} className="space-y-2">
                <div className={cn(
                  "flex gap-3",
                  message.role === "user" ? "justify-end" : "justify-start"
                )}>
                  {message.role === "assistant" && (
                    <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                      <span className="text-primary-foreground">🤖</span>
                    </div>
                  )}
                  
                  <div className={cn(
                    "max-w-[80%] rounded-lg p-3",
                    message.role === "user" 
                      ? "bg-primary text-primary-foreground" 
                      : "bg-muted"
                  )}>
                    {message.role === "user" ? (
                      <p className="whitespace-pre-wrap">{message.content}</p>
                    ) : (
                      <div>
                        {message.thinking && (
                          <div className="mb-2 text-sm text-muted-foreground">
                            {message.thinking}
                          </div>
                        )}
                        {message.content && (
                          <div className="prose prose-sm max-w-none">
                            {message.isStreaming ? (
                              <TypewriterText text={message.content} />
                            ) : (
                              <MarkdownRenderer content={message.content} />
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {message.role === "user" && (
                    <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                      <span>👤</span>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input area */}
      <div className="border-t p-4">
        <div className="max-w-4xl mx-auto space-y-4">
          {/* File upload */}
          <ChatFileUpload 
            onFilesSelected={setUploadedFiles}
            className="max-w-md"
          />

          {/* Message input */}
          <div className="flex gap-2">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={handleTextareaFocus}
              onBlur={handleTextareaBlur}
              placeholder="输入您的消息..."
              className="flex-1 min-h-[60px] max-h-[200px] resize-none"
              disabled={isLoading}
            />
            <Button
              size="icon"
              className="h-[60px] w-[60px] rounded-full"
              disabled={isLoading || (!input.trim() && uploadedFiles.length === 0)}
              onClick={handleSend}
            >
              {isLoading ? (
                <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <span>📤</span>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
})

ChatInterface.displayName = 'ChatInterface'

export default ChatInterface
