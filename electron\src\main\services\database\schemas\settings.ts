import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const settings = sqliteTable('settings', {
  key: text('key').primaryKey(),
  value: text('value').notNull(), // JSON value
  type: text('type').notNull(), // 'string', 'number', 'boolean', 'object'
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull()
});

export type Setting = typeof settings.$inferSelect;
export type NewSetting = typeof settings.$inferInsert;
