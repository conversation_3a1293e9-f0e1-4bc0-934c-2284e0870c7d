import * as React from 'react';

import { IFormFillData } from '@mario-ai/shared';
import store from '../../../store';
import { cn } from '@browser/utils/tailwind-helpers';
import { transparency, ICON_DROPDOWN } from '@mario-ai/shared';

interface Props {
  label: string;
  icon: any;
  children?: any;
  style?: any;
}

export const onMoreClick = (data: IFormFillData) => (
  e: React.MouseEvent<HTMLDivElement>,
) => {
  e.stopPropagation();

  const { left, top } = e.currentTarget.getBoundingClientRect();

  store.autoFill.selectedItem = data;
  store.autoFill.menuTop = top;
  store.autoFill.menuLeft = left;
  store.autoFill.menuVisible = true;
};

export const Section = (props: Props) => {
  const { label, icon, children, style } = props;
  const [expanded, setExpanded] = React.useState(false);

  const onClick = () => {
    setExpanded(!expanded);
  };

  // StyledSection 样式 - Tailwind 版本
  const sectionClasses = cn(
    'w-full rounded mt-3',
    // 背景色根据主题
    store.theme['pages.lightForeground']
      ? 'bg-white/4'
      : 'bg-black/4'
  );

  // Header 样式 - Tailwind 版本
  const headerClasses = cn(
    'w-full h-12 flex items-center cursor-pointer'
  );

  // Icon 样式 - Tailwind 版本
  const iconClasses = cn(
    'w-[18px] h-[18px] ml-4 bg-center bg-no-repeat bg-contain',
    `opacity-[${transparency.icons.inactive}]`,
    // 过滤器根据主题
    store.theme['pages.lightForeground'] ? 'invert' : ''
  );

  // Label 样式 - Tailwind 版本
  const labelClasses = cn(
    'ml-4 text-sm'
  );

  // DropIcon 样式 - Tailwind 版本
  const dropIconClasses = cn(
    'w-5 h-5 bg-center bg-no-repeat bg-contain ml-auto mr-4',
    `opacity-[${transparency.icons.inactive}]`,
    'transition-transform duration-200',
    // 旋转根据展开状态
    expanded ? 'rotate-180' : 'rotate-0',
    // 过滤器根据主题
    store.theme['pages.lightForeground'] ? 'invert' : ''
  );

  // Container 样式 - Tailwind 版本
  const containerClasses = cn(
    'w-full overflow-hidden transition-all duration-200',
    // 高度根据展开状态
    expanded ? 'max-h-[1000px]' : 'max-h-0'
  );

  return (
    <div className={sectionClasses}>
      <div className={headerClasses} onClick={onClick}>
        <div
          className={iconClasses}
          style={{ backgroundImage: `url(${icon})` }}
        />
        <div className={labelClasses}>{label}</div>
        <div
          className={dropIconClasses}
          style={{ backgroundImage: `url(${ICON_DROPDOWN})` }}
        />
      </div>
      <div className={containerClasses} style={style}>
        {children}
      </div>
    </div>
  );
};
