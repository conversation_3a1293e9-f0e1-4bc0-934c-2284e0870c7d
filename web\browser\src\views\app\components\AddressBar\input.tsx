import { useRef, ChangeEvent, FocusEvent, forwardRef, Ref } from 'react';
import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  visible?: boolean;
}

export const MyInput = forwardRef((props: InputProps, ref: Ref<HTMLInputElement>) => {
  const el = useRef<HTMLInputElement>(null);
  const { onChange, onFocus, onBlur, value, defaultValue, visible = true, ...attrs } = props;

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e);
    }
  };

  let inputing = false;

  // Input 样式 - Tailwind 版本
  const inputClasses = cn(
    'outline-none min-w-0 w-full h-full bg-transparent border-none p-0 m-0',
    'text-black font-inherit text-sm relative z-[2]',
    // 可见性控制
    visible ? 'text-inherit' : 'text-transparent',
    // placeholder 样式
    store.theme['searchBox.lightForeground']
      ? 'placeholder:text-white/54'
      : 'placeholder:text-black/54'
  );

  return (
    <input
      {...attrs}
      className={inputClasses}
      value={value}
      ref={(input) => {
        el.current = input;
        if (ref) {
          if (typeof ref === 'function') {
            ref(input);
          } else {
            ref.current = input;
          }
        }

        // 添加原生input事件监听器作为备用
        if (input) {
          const nativeInputHandler = (e: Event) => {
            console.log('[MyInput] Native input event:', (e.target as HTMLInputElement).value);
            if (onChange) {
              // 创建一个合成事件
              const syntheticEvent = {
                target: e.target,
                currentTarget: e.target,
                preventDefault: () => {},
                stopPropagation: () => {}
              } as any;
              onChange(syntheticEvent);
            }
          };

          input.addEventListener('input', nativeInputHandler);

          // 清理函数
          return () => {
            input.removeEventListener('input', nativeInputHandler);
          };
        }
      }}
      onFocus={(e) => {
        onFocus && onFocus(e);
      }}
      onBlur={(e) => {
        onBlur && onBlur(e);
      }}
      onCompositionStart={() => {
        inputing = true;
      }}
      onCompositionEnd={(e) => {
        inputing = false;
        handleChange(e);
      }}
      onChange={(e) => {
        if (!inputing) {
          handleChange(e);
        }
      }}
    />
  );
});

export default MyInput;