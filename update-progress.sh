#!/bin/bash

# 简单的进度更新脚本
# 使用方法: ./update-progress.sh [task-id] [status] [notes]

PROGRESS_FILE="MIGRATION_PROGRESS.md"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 显示帮助
show_help() {
    echo "MarioAI 迁移进度更新工具"
    echo ""
    echo "使用方法:"
    echo "  ./update-progress.sh complete 1.1 [备注]     # 完成任务"
    echo "  ./update-progress.sh start 1.2 [备注]       # 开始任务"
    echo "  ./update-progress.sh problem 1.1 [问题描述] # 记录问题"
    echo "  ./update-progress.sh log [日志内容]         # 添加工作日志"
    echo "  ./update-progress.sh status                 # 查看当前状态"
    echo ""
    echo "示例:"
    echo "  ./update-progress.sh complete 1.1 '目录结构创建完成'"
    echo "  ./update-progress.sh start 1.2 '开始配置workspace'"
    echo "  ./update-progress.sh problem 1.1 '依赖安装失败'"
    echo "  ./update-progress.sh log '今天完成了基础架构搭建'"
}

# 更新任务状态
update_task() {
    local action=$1
    local task_id=$2
    local notes=$3
    local timestamp=$(date '+%Y-%m-%d %H:%M')
    
    if [ ! -f "$PROGRESS_FILE" ]; then
        echo -e "${RED}错误: 进度文件不存在${NC}"
        exit 1
    fi
    
    case $action in
        "complete")
            # 将 [ ] 改为 [x]，更新状态为已完成
            sed -i.bak "s/- \[ \] \*\*${task_id}\*\*/- [x] **${task_id}**/" "$PROGRESS_FILE"
            sed -i.bak "s/状态: 未开始/状态: ✅ 已完成 (${timestamp})/" "$PROGRESS_FILE"
            sed -i.bak "s/状态: 进行中/状态: ✅ 已完成 (${timestamp})/" "$PROGRESS_FILE"
            
            # 添加到工作日志
            add_log "✅ 完成任务 ${task_id}: ${notes}"
            
            echo -e "${GREEN}✅ 任务 ${task_id} 已标记为完成${NC}"
            ;;
            
        "start")
            # 更新状态为进行中
            sed -i.bak "s/状态: 未开始/状态: 🔄 进行中 (${timestamp})/" "$PROGRESS_FILE"
            
            # 添加到工作日志
            add_log "🔄 开始任务 ${task_id}: ${notes}"
            
            echo -e "${YELLOW}🔄 任务 ${task_id} 已标记为进行中${NC}"
            ;;
            
        "problem")
            # 添加到问题记录
            add_problem "${task_id}: ${notes}"
            
            echo -e "${RED}⚠️ 已记录问题: ${task_id}${NC}"
            ;;
    esac
    
    # 删除备份文件
    rm -f "${PROGRESS_FILE}.bak"
}

# 添加工作日志
add_log() {
    local log_entry=$1
    local timestamp=$(date '+%Y-%m-%d %H:%M')
    
    # 在工作日志部分添加新条目
    sed -i.bak "/^### $(date '+%Y-%m-%d')$/a\\
- ${timestamp}: ${log_entry}
" "$PROGRESS_FILE"
    
    # 如果今天的日期不存在，创建新的日期条目
    if ! grep -q "^### $(date '+%Y-%m-%d')$" "$PROGRESS_FILE"; then
        sed -i.bak "/^## 📝 工作日志$/a\\
\\
### $(date '+%Y-%m-%d')\\
- ${timestamp}: ${log_entry}
" "$PROGRESS_FILE"
    fi
    
    rm -f "${PROGRESS_FILE}.bak"
}

# 添加问题记录
add_problem() {
    local problem=$1
    local timestamp=$(date '+%Y-%m-%d %H:%M')
    
    # 在当前问题部分添加
    sed -i.bak "/^### 当前问题$/a\\
- [${timestamp}] ${problem}
" "$PROGRESS_FILE"
    
    rm -f "${PROGRESS_FILE}.bak"
}

# 查看当前状态
show_status() {
    if [ ! -f "$PROGRESS_FILE" ]; then
        echo -e "${RED}错误: 进度文件不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}=== 当前进度状态 ===${NC}"
    echo ""
    
    # 统计完成的任务
    local total_tasks=$(grep -c "^- \[.\] \*\*[0-9]" "$PROGRESS_FILE")
    local completed_tasks=$(grep -c "^- \[x\] \*\*[0-9]" "$PROGRESS_FILE")
    local progress=$((completed_tasks * 100 / total_tasks))
    
    echo "📊 总体进度: ${completed_tasks}/${total_tasks} (${progress}%)"
    echo ""
    
    # 显示进行中的任务
    echo -e "${YELLOW}🔄 进行中的任务:${NC}"
    grep -A 2 "状态: 🔄 进行中" "$PROGRESS_FILE" | grep "^- \[ \]" || echo "  无"
    echo ""
    
    # 显示下一个可开始的任务
    echo -e "${GREEN}📋 下一个可开始的任务:${NC}"
    grep -A 2 "状态: 未开始" "$PROGRESS_FILE" | grep "^- \[ \]" | head -1 || echo "  无"
    echo ""
    
    # 显示当前问题
    echo -e "${RED}⚠️ 当前问题:${NC}"
    sed -n '/^### 当前问题$/,/^### /p' "$PROGRESS_FILE" | grep "^- \[" || echo "  无"
}

# 主函数
main() {
    local command=$1
    
    case $command in
        "complete")
            if [ -z "$2" ]; then
                echo -e "${RED}错误: 请提供任务ID${NC}"
                show_help
                exit 1
            fi
            update_task "complete" "$2" "$3"
            ;;
        "start")
            if [ -z "$2" ]; then
                echo -e "${RED}错误: 请提供任务ID${NC}"
                show_help
                exit 1
            fi
            update_task "start" "$2" "$3"
            ;;
        "problem")
            if [ -z "$2" ]; then
                echo -e "${RED}错误: 请提供任务ID${NC}"
                show_help
                exit 1
            fi
            update_task "problem" "$2" "$3"
            ;;
        "log")
            if [ -z "$2" ]; then
                echo -e "${RED}错误: 请提供日志内容${NC}"
                show_help
                exit 1
            fi
            add_log "$2"
            echo -e "${GREEN}✅ 日志已添加${NC}"
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$command'${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
