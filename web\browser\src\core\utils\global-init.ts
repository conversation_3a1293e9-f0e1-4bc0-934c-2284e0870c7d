/**
 * 轻量级全局初始化工具
 * 提供平台无关的全局初始化能力
 */

// 引入 Tailwind CSS 样式
import '@browser/styles/tailwind-theme.css';

/**
 * 检查当前运行环境
 */
const isElectronEnvironment = (): boolean => {
  return typeof window !== 'undefined' && 
         (window as any).require !== undefined &&
         process?.versions?.electron !== undefined;
};

/**
 * 轻量级全局初始化函数
 * 在Electron环境中加载原有的renderer-process初始化
 * 在浏览器环境中进行必要的Web环境初始化
 */
export const initializeGlobal = () => {
  const isElectron = isElectronEnvironment();
  
  if (isElectron) {
    // Electron环境：动态加载原有的renderer-process初始化
    try {
      // 动态导入electron的renderer-process初始化
      const electronRenderer = (window as any).require('@electron/shared/renderer-config');
      console.log('[Global-Init] Electron renderer initialized');
    } catch (error) {
      console.warn('[Global-Init] Failed to load electron renderer:', error);
    }
  } else {
    // 浏览器环境：进行Web环境的初始化
    initializeBrowserEnvironment();
  }
};

/**
 * 浏览器环境初始化
 * 设置必要的全局变量和环境配置
 */
const initializeBrowserEnvironment = () => {
  console.log('[Global-Init] Initializing browser environment');
  
  // 设置全局环境标识
  (window as any).__MARIO_AI_ENV__ = 'browser';
  
  // 模拟一些Electron环境中可能需要的全局变量
  if (!(window as any).process) {
    (window as any).process = {
      env: {
        NODE_ENV: 'development'
      },
      platform: 'web',
      versions: {
        node: 'web',
        chrome: navigator.userAgent.match(/Chrome\/(\d+)/)?.[1] || 'unknown'
      }
    };
  }
  
  // 设置一些可能需要的全局函数的Mock版本
  setupBrowserMocks();
  
  console.log('[Global-Init] Browser environment initialized');
};

/**
 * 设置浏览器环境的Mock函数
 * 为一些Electron特有的功能提供浏览器兼容的Mock实现
 */
const setupBrowserMocks = () => {
  // Mock一些可能在组件中使用的全局函数
  if (!(window as any).electronAPI) {
    (window as any).electronAPI = {
      // 提供一些基础的Mock API
      getVersion: () => '1.0.0-web',
      getPlatform: () => 'web',
      isElectron: () => false
    };
  }

  // 修复dragEvent未定义的问题
  if (typeof (window as any).dragEvent === 'undefined') {
    (window as any).dragEvent = null;
  }

  // 如果需要，可以在这里添加更多的Mock函数
};

/**
 * 获取当前环境信息
 */
export const getEnvironmentInfo = () => {
  const isElectron = isElectronEnvironment();
  
  return {
    isElectron,
    isBrowser: !isElectron,
    platform: isElectron ? 'electron' : 'browser',
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  };
};

// 自动初始化（当模块被导入时）
initializeGlobal();

// 在开发环境中输出环境信息
if (typeof window !== 'undefined') {
  const envInfo = getEnvironmentInfo();
  console.log('[Global-Init] Environment:', envInfo);
}