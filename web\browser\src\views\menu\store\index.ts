import { eventUtils } from '@browser/core/utils/platform-lite';
import { makeObservable, observable } from 'mobx';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';
export class Store extends LiteDialogStore {
  public alwaysOnTop = false;

  public updateAvailable = false;

  public constructor() {
    super();

    makeObservable(this, {
      alwaysOnTop: observable,
      updateAvailable: observable,
    });

    this.init();

    eventUtils.on('update-available', () => {
      this.updateAvailable = true;
    });

    // 添加菜单特有的失焦隐藏功能
    if (this.hideOnBlur) {
      this.setupBlurHiding();
    }
  }

  private setupBlurHiding() {
    // 防止重复设置失焦隐藏
    if ((this as any).__blurHidingSetup) {
      return;
    }
    (this as any).__blurHidingSetup = true;

    let blurTimer: NodeJS.Timeout | null = null;

    const handleBlur = () => {
      blurTimer = setTimeout(() => {
        console.log('[MenuStore] Window blur detected, hiding menu');
        this.hide();
      }, 50); // 减少延迟，菜单应该快速响应
    };

    const handleFocus = () => {
      if (blurTimer) {
        clearTimeout(blurTimer);
        blurTimer = null;
      }
    };

    window.addEventListener('blur', handleBlur);
    window.addEventListener('focus', handleFocus);

    // 清理函数
    const cleanup = () => {
      window.removeEventListener('blur', handleBlur);
      window.removeEventListener('focus', handleFocus);
      if (blurTimer) {
        clearTimeout(blurTimer);
      }
    };

    // 当菜单隐藏时清理事件监听器
    const originalHide = this.hide.bind(this);
    this.hide = () => {
      cleanup();
      originalHide();
    };
  }

  public async init() {
    try {
      // 通过 IPC 获取窗口状态，而不是直接调用不存在的方法
      this.alwaysOnTop = await eventUtils.invoke('is-always-on-top');
    } catch (error) {
      console.warn('[MenuStore] Failed to get always-on-top status:', error);
      this.alwaysOnTop = false;
    }

    try {
      this.updateAvailable = await eventUtils.invoke('is-update-available');
    } catch (error) {
      console.warn('[MenuStore] Failed to get update status:', error);
      this.updateAvailable = false;
    }
  }

  public async save() {
    // TODO: 实现设置保存逻辑
    console.log('[MenuStore] Save method called - implementation needed');
  }
}

export default new Store();
