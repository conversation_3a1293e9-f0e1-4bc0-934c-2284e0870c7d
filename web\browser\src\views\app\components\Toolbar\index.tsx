import { observer } from 'mobx-react-lite';
import * as React from 'react';

import { NavigationButtons } from '../NavigationButtons';
import { AddressBar } from '../AddressBar';
import { RightButtons } from '../RightButtons';
import { cn } from '@browser/utils/tailwind-helpers';
import { TOOLBAR_HEIGHT } from '@mario-ai/shared';
import store from '../../store';

export const Toolbar = observer(() => {
  // StyledToolbar 样式 - Tailwind 版本，支持前卫主题
  const toolbarClasses = cn(
    'relative z-[100] flex items-center flex-row w-full pb-1',
    'text-futuristic-toolbar-text border-b border-mario-toolbar-bottom-line'
  );

  const toolbarStyle: React.CSSProperties = {
    height: `${TOOLBAR_HEIGHT}px`,
    background: 'var(--toolbar-bg, var(--mario-toolbar-bg, #ffffff))', // 支持前卫主题和深色主题
  };

  return (
    <div className={toolbarClasses} style={toolbarStyle}>
      <NavigationButtons />
      <AddressBar />
      <RightButtons />
    </div>
  );
});
