// 从 electron/main/application.ts 移动到这里
import { app, ipc<PERSON>ain, Menu } from 'electron';
import { isAbsolute, extname } from 'path';
import { existsSync } from 'fs';
import { SessionsService } from '@electron/main/services/sessions';
import { checkFiles } from '@electron/main/utils/files';

// 内联URL工具函数
const isURL = (input: string): boolean => {
  return /^https?:\/\//.test(input) || /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}/.test(input);
};

const prefixHttp = (url: string): string => {
  if (!/^https?:\/\//.test(url)) {
    return `http://${url}`;
  }
  return url;
};
import { WindowsService } from '@electron/main/services/windows';
import { LibSQLStorageService } from '@electron/main/services/storage-libsql';
import { getMainMenu } from '@electron/main/ui/menus/main';
import { runAutoUpdaterService } from '@electron/main/services';
import { DialogsService } from '@electron/main/services/dialogs-service';
import { requestAuth } from '@electron/main/ui/dialogs/auth';
import { NetworkServiceHandler } from '@electron/main/utils/network/network-service-handler';
import { ExtensionServiceHandler } from '@electron/main/services/extensions';

export class Application {
  public tempTabUrl = "";
  
  public static instance = new Application();

  public sessions: SessionsService;

  public storage = new LibSQLStorageService();

  public windows = new WindowsService();

  public dialogs = new DialogsService();

  /**
   * 设置内部事件监听器
   */
  private setupInternalEventListeners(): void {
    const { ipcMain } = require('electron');

    // 监听设置更新事件
    ipcMain.on('settings-updated-internal', (settings: any) => {
      this.broadcastSettingsUpdate(settings);
    });
  }

  /**
   * 广播设置更新到所有窗口和对话框
   */
  private broadcastSettingsUpdate(settings: any): void {
    try {
      // 广播到所有对话框
      this.dialogs.sendToAll('update-settings', settings);

      // 广播到所有窗口
      for (const window of this.windows.list) {
        window.send('update-settings', settings);

        // 广播到窗口中的所有视图
        window.viewManager.views.forEach(async (v) => {
          if (v.webContents.getURL().startsWith('http://localhost:4444/')) {
            v.webContents.send('update-settings', settings);
          }
        });
      }

      console.log('[Application] 设置更新已广播到所有窗口');
    } catch (error) {
      console.error('[Application] 广播设置更新失败:', error);
    }
  }

  public start() {
    console.log('[Application] Starting application...');
    const gotTheLock = app.requestSingleInstanceLock();

    if (!gotTheLock) {
      console.log('[Application] Another instance is running, quitting...');
      app.quit();
      return;
    } else {
      console.log('[Application] Got single instance lock');
      app.on('second-instance', async (e, argv) => {
        const path = argv[argv.length - 1];

        if (isAbsolute(path) && existsSync(path)) {
          if (process.env.NODE_ENV !== 'development') {
            const path = argv[argv.length - 1];
            const ext = extname(path);

            if (ext === '.html') {
              this.windows.current.win.focus();
              this.windows.current.viewManager.create({
                url: `file:///${path}`,
                active: true,
              });
            }
          }
          return;
        } else if (isURL(path)) {
          this.windows.current.win.focus();
          this.windows.current.viewManager.create({
            url: prefixHttp(path),
            active: true,
          });
          return;
        }

        this.windows.open();
      });
    }

    app.on('login', async (e, webContents, request, authInfo, callback) => {
      e.preventDefault();

      const window = this.windows.findByBrowserView(webContents.id);
      const credentials = await requestAuth(
        window.win,
        request.url,
        webContents.id,
      );

      if (credentials) {
        callback(credentials.username, credentials.password);
      }
    });

    ipcMain.on('create-window', (e, incognito = false) => {
      this.windows.open(incognito);
    });

    // 处理下载路径修改 - 使用 handle 模式返回新路径
    ipcMain.handle('downloads-path-change', async (e) => {
      try {
        const { dialog } = require('electron');
        const result = await dialog.showOpenDialog({
          properties: ['openDirectory'],
          title: '选择下载文件夹'
        });

        if (!result.canceled && result.filePaths.length > 0) {
          const newPath = result.filePaths[0];

          // 通过 storage 更新设置
          await this.storage.setConfig('downloadsPath', newPath);

          console.log('[Application] Downloads path updated to:', newPath);

          // 返回新路径给前端
          return { success: true, path: newPath };
        }

        return { success: false };
      } catch (error) {
        console.error('[Application] Error updating downloads path:', error);
        return { success: false, error: error.message };
      }
    });

    // 广告拦截相关的 IPC 处理器
    ipcMain.handle('get-ad-lists', async () => {
      try {
        const { getAdLists } = require('../services/adblock');
        const lists = getAdLists();
        return lists.join('\n');
      } catch (error) {
        console.error('[Application] Error getting ad lists:', error);
        return '';
      }
    });

    ipcMain.handle('get-ad-filter-count', async () => {
      try {
        const { filterCount } = require('../services/adblock');
        return filterCount;
      } catch (error) {
        console.error('[Application] Error getting filter count:', error);
        return 0;
      }
    });

    ipcMain.on('update-ad-lists', async (e, lists: string) => {
      try {
        const { updateAdLists } = require('../services/adblock');
        const sessions = this.sessions.sessions.map(s => s.session);
        await updateAdLists(lists, sessions);
        console.log('[Application] Ad lists updated successfully');
      } catch (error) {
        console.error('[Application] Error updating ad lists:', error);
      }
    });

    // 清除浏览数据 - 支持 send 和 handle 两种方式
    const clearBrowsingDataHandler = async (e?: any) => {
      try {
        console.log('[Application] Clearing browsing data...');

        // 清除历史记录
        const historyResult = await this.storage.remove({ scope: 'history', query: {}, multi: true });
        console.log('[Application] History cleared, deleted:', historyResult, 'records');

        // 清除网站图标
        const faviconsResult = await this.storage.remove({ scope: 'favicons', query: {}, multi: true });
        console.log('[Application] Favicons cleared, deleted:', faviconsResult, 'records');

        // 清除表单填充数据
        const formfillResult = await this.storage.remove({ scope: 'formfill', query: {}, multi: true });
        console.log('[Application] Form fill data cleared, deleted:', formfillResult, 'records');

        // 清除会话缓存和 cookies
        this.sessions.clearCache('normal');
        this.sessions.clearCache('incognito');
        console.log('[Application] Session data cleared');

        // 清除浏览数据相关的缓存，但保留书签缓存
        this.storage.clearBrowsingDataCache();
        console.log('[Application] Browsing data cache cleared (bookmarks preserved)');

        // 广播数据清除事件到所有窗口
        this.windows.broadcast('browsing-data-cleared');

        console.log('[Application] All browsing data cleared successfully');
        return { success: true };
      } catch (error) {
        console.error('[Application] Error clearing browsing data:', error);
        return { success: false, error: error.message };
      }
    };

    // 注册两种处理方式
    ipcMain.on('clear-browsing-data', clearBrowsingDataHandler);
    ipcMain.handle('clear-browsing-data', clearBrowsingDataHandler);



    this.onReady();
  }

  private async onReady() {
    console.log('[Application] Setting up app ready handler...');
    app.commandLine.appendSwitch('lang', 'zh-CN');
    await app.whenReady();
    console.log('[Application] App is ready');

    new ExtensionServiceHandler();

    NetworkServiceHandler.get();

    checkFiles();

    console.log('[Application] Starting storage service...');
    await this.storage.initialize();

    // 设置内部事件监听器
    this.setupInternalEventListeners();

    console.log('[Application] Starting sessions service...');
    this.sessions = new SessionsService();

    console.log('[Application] Starting dialogs service...');
    this.dialogs.run();

    console.log('[Application] Opening main window...');
    this.windows.open();
    console.log('[Application] Main window opened');

    Menu.setApplicationMenu(getMainMenu());
    runAutoUpdaterService();

    app.on('activate', () => {
      if (this.windows.list.filter((x) => x !== null).length === 0) {
        this.windows.open();
      }
    });
  }
}