import * as React from 'react';
import { observer } from 'mobx-react-lite';
import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';
import { Button } from '@browser/core/components/Button';
import { eventUtils } from '@browser/core/utils/platform-lite';
import { UIStyle } from '@browser/core/styles/default-styles';


const onClick = (type: string) => () => {
  eventUtils.send(`show-${type}-video-dialog-${store.tabId}`);
  store.hide();
};
const onCopyClick = () => {
  const s = (store.videoUrls || []).join("\n");
  const textarea = document.createElement('textarea');
  textarea.value = s;
  document.body.appendChild(textarea);
  textarea.select();
  navigator.clipboard.writeText(textarea.value)
    .then(() => {
      alert("以下链接已复制到剪贴板：\n" + s);
    })
    .catch((error) => {
      alert("复制文本失败：\n" + error);
    });
  document.body.removeChild(textarea);
  store.hide();
};

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  const appClasses = cn(
    'p-4 rounded-[10px] shadow-dialog bg-mario-dialog',
    'animate-[fadeIn_0.15s_ease-out]',
    // textfield 和 dropdown 样式
    '[&_.textfield]:w-[255px] [&_.textfield]:ml-auto',
    '[&_.dropdown]:w-[255px] [&_.dropdown]:ml-auto',
    // 文本颜色根据主题
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black'
  );

  // Buttons 样式 - Tailwind 版本
  const buttonsClasses = cn(
    'w-full block',
    // 子元素 button 的下边距 (除了最后一个)
    '[&_.button:not(:last-child)]:mb-3'
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <div className={buttonsClasses}>
        <Button onClick={onClick("full")}>全屏播放</Button>
        <Button onClick={onClick("float")}>悬浮播放</Button>
        <Button onClick={onClick("other")}>外部播放</Button>
        <Button onClick={onCopyClick}>复制地址</Button>
      </div>
    </div>
  );
});
