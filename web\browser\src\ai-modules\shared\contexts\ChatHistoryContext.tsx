import React, { createContext, useContext, useState, useCallback } from 'react';

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  thinking?: string;
  isStreaming?: boolean;
}

interface ChatHistoryContextType {
  currentMessages: Message[];
  addMessage: (message: Message) => void;
  updateMessage: (id: string, updates: Partial<Message>) => void;
  createNewChat: () => void;
  clearHistory: () => void;
}

const ChatHistoryContext = createContext<ChatHistoryContextType | undefined>(undefined);

export const useChatHistory = () => {
  const context = useContext(ChatHistoryContext);
  if (!context) {
    throw new Error('useChatHistory must be used within a ChatHistoryProvider');
  }
  return context;
};

interface ChatHistoryProviderProps {
  children: React.ReactNode;
}

export const ChatHistoryProvider: React.FC<ChatHistoryProviderProps> = ({ children }) => {
  const [currentMessages, setCurrentMessages] = useState<Message[]>([]);

  const addMessage = useCallback((message: Message) => {
    setCurrentMessages(prev => [...prev, message]);
  }, []);

  const updateMessage = useCallback((id: string, updates: Partial<Message>) => {
    setCurrentMessages(prev => 
      prev.map(msg => 
        msg.id === id ? { ...msg, ...updates } : msg
      )
    );
  }, []);

  const createNewChat = useCallback(() => {
    setCurrentMessages([]);
  }, []);

  const clearHistory = useCallback(() => {
    setCurrentMessages([]);
  }, []);

  const value: ChatHistoryContextType = {
    currentMessages,
    addMessage,
    updateMessage,
    createNewChat,
    clearHistory,
  };

  return (
    <ChatHistoryContext.Provider value={value}>
      {children}
    </ChatHistoryContext.Provider>
  );
};
