import { observer } from 'mobx-react-lite';
import * as React from 'react';
import { WindowsControls } from 'react-windows-controls';

import store from '../../store';
import { Tabbar } from '../Tabbar';
import { NavigationButtons } from '../NavigationButtons';
import { RightButtons } from '../RightButtons';
import { SiteButtons } from '../SiteButtons';
import { windowControls } from '@browser/core/utils/platform-lite';
import { cn } from '@browser/utils/tailwind-helpers';
import { ICON_FULLSCREEN_EXIT } from '@mario-ai/shared';

// 轻量级平台检测，避免使用Node.js os模块
const getPlatform = (): string => {
  if (typeof navigator !== 'undefined') {
    const platform = navigator.platform.toLowerCase();
    if (platform.includes('mac')) return 'darwin';
    if (platform.includes('win')) return 'win32';
    if (platform.includes('linux')) return 'linux';
  }
  return 'unknown';
};

const onMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
  if (store.addressbarFocused) {
    e.preventDefault();
  }
};

// 使用轻量级平台工具，避免完整抽象层的性能开销
const onCloseClick = () => windowControls.close();
const onMinimizeClick = () => windowControls.minimize();
const onMaximizeClick = () => windowControls.maximize();
const onFullscreenExit = () => windowControls.toggleFullscreen();

export const Titlebar = observer(() => {
  // StyledTitlebar 样式 - 支持所有主题
  const titlebarClasses = cn(
    'relative z-[100] flex flex-row w-full',
    // 根据主题动态设置文本颜色
    store.theme['toolbar.lightForeground'] ? 'text-white' : 'text-black',
    // 原始样式：align-items: ${theme.isCompact ? 'center' : 'initial'}/*  */
    store.isCompact ? 'items-center' : 'items-start',
    // 左边距根据平台和全屏状态，匹配侧边栏宽度64px
    getPlatform() === 'darwin' && !store.isFullscreen ? 'pl-[78px]' : 'pl-0'
  );

  // 对应原始样式：height: ${theme.titlebarHeight}px
  const titlebarHeight = store.theme.titlebarHeight || (store.isCompact ? 32 : 40);

  // 标题栏样式 - 合并背景和高度，确保所有主题都能正确显示
  const titlebarStyle: React.CSSProperties = {
    height: `${titlebarHeight}px`,
    background: 'var(--titlebar-bg)', // 使用标准CSS变量
    color: 'var(--titlebar-text)', // 确保文本颜色正确
  };



  const beforeStyle: React.CSSProperties = {
    position: 'absolute',
    zIndex: 0,
    top: '4px',
    left: '4px',
    right: '4px',
    bottom: '0px',
    content: '""',
    WebkitAppRegion: store.isFullscreen ? 'no-drag' : 'drag',
  } as React.CSSProperties;

  // FullscreenExitButton 样式 - Tailwind 版本
  const fullscreenExitButtonClasses = cn(
    'top-0 right-0 h-8 min-w-[45px] ml-2 transition-colors duration-100',
    'bg-center bg-no-repeat bg-contain',
    'hover:bg-gray-600/40',
    // 图标过滤器
    store.theme['dialog.lightForeground'] ? 'invert' : '',
    // webkit-app-region
    '[&]:[-webkit-app-region:no-drag]'
  );

  return (
    <div
      className={titlebarClasses}
      style={titlebarStyle}
      onMouseDown={onMouseDown}
    >
      {/* before 伪元素替代 */}
      <div style={beforeStyle} />

      {store.isCompact && <NavigationButtons />}
      <Tabbar />
      {store.isCompact && <RightButtons />}

      {getPlatform() !== 'darwin' && (
        store.isFullscreen
          ? <div
            className={fullscreenExitButtonClasses}
            style={{
              height: store.isCompact ? '100%' : 32,
              backgroundImage: `url(${ICON_FULLSCREEN_EXIT})`,
            }}
            onMouseUp={onFullscreenExit}
          />
          : <WindowsControls
            style={{
              height: store.isCompact ? '100%' : 32,
              WebkitAppRegion: 'no-drag',
              marginLeft: 8,
              marginTop: 4
            }}
            onClose={onCloseClick}
            onMinimize={onMinimizeClick}
            onMaximize={onMaximizeClick}
            dark={store.theme['toolbar.lightForeground']}
          />
      )}
    </div>
  );
});