import { makeObservable, observable } from 'mobx';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public url = '';

  public constructor() {
    super({ hideOnBlur: false, visibilityWrapper: false });

    makeObservable(this, { url: observable });

    this.onUpdateTabInfo = (tabId, auth) => {
      this.url = auth.url;
    };
  }
}

export default new Store();
