import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { BLUE_500 } from '@mario-ai/shared';

export interface Props {
  style?: any;
  color?: string;
  thickness?: number;
  size?: number;
  indeterminate?: boolean;
  value?: number;
}

export const Preloader = ({
  style,
  color = BLUE_500,
  size = 48,
  thickness = 4,
  value = 0,
  indeterminate = false,
}: Props) => {
  // 容器样式
  const containerClasses = cn(
    'origin-center z-[5] rotate-[-89deg]',
    indeterminate ? 'animate-preloader-rotate' : ''
  );

  const containerStyle: React.CSSProperties = {
    width: `${size}px`,
    height: `${size}px`,
  };

  // SVG 路径样式
  const pathStyle: React.CSSProperties = {
    strokeLinecap: 'square' as const,
    strokeDasharray: indeterminate ? '1, 200' : '199, 200',
    strokeDashoffset: indeterminate 
      ? undefined 
      : `${199 - value * (199 - 82)}px`,
    strokeWidth: thickness,
    stroke: color,
    animation: indeterminate 
      ? 'preloader-dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite'
      : undefined,
    transition: indeterminate 
      ? '0.3s stroke, 0.3s stroke-dasharray'
      : '0.3s stroke',
  };

  return (
    <div style={style}>
      <div 
        className={containerClasses}
        style={containerStyle}
      >
        <svg viewBox="25 25 50 50">
          <circle
            cx="50"
            cy="50"
            r="20"
            fill="none"
            strokeMiterlimit="10"
            style={pathStyle}
          />
        </svg>
      </div>
    </div>
  );
};
