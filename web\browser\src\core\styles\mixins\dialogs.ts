export const DIALOG_TRANSITION = `0.2s opacity`;

export const DIALOG_BOX_SHADOW =
  '0 12px 16px rgba(0, 0, 0, 0.12), 0 8px 10px rgba(0, 0, 0, 0.16)';

export const DIALOG_BORDER_RADIUS = '10';

// Pure CSS constants for dialogs
export const DIALOG_BASE_STYLES = {
  margin: '16px',
  marginTop: '3px',
  boxShadow: DIALOG_BOX_SHADOW,
  borderRadius: `${DIALOG_BORDER_RADIUS}px`,
  overflow: 'hidden',
  position: 'relative' as const
};
