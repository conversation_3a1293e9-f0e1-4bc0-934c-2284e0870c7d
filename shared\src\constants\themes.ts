import { ITheme } from '../types/theme.js';
import { BLUE_500 } from './colors.js';

export const lightTheme: ITheme = {
  'titlebar.backgroundColor': '#f3f3fa',
  'addressbar.backgroundColor': '#fff',
  'addressbar.textColor': '#000',
  'toolbar.backgroundColor': '#f3f3fa',
  'toolbar.bottomLine.backgroundColor': 'rgba(0, 0, 0, 0.12)',
  'toolbar.lightForeground': false,
  'toolbar.separator.color': 'rgba(0, 0, 0, 0.12)',
  'tab.selected.textColor': '#000',
  'tab.textColor': `rgba(0, 0, 0, 0.7)`,
  'control.backgroundColor': 'rgba(0, 0, 0, 0.08)',
  'control.hover.backgroundColor': 'rgba(0, 0, 0, 0.1)',
  'control.valueColor': '#000',
  'control.lightIcon': false,
  'switch.backgroundColor': 'rgba(0, 0, 0, 0.16)',
  'dialog.backgroundColor': '#fff',
  'dialog.separator.color': 'rgba(0, 0, 0, 0.12)',
  'dialog.textColor': '#000',
  'dialog.lightForeground': false,
  'searchBox.backgroundColor': '#fff',
  'searchBox.lightForeground': false,
  'pages.backgroundColor': '#fff',
  'pages.lightForeground': false,
  'pages.textColor': '#000',
  'dropdown.backgroundColor': '#fff',
  'dropdown.backgroundColor.translucent': 'rgba(255, 255, 255, 0.7)',
  'dropdown.separator.color': 'rgba(0, 0, 0, 0.12)',
  'pages.navigationDrawer1.backgroundColor': '#f5f5f5',
  'pages.navigationDrawer2.backgroundColor': '#fafafa',

  accentColor: BLUE_500,
  backgroundColor: '#fff',
};

export const darkTheme: ITheme = {
  'titlebar.backgroundColor': '#1c1c1c',
  'addressbar.backgroundColor': '#393939',
  'addressbar.textColor': '#fff',
  'toolbar.backgroundColor': '#1c1c1c',
  'toolbar.bottomLine.backgroundColor': 'rgba(255, 255, 255, 0.08)',
  'toolbar.lightForeground': true,
  'toolbar.separator.color': 'rgba(255, 255, 255, 0.12)',
  'tab.selected.textColor': '#fff',
  'tab.textColor': 'rgba(255, 255, 255, 0.54)',
  'control.backgroundColor': 'rgba(255, 255, 255, 0.1)',
  'control.hover.backgroundColor': 'rgba(255, 255, 255, 0.12)',
  'control.valueColor': '#fff',
  'control.lightIcon': true,
  'switch.backgroundColor': 'rgba(255, 255, 255, 0.24)',
  'dialog.backgroundColor': '#383838',
  'dialog.separator.color': 'rgba(255, 255, 255, 0.12)',
  'dialog.textColor': '#fff',
  'dialog.lightForeground': true,
  'searchBox.backgroundColor': '#262626',
  'searchBox.lightForeground': true,
  'pages.backgroundColor': '#212121',
  'pages.lightForeground': true,
  'pages.textColor': '#fff',
  'dropdown.backgroundColor': 'rgb(66, 66, 66)',
  'dropdown.backgroundColor.translucent': 'rgb(60, 60, 60, 0.6)',
  'dropdown.separator.color': 'rgba(255, 255, 255, 0.12)',
  'pages.navigationDrawer1.backgroundColor': '#2d2d2d',
  'pages.navigationDrawer2.backgroundColor': '#262626',

  backgroundColor: '#1c1c1c',
  accentColor: BLUE_500,
};

// 暖灰橙主题 - 温暖舒适的现代配色，类似 Notion 和 Linear 的设计
export const futuristicTheme: ITheme = {
  'titlebar.backgroundColor': 'linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%)',
  'addressbar.backgroundColor': 'rgba(255, 255, 255, 0.1)',
  'addressbar.textColor': 'rgba(255, 255, 255, 0.95)',
  'toolbar.backgroundColor': 'linear-gradient(135deg, #3a3a3a 0%, #2d2d2d 100%)',
  'toolbar.bottomLine.backgroundColor': 'rgba(251, 146, 60, 0.4)',
  'toolbar.lightForeground': true,
  'toolbar.separator.color': 'rgba(251, 146, 60, 0.3)',
  'tab.selected.textColor': 'rgba(251, 146, 60, 1)',
  'tab.textColor': 'rgba(255, 255, 255, 0.8)',
  'control.backgroundColor': 'rgba(251, 146, 60, 0.15)',
  'control.hover.backgroundColor': 'rgba(251, 146, 60, 0.25)',
  'control.valueColor': 'rgba(251, 146, 60, 1)',
  'control.lightIcon': true,
  'switch.backgroundColor': 'rgba(251, 146, 60, 0.25)',
  'dialog.backgroundColor': 'rgba(45, 45, 45, 0.95)',
  'dialog.separator.color': 'rgba(251, 146, 60, 0.3)',
  'dialog.textColor': 'rgba(255, 255, 255, 0.95)',
  'dialog.lightForeground': true,
  'searchBox.backgroundColor': 'rgba(255, 255, 255, 0.1)',
  'searchBox.lightForeground': true,
  'pages.backgroundColor': 'linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 50%, #474747 100%)',
  'pages.lightForeground': true,
  'pages.textColor': 'rgba(255, 255, 255, 0.95)',
  'dropdown.backgroundColor': 'rgba(45, 45, 45, 0.95)',
  'dropdown.backgroundColor.translucent': 'rgba(45, 45, 45, 0.8)',
  'dropdown.separator.color': 'rgba(251, 146, 60, 0.3)',
  'pages.navigationDrawer1.backgroundColor': 'rgba(251, 146, 60, 0.12)',
  'pages.navigationDrawer2.backgroundColor': 'rgba(251, 146, 60, 0.08)',

  backgroundColor: 'linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 50%, #474747 100%)',
  accentColor: '#fb923c',
};
