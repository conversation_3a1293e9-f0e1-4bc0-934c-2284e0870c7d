import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const permissions = sqliteTable('permissions', {
  id: text('id').primaryKey(),
  origin: text('origin').notNull(),
  permissionType: text('permission_type').notNull(), // 'camera', 'microphone', 'location', etc.
  granted: integer('granted', { mode: 'boolean' }).default(false),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull()
});

export type Permission = typeof permissions.$inferSelect;
export type NewPermission = typeof permissions.$inferInsert;
