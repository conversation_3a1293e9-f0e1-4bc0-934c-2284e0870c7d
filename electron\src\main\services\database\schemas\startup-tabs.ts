import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const startupTabs = sqliteTable('startup_tabs', {
  id: text('id').primaryKey(),
  title: text('title'),
  url: text('url').notNull(),
  favicon: text('favicon'),
  isUserDefined: integer('is_user_defined', { mode: 'boolean' }).default(false),
  pinned: integer('pinned', { mode: 'boolean' }).default(false),
  windowId: integer('window_id').default(1),
  orderIndex: integer('order_index').default(0),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull()
});

export type StartupTab = typeof startupTabs.$inferSelect;
export type NewStartupTab = typeof startupTabs.$inferInsert;
