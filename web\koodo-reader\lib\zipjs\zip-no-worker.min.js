((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zip={})})(this,(function(e){"use strict";const{Array:t,Object:n,String:i,BigInt:r,Math:a,Date:s,Map:o,URL:l,Error:c,Uint8Array:d,Uint16Array:f,Uint32Array:u,DataView:h,Blob:_,Promise:w,TextEncoder:p,TextDecoder:g,FileReader:b,document:x,crypto:y,btoa:m}=globalThis,k=-2;function v(e){return R(e.map((([e,n])=>new t(e).fill(n,0,e))))}function R(e){return e.reduce(((e,n)=>e.concat(t.isArray(n)?R(n):n)),[])}const E=[0,1,2,3].concat(...v([[2,4],[2,5],[4,6],[4,7],[8,8],[8,9],[16,10],[16,11],[32,12],[32,13],[64,14],[64,15],[2,0],[1,16],[1,17],[2,18],[2,19],[4,20],[4,21],[8,22],[8,23],[16,24],[16,25],[32,26],[32,27],[64,28],[64,29]]));function D(){const e=this;function t(e,t){let n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}e.build_tree=n=>{const i=e.dyn_tree,r=e.stat_desc.static_tree,s=e.stat_desc.elems;let o,l,c,d=-1;for(n.heap_len=0,n.heap_max=573,o=0;s>o;o++)0!==i[2*o]?(n.heap[++n.heap_len]=d=o,n.depth[o]=0):i[2*o+1]=0;for(;2>n.heap_len;)c=n.heap[++n.heap_len]=2>d?++d:0,i[2*c]=1,n.depth[c]=0,n.opt_len--,r&&(n.static_len-=r[2*c+1]);for(e.max_code=d,o=a.floor(n.heap_len/2);o>=1;o--)n.pqdownheap(i,o);c=s;do{o=n.heap[1],n.heap[1]=n.heap[n.heap_len--],n.pqdownheap(i,1),l=n.heap[1],n.heap[--n.heap_max]=o,n.heap[--n.heap_max]=l,i[2*c]=i[2*o]+i[2*l],n.depth[c]=a.max(n.depth[o],n.depth[l])+1,i[2*o+1]=i[2*l+1]=c,n.heap[1]=c++,n.pqdownheap(i,1)}while(n.heap_len>=2);n.heap[--n.heap_max]=n.heap[1],(t=>{const n=e.dyn_tree,i=e.stat_desc.static_tree,r=e.stat_desc.extra_bits,a=e.stat_desc.extra_base,s=e.stat_desc.max_length;let o,l,c,d,f,u,h=0;for(d=0;15>=d;d++)t.bl_count[d]=0;for(n[2*t.heap[t.heap_max]+1]=0,o=t.heap_max+1;573>o;o++)l=t.heap[o],d=n[2*n[2*l+1]+1]+1,d>s&&(d=s,h++),n[2*l+1]=d,l>e.max_code||(t.bl_count[d]++,f=0,a>l||(f=r[l-a]),u=n[2*l],t.opt_len+=u*(d+f),i&&(t.static_len+=u*(i[2*l+1]+f)));if(0!==h){do{for(d=s-1;0===t.bl_count[d];)d--;t.bl_count[d]--,t.bl_count[d+1]+=2,t.bl_count[s]--,h-=2}while(h>0);for(d=s;0!==d;d--)for(l=t.bl_count[d];0!==l;)c=t.heap[--o],c>e.max_code||(n[2*c+1]!=d&&(t.opt_len+=(d-n[2*c+1])*n[2*c],n[2*c+1]=d),l--)}})(n),((e,n,i)=>{const r=[];let a,s,o,l=0;for(a=1;15>=a;a++)r[a]=l=l+i[a-1]<<1;for(s=0;n>=s;s++)o=e[2*s+1],0!==o&&(e[2*s]=t(r[o]++,o))})(i,e.max_code,n.bl_count)}}function A(e,t,n,i,r){const a=this;a.static_tree=e,a.extra_bits=t,a.extra_base=n,a.elems=i,a.max_length=r}D._length_code=[0,1,2,3,4,5,6,7].concat(...v([[2,8],[2,9],[2,10],[2,11],[4,12],[4,13],[4,14],[4,15],[8,16],[8,17],[8,18],[8,19],[16,20],[16,21],[16,22],[16,23],[32,24],[32,25],[32,26],[31,27],[1,28]])),D.base_length=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0],D.base_dist=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576],D.d_code=e=>256>e?E[e]:E[256+(e>>>7)],D.extra_lbits=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],D.extra_dbits=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],D.extra_blbits=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],D.bl_order=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];const F=v([[144,8],[112,9],[24,7],[8,8]]);A.static_ltree=R([12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254,1,129,65,193,33,161,97,225,17,145,81,209,49,177,113,241,9,137,73,201,41,169,105,233,25,153,89,217,57,185,121,249,5,133,69,197,37,165,101,229,21,149,85,213,53,181,117,245,13,141,77,205,45,173,109,237,29,157,93,221,61,189,125,253,19,275,147,403,83,339,211,467,51,307,179,435,115,371,243,499,11,267,139,395,75,331,203,459,43,299,171,427,107,363,235,491,27,283,155,411,91,347,219,475,59,315,187,443,123,379,251,507,7,263,135,391,71,327,199,455,39,295,167,423,103,359,231,487,23,279,151,407,87,343,215,471,55,311,183,439,119,375,247,503,15,271,143,399,79,335,207,463,47,303,175,431,111,367,239,495,31,287,159,415,95,351,223,479,63,319,191,447,127,383,255,511,0,64,32,96,16,80,48,112,8,72,40,104,24,88,56,120,4,68,36,100,20,84,52,116,3,131,67,195,35,163,99,227].map(((e,t)=>[e,F[t]])));const S=v([[30,5]]);function z(e,t,n,i,r){const a=this;a.good_length=e,a.max_lazy=t,a.nice_length=n,a.max_chain=i,a.func=r}A.static_dtree=R([0,16,8,24,4,20,12,28,2,18,10,26,6,22,14,30,1,17,9,25,5,21,13,29,3,19,11,27,7,23].map(((e,t)=>[e,S[t]]))),A.static_l_desc=new A(A.static_ltree,D.extra_lbits,257,286,15),A.static_d_desc=new A(A.static_dtree,D.extra_dbits,0,30,15),A.static_bl_desc=new A(null,D.extra_blbits,0,19,7);const T=[new z(0,0,0,0,0),new z(4,4,8,4,1),new z(4,5,16,8,1),new z(4,6,32,32,1),new z(4,4,16,16,2),new z(8,16,32,32,2),new z(8,16,128,128,2),new z(8,32,128,256,2),new z(32,128,258,1024,2),new z(32,258,258,4096,2)],U=["need dictionary","stream end","","","stream error","data error","","buffer error","",""],C=113,W=666,I=262;function B(e,t,n,i){const r=e[2*t],a=e[2*n];return a>r||r==a&&i[t]<=i[n]}function L(){const e=this;let t,n,i,r,s,o,l,c,u,h,_,w,p,g,b,x,y,m,v,R,E,F,S,z,L,M,N,H,O,P,V,q,Z;const K=new D,G=new D,Y=new D;let j,X,J,Q,$,ee;function te(){let t;for(t=0;286>t;t++)V[2*t]=0;for(t=0;30>t;t++)q[2*t]=0;for(t=0;19>t;t++)Z[2*t]=0;V[512]=1,e.opt_len=e.static_len=0,X=J=0}function ne(e,t){let n,i=-1,r=e[1],a=0,s=7,o=4;0===r&&(s=138,o=3),e[2*(t+1)+1]=65535;for(let l=0;t>=l;l++)n=r,r=e[2*(l+1)+1],++a<s&&n==r||(o>a?Z[2*n]+=a:0!==n?(n!=i&&Z[2*n]++,Z[32]++):a>10?Z[36]++:Z[34]++,a=0,i=n,0===r?(s=138,o=3):n==r?(s=6,o=3):(s=7,o=4))}function ie(t){e.pending_buf[e.pending++]=t}function re(e){ie(255&e),ie(e>>>8&255)}function ae(e,t){let n;const i=t;ee>16-i?(n=e,$|=n<<ee&65535,re($),$=n>>>16-ee,ee+=i-16):($|=e<<ee&65535,ee+=i)}function se(e,t){const n=2*e;ae(65535&t[n],65535&t[n+1])}function oe(e,t){let n,i,r=-1,a=e[1],s=0,o=7,l=4;for(0===a&&(o=138,l=3),n=0;t>=n;n++)if(i=a,a=e[2*(n+1)+1],++s>=o||i!=a){if(l>s)do{se(i,Z)}while(0!=--s);else 0!==i?(i!=r&&(se(i,Z),s--),se(16,Z),ae(s-3,2)):s>10?(se(18,Z),ae(s-11,7)):(se(17,Z),ae(s-3,3));s=0,r=i,0===a?(o=138,l=3):i==a?(o=6,l=3):(o=7,l=4)}}function le(){16==ee?(re($),$=0,ee=0):8>ee||(ie(255&$),$>>>=8,ee-=8)}function ce(t,n){let i,r,s;if(e.dist_buf[X]=t,e.lc_buf[X]=255&n,X++,0===t?V[2*n]++:(J++,t--,V[2*(D._length_code[n]+256+1)]++,q[2*D.d_code(t)]++),0==(8191&X)&&N>2){for(i=8*X,r=E-y,s=0;30>s;s++)i+=q[2*s]*(5+D.extra_dbits[s]);if(i>>>=3,J<a.floor(X/2)&&i<a.floor(r/2))return!0}return X==j-1}function de(t,n){let i,r,a,s,o=0;if(0!==X)do{i=e.dist_buf[o],r=e.lc_buf[o],o++,0===i?se(r,t):(a=D._length_code[r],se(a+256+1,t),s=D.extra_lbits[a],0!==s&&(r-=D.base_length[a],ae(r,s)),i--,a=D.d_code(i),se(a,n),s=D.extra_dbits[a],0!==s&&(i-=D.base_dist[a],ae(i,s)))}while(X>o);se(256,t),Q=t[513]}function fe(){ee>8?re($):ee>0&&ie(255&$),$=0,ee=0}function ue(t,n,i){ae(0+(i?1:0),3),((t,n)=>{fe(),Q=8,re(n),re(~n),e.pending_buf.set(c.subarray(t,t+n),e.pending),e.pending+=n})(t,n)}function he(n){((t,n,i)=>{let r,a,s=0;N>0?(K.build_tree(e),G.build_tree(e),s=(()=>{let t;for(ne(V,K.max_code),ne(q,G.max_code),Y.build_tree(e),t=18;t>=3&&0===Z[2*D.bl_order[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t})(),r=e.opt_len+3+7>>>3,a=e.static_len+3+7>>>3,a>r||(r=a)):r=a=n+5,n+4>r||-1==t?a==r?(ae(2+(i?1:0),3),de(A.static_ltree,A.static_dtree)):(ae(4+(i?1:0),3),((e,t,n)=>{let i;for(ae(e-257,5),ae(t-1,5),ae(n-4,4),i=0;n>i;i++)ae(Z[2*D.bl_order[i]+1],3);oe(V,e-1),oe(q,t-1)})(K.max_code+1,G.max_code+1,s+1),de(V,q)):ue(t,n,i),te(),i&&fe()})(0>y?-1:y,E-y,n),y=E,t.flush_pending()}function _e(){let e,n,i,r;do{if(r=u-S-E,0===r&&0===E&&0===S)r=s;else if(-1==r)r--;else if(E>=s+s-I){c.set(c.subarray(s,s+s),0),F-=s,E-=s,y-=s,e=p,i=e;do{n=65535&_[--i],_[i]=s>n?0:n-s}while(0!=--e);e=s,i=e;do{n=65535&h[--i],h[i]=s>n?0:n-s}while(0!=--e);r+=s}if(0===t.avail_in)return;e=t.read_buf(c,E+S,r),S+=e,3>S||(w=255&c[E],w=(w<<x^255&c[E+1])&b)}while(I>S&&0!==t.avail_in)}function we(e){let t,n,i=L,r=E,a=z;const o=E>s-I?E-(s-I):0;let d=P;const f=l,u=E+258;let _=c[r+a-1],w=c[r+a];O>z||(i>>=2),d>S&&(d=S);do{if(t=e,c[t+a]==w&&c[t+a-1]==_&&c[t]==c[r]&&c[++t]==c[r+1]){r+=2,t++;do{}while(c[++r]==c[++t]&&c[++r]==c[++t]&&c[++r]==c[++t]&&c[++r]==c[++t]&&c[++r]==c[++t]&&c[++r]==c[++t]&&c[++r]==c[++t]&&c[++r]==c[++t]&&u>r);if(n=258-(u-r),r=u-258,n>a){if(F=e,a=n,n>=d)break;_=c[r+a-1],w=c[r+a]}}}while((e=65535&h[e&f])>o&&0!=--i);return a>S?S:a}e.depth=[],e.bl_count=[],e.heap=[],V=[],q=[],Z=[],e.pqdownheap=(t,n)=>{const i=e.heap,r=i[n];let a=n<<1;for(;a<=e.heap_len&&(a<e.heap_len&&B(t,i[a+1],i[a],e.depth)&&a++,!B(t,r,i[a],e.depth));)i[n]=i[a],n=a,a<<=1;i[n]=r},e.deflateInit=(t,v,D,F,U,W)=>(F||(F=8),U||(U=8),W||(W=0),t.msg=null,-1==v&&(v=6),1>U||U>9||8!=F||9>D||D>15||0>v||v>9||0>W||W>2?k:(t.dstate=e,o=D,s=1<<o,l=s-1,g=U+7,p=1<<g,b=p-1,x=a.floor((g+3-1)/3),c=new d(2*s),h=[],_=[],j=1<<U+6,e.pending_buf=new d(4*j),i=4*j,e.dist_buf=new f(j),e.lc_buf=new d(j),N=v,H=W,(t=>(t.total_in=t.total_out=0,t.msg=null,e.pending=0,e.pending_out=0,n=C,r=0,K.dyn_tree=V,K.stat_desc=A.static_l_desc,G.dyn_tree=q,G.stat_desc=A.static_d_desc,Y.dyn_tree=Z,Y.stat_desc=A.static_bl_desc,$=0,ee=0,Q=8,te(),(()=>{u=2*s,_[p-1]=0;for(let e=0;p-1>e;e++)_[e]=0;M=T[N].max_lazy,O=T[N].good_length,P=T[N].nice_length,L=T[N].max_chain,E=0,y=0,S=0,m=z=2,R=0,w=0})(),0))(t))),e.deflateEnd=()=>42!=n&&n!=C&&n!=W?k:(e.lc_buf=null,e.dist_buf=null,e.pending_buf=null,_=null,h=null,c=null,e.dstate=null,n==C?-3:0),e.deflateParams=(e,t,n)=>{let i=0;return-1==t&&(t=6),0>t||t>9||0>n||n>2?k:(T[N].func!=T[t].func&&0!==e.total_in&&(i=e.deflate(1)),N!=t&&(N=t,M=T[N].max_lazy,O=T[N].good_length,P=T[N].nice_length,L=T[N].max_chain),H=n,i)},e.deflateSetDictionary=(e,t,i)=>{let r,a=i,o=0;if(!t||42!=n)return k;if(3>a)return 0;for(a>s-I&&(a=s-I,o=i-a),c.set(t.subarray(o,o+a),0),E=a,y=a,w=255&c[0],w=(w<<x^255&c[1])&b,r=0;a-3>=r;r++)w=(w<<x^255&c[r+2])&b,h[r&l]=_[w],_[w]=r;return 0},e.deflate=(a,d)=>{let f,u,g,D,B;if(d>4||0>d)return k;if(!a.next_out||!a.next_in&&0!==a.avail_in||n==W&&4!=d)return a.msg=U[4],k;if(0===a.avail_out)return a.msg=U[7],-5;var L;if(t=a,D=r,r=d,42==n&&(u=8+(o-8<<4)<<8,g=(N-1&255)>>1,g>3&&(g=3),u|=g<<6,0!==E&&(u|=32),u+=31-u%31,n=C,ie((L=u)>>8&255),ie(255&L)),0!==e.pending){if(t.flush_pending(),0===t.avail_out)return r=-1,0}else if(0===t.avail_in&&D>=d&&4!=d)return t.msg=U[7],-5;if(n==W&&0!==t.avail_in)return a.msg=U[7],-5;if(0!==t.avail_in||0!==S||0!=d&&n!=W){switch(B=-1,T[N].func){case 0:B=(e=>{let n,r=65535;for(r>i-5&&(r=i-5);;){if(1>=S){if(_e(),0===S&&0==e)return 0;if(0===S)break}if(E+=S,S=0,n=y+r,(0===E||E>=n)&&(S=E-n,E=n,he(!1),0===t.avail_out))return 0;if(E-y>=s-I&&(he(!1),0===t.avail_out))return 0}return he(4==e),0===t.avail_out?4==e?2:0:4==e?3:1})(d);break;case 1:B=(e=>{let n,i=0;for(;;){if(I>S){if(_e(),I>S&&0==e)return 0;if(0===S)break}if(3>S||(w=(w<<x^255&c[E+2])&b,i=65535&_[w],h[E&l]=_[w],_[w]=E),0===i||(E-i&65535)>s-I||2!=H&&(m=we(i)),3>m)n=ce(0,255&c[E]),S--,E++;else if(n=ce(E-F,m-3),S-=m,m>M||3>S)E+=m,m=0,w=255&c[E],w=(w<<x^255&c[E+1])&b;else{m--;do{E++,w=(w<<x^255&c[E+2])&b,i=65535&_[w],h[E&l]=_[w],_[w]=E}while(0!=--m);E++}if(n&&(he(!1),0===t.avail_out))return 0}return he(4==e),0===t.avail_out?4==e?2:0:4==e?3:1})(d);break;case 2:B=(e=>{let n,i,r=0;for(;;){if(I>S){if(_e(),I>S&&0==e)return 0;if(0===S)break}if(3>S||(w=(w<<x^255&c[E+2])&b,r=65535&_[w],h[E&l]=_[w],_[w]=E),z=m,v=F,m=2,0!==r&&M>z&&s-I>=(E-r&65535)&&(2!=H&&(m=we(r)),5>=m&&(1==H||3==m&&E-F>4096)&&(m=2)),3>z||m>z)if(0!==R){if(n=ce(0,255&c[E-1]),n&&he(!1),E++,S--,0===t.avail_out)return 0}else R=1,E++,S--;else{i=E+S-3,n=ce(E-1-v,z-3),S-=z-1,z-=2;do{++E>i||(w=(w<<x^255&c[E+2])&b,r=65535&_[w],h[E&l]=_[w],_[w]=E)}while(0!=--z);if(R=0,m=2,E++,n&&(he(!1),0===t.avail_out))return 0}}return 0!==R&&(n=ce(0,255&c[E-1]),R=0),he(4==e),0===t.avail_out?4==e?2:0:4==e?3:1})(d)}if(2!=B&&3!=B||(n=W),0==B||2==B)return 0===t.avail_out&&(r=-1),0;if(1==B){if(1==d)ae(2,3),se(256,A.static_ltree),le(),9>1+Q+10-ee&&(ae(2,3),se(256,A.static_ltree),le()),Q=7;else if(ue(0,0,!1),3==d)for(f=0;p>f;f++)_[f]=0;if(t.flush_pending(),0===t.avail_out)return r=-1,0}}return 4!=d?0:1}}function M(){const e=this;e.next_in_index=0,e.next_out_index=0,e.avail_in=0,e.total_in=0,e.avail_out=0,e.total_out=0}M.prototype={deflateInit:function(e,t){const n=this;return n.dstate=new L,t||(t=15),n.dstate.deflateInit(n,e,t)},deflate:function(e){const t=this;return t.dstate?t.dstate.deflate(t,e):k},deflateEnd:function(){const e=this;if(!e.dstate)return k;const t=e.dstate.deflateEnd();return e.dstate=null,t},deflateParams:function(e,t){const n=this;return n.dstate?n.dstate.deflateParams(n,e,t):k},deflateSetDictionary:function(e,t){const n=this;return n.dstate?n.dstate.deflateSetDictionary(n,e,t):k},read_buf:function(e,t,n){const i=this;let r=i.avail_in;return r>n&&(r=n),0===r?0:(i.avail_in-=r,e.set(i.next_in.subarray(i.next_in_index,i.next_in_index+r),t),i.next_in_index+=r,i.total_in+=r,r)},flush_pending:function(){const e=this;let t=e.dstate.pending;t>e.avail_out&&(t=e.avail_out),0!==t&&(e.next_out.set(e.dstate.pending_buf.subarray(e.dstate.pending_out,e.dstate.pending_out+t),e.next_out_index),e.next_out_index+=t,e.dstate.pending_out+=t,e.total_out+=t,e.avail_out-=t,e.dstate.pending-=t,0===e.dstate.pending&&(e.dstate.pending_out=0))}};const N=-2,H=-3,O=-5,P=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],V=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],q=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],Z=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],K=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],G=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],Y=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function j(){let e,t,n,i,r,a;function s(e,t,s,o,l,c,d,f,u,h,_){let w,p,g,b,x,y,m,k,v,R,E,D,A,F,S;R=0,x=s;do{n[e[t+R]]++,R++,x--}while(0!==x);if(n[0]==s)return d[0]=-1,f[0]=0,0;for(k=f[0],y=1;15>=y&&0===n[y];y++);for(m=y,y>k&&(k=y),x=15;0!==x&&0===n[x];x--);for(g=x,k>x&&(k=x),f[0]=k,F=1<<y;x>y;y++,F<<=1)if(0>(F-=n[y]))return H;if(0>(F-=n[x]))return H;for(n[x]+=F,a[1]=y=0,R=1,A=2;0!=--x;)a[A]=y+=n[R],A++,R++;x=0,R=0;do{0!==(y=e[t+R])&&(_[a[y]++]=x),R++}while(++x<s);for(s=a[g],a[0]=x=0,R=0,b=-1,D=-k,r[0]=0,E=0,S=0;g>=m;m++)for(w=n[m];0!=w--;){for(;m>D+k;){if(b++,D+=k,S=g-D,S=S>k?k:S,(p=1<<(y=m-D))>w+1&&(p-=w+1,A=m,S>y))for(;++y<S&&(p<<=1)>n[++A];)p-=n[A];if(S=1<<y,h[0]+S>1440)return H;r[b]=E=h[0],h[0]+=S,0!==b?(a[b]=x,i[0]=y,i[1]=k,y=x>>>D-k,i[2]=E-r[b-1]-y,u.set(i,3*(r[b-1]+y))):d[0]=E}for(i[1]=m-D,s>R?_[R]<o?(i[0]=256>_[R]?0:96,i[2]=_[R++]):(i[0]=c[_[R]-o]+16+64,i[2]=l[_[R++]-o]):i[0]=192,p=1<<m-D,y=x>>>D;S>y;y+=p)u.set(i,3*(E+y));for(y=1<<m-1;0!=(x&y);y>>>=1)x^=y;for(x^=y,v=(1<<D)-1;(x&v)!=a[b];)b--,D-=k,v=(1<<D)-1}return 0!==F&&1!=g?O:0}function o(s){let o;for(e||(e=[],t=[],n=new Int32Array(16),i=[],r=new Int32Array(15),a=new Int32Array(16)),t.length<s&&(t=[]),o=0;s>o;o++)t[o]=0;for(o=0;16>o;o++)n[o]=0;for(o=0;3>o;o++)i[o]=0;r.set(n.subarray(0,15),0),a.set(n.subarray(0,16),0)}this.inflate_trees_bits=(n,i,r,a,l)=>{let c;return o(19),e[0]=0,c=s(n,0,19,19,null,null,r,i,a,e,t),c==H?l.msg="oversubscribed dynamic bit lengths tree":c!=O&&0!==i[0]||(l.msg="incomplete dynamic bit lengths tree",c=H),c},this.inflate_trees_dynamic=(n,i,r,a,l,c,d,f,u)=>{let h;return o(288),e[0]=0,h=s(r,0,n,257,Z,K,c,a,f,e,t),0!=h||0===a[0]?(h==H?u.msg="oversubscribed literal/length tree":-4!=h&&(u.msg="incomplete literal/length tree",h=H),h):(o(288),h=s(r,n,i,0,G,Y,d,l,f,e,t),0!=h||0===l[0]&&n>257?(h==H?u.msg="oversubscribed distance tree":h==O?(u.msg="incomplete distance tree",h=H):-4!=h&&(u.msg="empty distance tree with lengths",h=H),h):0)}}function X(){const e=this;let t,n,i,r,a=0,s=0,o=0,l=0,c=0,d=0,f=0,u=0,h=0,_=0;function w(e,t,n,i,r,a,s,o){let l,c,d,f,u,h,_,w,p,g,b,x,y,m,k,v;_=o.next_in_index,w=o.avail_in,u=s.bitb,h=s.bitk,p=s.write,g=p<s.read?s.read-p-1:s.end-p,b=P[e],x=P[t];do{for(;20>h;)w--,u|=(255&o.read_byte(_++))<<h,h+=8;if(l=u&b,c=n,d=i,v=3*(d+l),0!==(f=c[v]))for(;;){if(u>>=c[v+1],h-=c[v+1],0!=(16&f)){for(f&=15,y=c[v+2]+(u&P[f]),u>>=f,h-=f;15>h;)w--,u|=(255&o.read_byte(_++))<<h,h+=8;for(l=u&x,c=r,d=a,v=3*(d+l),f=c[v];;){if(u>>=c[v+1],h-=c[v+1],0!=(16&f)){for(f&=15;f>h;)w--,u|=(255&o.read_byte(_++))<<h,h+=8;if(m=c[v+2]+(u&P[f]),u>>=f,h-=f,g-=y,m>p){k=p-m;do{k+=s.end}while(0>k);if(f=s.end-k,y>f){if(y-=f,p-k>0&&f>p-k)do{s.win[p++]=s.win[k++]}while(0!=--f);else s.win.set(s.win.subarray(k,k+f),p),p+=f,k+=f,f=0;k=0}}else k=p-m,p-k>0&&2>p-k?(s.win[p++]=s.win[k++],s.win[p++]=s.win[k++],y-=2):(s.win.set(s.win.subarray(k,k+2),p),p+=2,k+=2,y-=2);if(p-k>0&&y>p-k)do{s.win[p++]=s.win[k++]}while(0!=--y);else s.win.set(s.win.subarray(k,k+y),p),p+=y,k+=y,y=0;break}if(0!=(64&f))return o.msg="invalid distance code",y=o.avail_in-w,y=y>h>>3?h>>3:y,w+=y,_-=y,h-=y<<3,s.bitb=u,s.bitk=h,o.avail_in=w,o.total_in+=_-o.next_in_index,o.next_in_index=_,s.write=p,H;l+=c[v+2],l+=u&P[f],v=3*(d+l),f=c[v]}break}if(0!=(64&f))return 0!=(32&f)?(y=o.avail_in-w,y=y>h>>3?h>>3:y,w+=y,_-=y,h-=y<<3,s.bitb=u,s.bitk=h,o.avail_in=w,o.total_in+=_-o.next_in_index,o.next_in_index=_,s.write=p,1):(o.msg="invalid literal/length code",y=o.avail_in-w,y=y>h>>3?h>>3:y,w+=y,_-=y,h-=y<<3,s.bitb=u,s.bitk=h,o.avail_in=w,o.total_in+=_-o.next_in_index,o.next_in_index=_,s.write=p,H);if(l+=c[v+2],l+=u&P[f],v=3*(d+l),0===(f=c[v])){u>>=c[v+1],h-=c[v+1],s.win[p++]=c[v+2],g--;break}}else u>>=c[v+1],h-=c[v+1],s.win[p++]=c[v+2],g--}while(g>=258&&w>=10);return y=o.avail_in-w,y=y>h>>3?h>>3:y,w+=y,_-=y,h-=y<<3,s.bitb=u,s.bitk=h,o.avail_in=w,o.total_in+=_-o.next_in_index,o.next_in_index=_,s.write=p,0}e.init=(e,a,s,o,l,c)=>{t=0,f=e,u=a,i=s,h=o,r=l,_=c,n=null},e.proc=(e,p,g)=>{let b,x,y,m,k,v,R,E=0,D=0,A=0;for(A=p.next_in_index,m=p.avail_in,E=e.bitb,D=e.bitk,k=e.write,v=k<e.read?e.read-k-1:e.end-k;;)switch(t){case 0:if(v>=258&&m>=10&&(e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,g=w(f,u,i,h,r,_,e,p),A=p.next_in_index,m=p.avail_in,E=e.bitb,D=e.bitk,k=e.write,v=k<e.read?e.read-k-1:e.end-k,0!=g)){t=1==g?7:9;break}o=f,n=i,s=h,t=1;case 1:for(b=o;b>D;){if(0===m)return e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);g=0,m--,E|=(255&p.read_byte(A++))<<D,D+=8}if(x=3*(s+(E&P[b])),E>>>=n[x+1],D-=n[x+1],y=n[x],0===y){l=n[x+2],t=6;break}if(0!=(16&y)){c=15&y,a=n[x+2],t=2;break}if(0==(64&y)){o=y,s=x/3+n[x+2];break}if(0!=(32&y)){t=7;break}return t=9,p.msg="invalid literal/length code",g=H,e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);case 2:for(b=c;b>D;){if(0===m)return e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);g=0,m--,E|=(255&p.read_byte(A++))<<D,D+=8}a+=E&P[b],E>>=b,D-=b,o=u,n=r,s=_,t=3;case 3:for(b=o;b>D;){if(0===m)return e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);g=0,m--,E|=(255&p.read_byte(A++))<<D,D+=8}if(x=3*(s+(E&P[b])),E>>=n[x+1],D-=n[x+1],y=n[x],0!=(16&y)){c=15&y,d=n[x+2],t=4;break}if(0==(64&y)){o=y,s=x/3+n[x+2];break}return t=9,p.msg="invalid distance code",g=H,e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);case 4:for(b=c;b>D;){if(0===m)return e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);g=0,m--,E|=(255&p.read_byte(A++))<<D,D+=8}d+=E&P[b],E>>=b,D-=b,t=5;case 5:for(R=k-d;0>R;)R+=e.end;for(;0!==a;){if(0===v&&(k==e.end&&0!==e.read&&(k=0,v=k<e.read?e.read-k-1:e.end-k),0===v&&(e.write=k,g=e.inflate_flush(p,g),k=e.write,v=k<e.read?e.read-k-1:e.end-k,k==e.end&&0!==e.read&&(k=0,v=k<e.read?e.read-k-1:e.end-k),0===v)))return e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);e.win[k++]=e.win[R++],v--,R==e.end&&(R=0),a--}t=0;break;case 6:if(0===v&&(k==e.end&&0!==e.read&&(k=0,v=k<e.read?e.read-k-1:e.end-k),0===v&&(e.write=k,g=e.inflate_flush(p,g),k=e.write,v=k<e.read?e.read-k-1:e.end-k,k==e.end&&0!==e.read&&(k=0,v=k<e.read?e.read-k-1:e.end-k),0===v)))return e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);g=0,e.win[k++]=l,v--,t=0;break;case 7:if(D>7&&(D-=8,m++,A--),e.write=k,g=e.inflate_flush(p,g),k=e.write,v=k<e.read?e.read-k-1:e.end-k,e.read!=e.write)return e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);t=8;case 8:return g=1,e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);case 9:return g=H,e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g);default:return g=N,e.bitb=E,e.bitk=D,p.avail_in=m,p.total_in+=A-p.next_in_index,p.next_in_index=A,e.write=k,e.inflate_flush(p,g)}},e.free=()=>{}}j.inflate_trees_fixed=(e,t,n,i)=>(e[0]=9,t[0]=5,n[0]=V,i[0]=q,0);const J=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function Q(e,t){const n=this;let i,r=0,a=0,s=0,o=0;const l=[0],c=[0],f=new X;let u=0,h=new Int32Array(4320);const _=new j;n.bitk=0,n.bitb=0,n.win=new d(t),n.end=t,n.read=0,n.write=0,n.reset=(e,t)=>{t&&(t[0]=0),6==r&&f.free(e),r=0,n.bitk=0,n.bitb=0,n.read=n.write=0},n.reset(e,null),n.inflate_flush=(e,t)=>{let i,r,a;return r=e.next_out_index,a=n.read,i=(a>n.write?n.end:n.write)-a,i>e.avail_out&&(i=e.avail_out),0!==i&&t==O&&(t=0),e.avail_out-=i,e.total_out+=i,e.next_out.set(n.win.subarray(a,a+i),r),r+=i,a+=i,a==n.end&&(a=0,n.write==n.end&&(n.write=0),i=n.write-a,i>e.avail_out&&(i=e.avail_out),0!==i&&t==O&&(t=0),e.avail_out-=i,e.total_out+=i,e.next_out.set(n.win.subarray(a,a+i),r),r+=i,a+=i),e.next_out_index=r,n.read=a,t},n.proc=(e,t)=>{let d,w,p,g,b,x,y,m;for(g=e.next_in_index,b=e.avail_in,w=n.bitb,p=n.bitk,x=n.write,y=x<n.read?n.read-x-1:n.end-x;;){let k,v,R,E,D,A,F,S;switch(r){case 0:for(;3>p;){if(0===b)return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);t=0,b--,w|=(255&e.read_byte(g++))<<p,p+=8}switch(d=7&w,u=1&d,d>>>1){case 0:w>>>=3,p-=3,d=7&p,w>>>=d,p-=d,r=1;break;case 1:k=[],v=[],R=[[]],E=[[]],j.inflate_trees_fixed(k,v,R,E),f.init(k[0],v[0],R[0],0,E[0],0),w>>>=3,p-=3,r=6;break;case 2:w>>>=3,p-=3,r=3;break;case 3:return w>>>=3,p-=3,r=9,e.msg="invalid block type",t=H,n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t)}break;case 1:for(;32>p;){if(0===b)return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);t=0,b--,w|=(255&e.read_byte(g++))<<p,p+=8}if((~w>>>16&65535)!=(65535&w))return r=9,e.msg="invalid stored block lengths",t=H,n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);a=65535&w,w=p=0,r=0!==a?2:0!==u?7:0;break;case 2:if(0===b)return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);if(0===y&&(x==n.end&&0!==n.read&&(x=0,y=x<n.read?n.read-x-1:n.end-x),0===y&&(n.write=x,t=n.inflate_flush(e,t),x=n.write,y=x<n.read?n.read-x-1:n.end-x,x==n.end&&0!==n.read&&(x=0,y=x<n.read?n.read-x-1:n.end-x),0===y)))return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);if(t=0,d=a,d>b&&(d=b),d>y&&(d=y),n.win.set(e.read_buf(g,d),x),g+=d,b-=d,x+=d,y-=d,0!=(a-=d))break;r=0!==u?7:0;break;case 3:for(;14>p;){if(0===b)return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);t=0,b--,w|=(255&e.read_byte(g++))<<p,p+=8}if(s=d=16383&w,(31&d)>29||(d>>5&31)>29)return r=9,e.msg="too many length or distance symbols",t=H,n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);if(d=258+(31&d)+(d>>5&31),!i||i.length<d)i=[];else for(m=0;d>m;m++)i[m]=0;w>>>=14,p-=14,o=0,r=4;case 4:for(;4+(s>>>10)>o;){for(;3>p;){if(0===b)return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);t=0,b--,w|=(255&e.read_byte(g++))<<p,p+=8}i[J[o++]]=7&w,w>>>=3,p-=3}for(;19>o;)i[J[o++]]=0;if(l[0]=7,d=_.inflate_trees_bits(i,l,c,h,e),0!=d)return(t=d)==H&&(i=null,r=9),n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);o=0,r=5;case 5:for(;d=s,258+(31&d)+(d>>5&31)>o;){let a,f;for(d=l[0];d>p;){if(0===b)return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);t=0,b--,w|=(255&e.read_byte(g++))<<p,p+=8}if(d=h[3*(c[0]+(w&P[d]))+1],f=h[3*(c[0]+(w&P[d]))+2],16>f)w>>>=d,p-=d,i[o++]=f;else{for(m=18==f?7:f-14,a=18==f?11:3;d+m>p;){if(0===b)return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);t=0,b--,w|=(255&e.read_byte(g++))<<p,p+=8}if(w>>>=d,p-=d,a+=w&P[m],w>>>=m,p-=m,m=o,d=s,m+a>258+(31&d)+(d>>5&31)||16==f&&1>m)return i=null,r=9,e.msg="invalid bit length repeat",t=H,n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);f=16==f?i[m-1]:0;do{i[m++]=f}while(0!=--a);o=m}}if(c[0]=-1,D=[],A=[],F=[],S=[],D[0]=9,A[0]=6,d=s,d=_.inflate_trees_dynamic(257+(31&d),1+(d>>5&31),i,D,A,F,S,h,e),0!=d)return d==H&&(i=null,r=9),t=d,n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);f.init(D[0],A[0],h,F[0],h,S[0]),r=6;case 6:if(n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,1!=(t=f.proc(n,e,t)))return n.inflate_flush(e,t);if(t=0,f.free(e),g=e.next_in_index,b=e.avail_in,w=n.bitb,p=n.bitk,x=n.write,y=x<n.read?n.read-x-1:n.end-x,0===u){r=0;break}r=7;case 7:if(n.write=x,t=n.inflate_flush(e,t),x=n.write,y=x<n.read?n.read-x-1:n.end-x,n.read!=n.write)return n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);r=8;case 8:return t=1,n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);case 9:return t=H,n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t);default:return t=N,n.bitb=w,n.bitk=p,e.avail_in=b,e.total_in+=g-e.next_in_index,e.next_in_index=g,n.write=x,n.inflate_flush(e,t)}}},n.free=e=>{n.reset(e,null),n.win=null,h=null},n.set_dictionary=(e,t,i)=>{n.win.set(e.subarray(t,t+i),0),n.read=n.write=i},n.sync_point=()=>1==r?1:0}const $=13,ee=[0,0,255,255];function te(){const e=this;function t(e){return e&&e.istate?(e.total_in=e.total_out=0,e.msg=null,e.istate.mode=7,e.istate.blocks.reset(e,null),0):N}e.mode=0,e.method=0,e.was=[0],e.need=0,e.marker=0,e.wbits=0,e.inflateEnd=t=>(e.blocks&&e.blocks.free(t),e.blocks=null,0),e.inflateInit=(n,i)=>(n.msg=null,e.blocks=null,8>i||i>15?(e.inflateEnd(n),N):(e.wbits=i,n.istate.blocks=new Q(n,1<<i),t(n),0)),e.inflate=(e,t)=>{let n,i;if(!e||!e.istate||!e.next_in)return N;const r=e.istate;for(t=4==t?O:0,n=O;;)switch(r.mode){case 0:if(0===e.avail_in)return n;if(n=t,e.avail_in--,e.total_in++,8!=(15&(r.method=e.read_byte(e.next_in_index++)))){r.mode=$,e.msg="unknown compression method",r.marker=5;break}if(8+(r.method>>4)>r.wbits){r.mode=$,e.msg="invalid win size",r.marker=5;break}r.mode=1;case 1:if(0===e.avail_in)return n;if(n=t,e.avail_in--,e.total_in++,i=255&e.read_byte(e.next_in_index++),((r.method<<8)+i)%31!=0){r.mode=$,e.msg="incorrect header check",r.marker=5;break}if(0==(32&i)){r.mode=7;break}r.mode=2;case 2:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,r.need=(255&e.read_byte(e.next_in_index++))<<24&4278190080,r.mode=3;case 3:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,r.need+=(255&e.read_byte(e.next_in_index++))<<16&16711680,r.mode=4;case 4:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,r.need+=(255&e.read_byte(e.next_in_index++))<<8&65280,r.mode=5;case 5:return 0===e.avail_in?n:(n=t,e.avail_in--,e.total_in++,r.need+=255&e.read_byte(e.next_in_index++),r.mode=6,2);case 6:return r.mode=$,e.msg="need dictionary",r.marker=0,N;case 7:if(n=r.blocks.proc(e,n),n==H){r.mode=$,r.marker=0;break}if(0==n&&(n=t),1!=n)return n;n=t,r.blocks.reset(e,r.was),r.mode=12;case 12:return e.avail_in=0,1;case $:return H;default:return N}},e.inflateSetDictionary=(e,t,n)=>{let i=0,r=n;if(!e||!e.istate||6!=e.istate.mode)return N;const a=e.istate;return r<1<<a.wbits||(r=(1<<a.wbits)-1,i=n-r),a.blocks.set_dictionary(t,i,r),a.mode=7,0},e.inflateSync=e=>{let n,i,r,a,s;if(!e||!e.istate)return N;const o=e.istate;if(o.mode!=$&&(o.mode=$,o.marker=0),0===(n=e.avail_in))return O;for(i=e.next_in_index,r=o.marker;0!==n&&4>r;)e.read_byte(i)==ee[r]?r++:r=0!==e.read_byte(i)?0:4-r,i++,n--;return e.total_in+=i-e.next_in_index,e.next_in_index=i,e.avail_in=n,o.marker=r,4!=r?H:(a=e.total_in,s=e.total_out,t(e),e.total_in=a,e.total_out=s,o.mode=7,0)},e.inflateSyncPoint=e=>e&&e.istate&&e.istate.blocks?e.istate.blocks.sync_point():N}function ne(){}ne.prototype={inflateInit:function(e){const t=this;return t.istate=new te,e||(e=15),t.istate.inflateInit(t,e)},inflate:function(e){const t=this;return t.istate?t.istate.inflate(t,e):N},inflateEnd:function(){const e=this;if(!e.istate)return N;const t=e.istate.inflateEnd(e);return e.istate=null,t},inflateSync:function(){const e=this;return e.istate?e.istate.inflateSync(e):N},inflateSetDictionary:function(e,t){const n=this;return n.istate?n.istate.inflateSetDictionary(n,e,t):N},read_byte:function(e){return this.next_in[e]},read_buf:function(e,t){return this.next_in.subarray(e,e+t)}};const ie={chunkSize:524288,maxWorkers:"undefined"!=typeof navigator&&navigator.hardwareConcurrency||2,terminateWorkerTimeout:5e3,useWebWorkers:!0,workerScripts:void 0},re=n.assign({},ie);function ae(){return re}function se(e){if(void 0!==e.baseURL&&(re.baseURL=e.baseURL),void 0!==e.chunkSize&&(re.chunkSize=e.chunkSize),void 0!==e.maxWorkers&&(re.maxWorkers=e.maxWorkers),void 0!==e.terminateWorkerTimeout&&(re.terminateWorkerTimeout=e.terminateWorkerTimeout),void 0!==e.useWebWorkers&&(re.useWebWorkers=e.useWebWorkers),void 0!==e.Deflate&&(re.Deflate=e.Deflate),void 0!==e.Inflate&&(re.Inflate=e.Inflate),void 0!==e.workerScripts){if(e.workerScripts.deflate){if(!t.isArray(e.workerScripts.deflate))throw new c("workerScripts.deflate must be an array");re.workerScripts||(re.workerScripts={}),re.workerScripts.deflate=e.workerScripts.deflate}if(e.workerScripts.inflate){if(!t.isArray(e.workerScripts.inflate))throw new c("workerScripts.inflate must be an array");re.workerScripts||(re.workerScripts={}),re.workerScripts.inflate=e.workerScripts.inflate}}}const oe=[];for(let e=0;256>e;e++){let t=e;for(let e=0;8>e;e++)1&t?t=t>>>1^3988292384:t>>>=1;oe[e]=t}class le{constructor(e){this.crc=e||-1}append(e){let t=0|this.crc;for(let n=0,i=0|e.length;i>n;n++)t=t>>>8^oe[255&(t^e[n])];this.crc=t}get(){return~this.crc}}function ce(e){if(void 0===p){const t=new d((e=unescape(encodeURIComponent(e))).length);for(let n=0;n<t.length;n++)t[n]=e.charCodeAt(n);return t}return(new p).encode(e)}const de={concat(e,t){if(0===e.length||0===t.length)return e.concat(t);const n=e[e.length-1],i=de.getPartial(n);return 32===i?e.concat(t):de._shiftRight(t,i,0|n,e.slice(0,e.length-1))},bitLength(e){const t=e.length;if(0===t)return 0;const n=e[t-1];return 32*(t-1)+de.getPartial(n)},clamp(e,t){if(32*e.length<t)return e;const n=(e=e.slice(0,a.ceil(t/32))).length;return t&=31,n>0&&t&&(e[n-1]=de.partial(t,e[n-1]&2147483648>>t-1,1)),e},partial:(e,t,n)=>32===e?t:(n?0|t:t<<32-e)+1099511627776*e,getPartial:e=>a.round(e/1099511627776)||32,_shiftRight(e,t,n,i){for(void 0===i&&(i=[]);t>=32;t-=32)i.push(n),n=0;if(0===t)return i.concat(e);for(let r=0;r<e.length;r++)i.push(n|e[r]>>>t),n=e[r]<<32-t;const r=e.length?e[e.length-1]:0,a=de.getPartial(r);return i.push(de.partial(t+a&31,t+a>32?n:i.pop(),1)),i}},fe={bytes:{fromBits(e){const t=de.bitLength(e)/8,n=new d(t);let i;for(let r=0;t>r;r++)0==(3&r)&&(i=e[r/4]),n[r]=i>>>24,i<<=8;return n},toBits(e){const t=[];let n,i=0;for(n=0;n<e.length;n++)i=i<<8|e[n],3==(3&n)&&(t.push(i),i=0);return 3&n&&t.push(de.partial(8*(3&n),i)),t}}},ue={sha1:function(e){e?(this._h=e._h.slice(0),this._buffer=e._buffer.slice(0),this._length=e._length):this.reset()}};ue.sha1.prototype={blockSize:512,reset:function(){const e=this;return e._h=this._init.slice(0),e._buffer=[],e._length=0,e},update:function(e){const t=this;"string"==typeof e&&(e=fe.utf8String.toBits(e));const n=t._buffer=de.concat(t._buffer,e),i=t._length,r=t._length=i+de.bitLength(e);if(r>9007199254740991)throw new c("Cannot hash more than 2^53 - 1 bits");const a=new u(n);let s=0;for(let e=t.blockSize+i-(t.blockSize+i&t.blockSize-1);r>=e;e+=t.blockSize)t._block(a.subarray(16*s,16*(s+1))),s+=1;return n.splice(0,16*s),t},finalize:function(){const e=this;let t=e._buffer;const n=e._h;t=de.concat(t,[de.partial(1,1)]);for(let e=t.length+2;15&e;e++)t.push(0);for(t.push(a.floor(e._length/4294967296)),t.push(0|e._length);t.length;)e._block(t.splice(0,16));return e.reset(),n},_init:[1732584193,4023233417,2562383102,271733878,3285377520],_key:[1518500249,1859775393,2400959708,3395469782],_f:(e,t,n,i)=>e>19?e>39?e>59?e>79?void 0:t^n^i:t&n|t&i|n&i:t^n^i:t&n|~t&i,_S:(e,t)=>t<<e|t>>>32-e,_block:function(e){const n=this,i=n._h,r=t(80);for(let t=0;16>t;t++)r[t]=e[t];let s=i[0],o=i[1],l=i[2],c=i[3],d=i[4];for(let e=0;79>=e;e++){16>e||(r[e]=n._S(1,r[e-3]^r[e-8]^r[e-14]^r[e-16]));const t=n._S(5,s)+n._f(e,o,l,c)+d+r[e]+n._key[a.floor(e/20)]|0;d=c,c=l,l=n._S(30,o),o=s,s=t}i[0]=i[0]+s|0,i[1]=i[1]+o|0,i[2]=i[2]+l|0,i[3]=i[3]+c|0,i[4]=i[4]+d|0}};const he={getRandomValues(e){const t=new u(e.buffer),n=e=>{let t=987654321;const n=4294967295;return()=>(t=36969*(65535&t)+(t>>16)&n,(((t<<16)+(e=18e3*(65535&e)+(e>>16)&n)&n)/4294967296+.5)*(a.random()>.5?1:-1))};for(let i,r=0;r<e.length;r+=4){let e=n(4294967296*(i||a.random()));i=987654071*e(),t[r/4]=4294967296*e()|0}return e}},_e={importKey:e=>new _e.hmacSha1(fe.bytes.toBits(e)),pbkdf2(e,t,n,i){if(n=n||1e4,0>i||0>n)throw new c("invalid params to pbkdf2");const r=1+(i>>5)<<2;let a,s,o,l,d;const f=new ArrayBuffer(r);let u=new h(f),_=0;const w=de;for(t=fe.bytes.toBits(t),d=1;(r||1)>_;d++){for(a=s=e.encrypt(w.concat(t,[d])),o=1;n>o;o++)for(s=e.encrypt(s),l=0;l<s.length;l++)a[l]^=s[l];for(o=0;(r||1)>_&&o<a.length;o++)u.setInt32(_,a[o]),_+=4}return f.slice(0,i/8)},hmacSha1:class{constructor(e){const t=this,n=t._hash=ue.sha1,i=[[],[]],r=n.prototype.blockSize/32;t._baseHash=[new n,new n],e.length>r&&(e=n.hash(e));for(let t=0;r>t;t++)i[0][t]=909522486^e[t],i[1][t]=1549556828^e[t];t._baseHash[0].update(i[0]),t._baseHash[1].update(i[1]),t._resultHash=new n(t._baseHash[0])}reset(){const e=this;e._resultHash=new e._hash(e._baseHash[0]),e._updated=!1}update(e){this._updated=!0,this._resultHash.update(e)}digest(){const e=this,t=e._resultHash.finalize(),n=new e._hash(e._baseHash[1]).update(t).finalize();return e.reset(),n}encrypt(e){if(this._updated)throw new c("encrypt on already updated hmac called!");return this.update(e),this.digest(e)}}},we="Invalid pasword",pe=16,ge={name:"PBKDF2"},be=n.assign({hash:{name:"HMAC"}},ge),xe=n.assign({iterations:1e3,hash:{name:"SHA-1"}},ge),ye=["deriveBits"],me=[8,12,16],ke=[16,24,32],ve=10,Re=[0,0,0,0],Ee=void 0!==y,De=Ee&&void 0!==y.subtle,Ae=fe.bytes,Fe=class{constructor(e){const t=this;t._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],t._tables[0][0][0]||t._precompute();const n=t._tables[0][4],i=t._tables[1],r=e.length;let a,s,o,l=1;if(4!==r&&6!==r&&8!==r)throw new c("invalid aes key size");for(t._key=[s=e.slice(0),o=[]],a=r;4*r+28>a;a++){let e=s[a-1];(a%r==0||8===r&&a%r==4)&&(e=n[e>>>24]<<24^n[e>>16&255]<<16^n[e>>8&255]<<8^n[255&e],a%r==0&&(e=e<<8^e>>>24^l<<24,l=l<<1^283*(l>>7))),s[a]=s[a-r]^e}for(let e=0;a;e++,a--){const t=s[3&e?a:a-4];o[e]=4>=a||4>e?t:i[0][n[t>>>24]]^i[1][n[t>>16&255]]^i[2][n[t>>8&255]]^i[3][n[255&t]]}}encrypt(e){return this._crypt(e,0)}decrypt(e){return this._crypt(e,1)}_precompute(){const e=this._tables[0],t=this._tables[1],n=e[4],i=t[4],r=[],a=[];let s,o,l,c;for(let e=0;256>e;e++)a[(r[e]=e<<1^283*(e>>7))^e]=e;for(let d=s=0;!n[d];d^=o||1,s=a[s]||1){let a=s^s<<1^s<<2^s<<3^s<<4;a=a>>8^255&a^99,n[d]=a,i[a]=d,c=r[l=r[o=r[d]]];let f=16843009*c^65537*l^257*o^16843008*d,u=257*r[a]^16843008*a;for(let n=0;4>n;n++)e[n][d]=u=u<<24^u>>>8,t[n][a]=f=f<<24^f>>>8}for(let n=0;5>n;n++)e[n]=e[n].slice(0),t[n]=t[n].slice(0)}_crypt(e,t){if(4!==e.length)throw new c("invalid aes block size");const n=this._key[t],i=n.length/4-2,r=[0,0,0,0],a=this._tables[t],s=a[0],o=a[1],l=a[2],d=a[3],f=a[4];let u,h,_,w=e[0]^n[0],p=e[t?3:1]^n[1],g=e[2]^n[2],b=e[t?1:3]^n[3],x=4;for(let e=0;i>e;e++)u=s[w>>>24]^o[p>>16&255]^l[g>>8&255]^d[255&b]^n[x],h=s[p>>>24]^o[g>>16&255]^l[b>>8&255]^d[255&w]^n[x+1],_=s[g>>>24]^o[b>>16&255]^l[w>>8&255]^d[255&p]^n[x+2],b=s[b>>>24]^o[w>>16&255]^l[p>>8&255]^d[255&g]^n[x+3],x+=4,w=u,p=h,g=_;for(let e=0;4>e;e++)r[t?3&-e:e]=f[w>>>24]<<24^f[p>>16&255]<<16^f[g>>8&255]<<8^f[255&b]^n[x++],u=w,w=p,p=g,g=b,b=u;return r}},Se=class{constructor(e,t){this._prf=e,this._initIv=t,this._iv=t}reset(){this._iv=this._initIv}update(e){return this.calculate(this._prf,e,this._iv)}incWord(e){if(255==(e>>24&255)){let t=e>>16&255,n=e>>8&255,i=255&e;255===t?(t=0,255===n?(n=0,255===i?i=0:++i):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=i}else e+=1<<24;return e}incCounter(e){0===(e[0]=this.incWord(e[0]))&&(e[1]=this.incWord(e[1]))}calculate(e,t,n){let i;if(!(i=t.length))return[];const r=de.bitLength(t);for(let r=0;i>r;r+=4){this.incCounter(n);const i=e.encrypt(n);t[r]^=i[0],t[r+1]^=i[1],t[r+2]^=i[2],t[r+3]^=i[3]}return de.clamp(t,r)}},ze=_e.hmacSha1;class Te{constructor(e,t,i){n.assign(this,{password:e,signed:t,strength:i-1,pendingInput:new d(0)})}async append(e){const n=this;if(n.password){const i=Be(e,0,me[n.strength]+2);await(async(e,t,n)=>{await We(e,n,Be(t,0,me[e.strength]));const i=Be(t,me[e.strength]),r=e.keys.passwordVerification;if(r[0]!=i[0]||r[1]!=i[1])throw new c(we)})(n,i,n.password),n.password=null,n.aesCtrGladman=new Se(new Fe(n.keys.key),t.from(Re)),n.hmac=new ze(n.keys.authentication),e=Be(e,me[n.strength]+2)}return Ce(n,e,new d(e.length-ve-(e.length-ve)%pe),0,ve,!0)}flush(){const e=this,t=e.pendingInput,n=Be(t,0,t.length-ve),i=Be(t,t.length-ve);let r=new d(0);if(n.length){const t=Ae.toBits(n);e.hmac.update(t);const i=e.aesCtrGladman.update(t);r=Ae.fromBits(i)}let a=!0;if(e.signed){const t=Be(Ae.fromBits(e.hmac.digest()),0,ve);for(let e=0;ve>e;e++)t[e]!=i[e]&&(a=!1)}return{valid:a,data:r}}}class Ue{constructor(e,t){n.assign(this,{password:e,strength:t-1,pendingInput:new d(0)})}async append(e){const n=this;let i=new d(0);n.password&&(i=await(async(e,t)=>{const n=(i=new d(me[e.strength]),Ee&&"function"==typeof y.getRandomValues?y.getRandomValues(i):he.getRandomValues(i));var i;return await We(e,t,n),Ie(n,e.keys.passwordVerification)})(n,n.password),n.password=null,n.aesCtrGladman=new Se(new Fe(n.keys.key),t.from(Re)),n.hmac=new ze(n.keys.authentication));const r=new d(i.length+e.length-e.length%pe);return r.set(i,0),Ce(n,e,r,i.length,0)}flush(){const e=this;let t=new d(0);if(e.pendingInput.length){const n=e.aesCtrGladman.update(Ae.toBits(e.pendingInput));e.hmac.update(n),t=Ae.fromBits(n)}const n=Be(Ae.fromBits(e.hmac.digest()),0,ve);return{data:Ie(t,n),signature:n}}}function Ce(e,t,n,i,r,a){const s=t.length-r;let o;for(e.pendingInput.length&&(t=Ie(e.pendingInput,t),n=((e,t)=>{if(t&&t>e.length){const n=e;(e=new d(t)).set(n,0)}return e})(n,s-s%pe)),o=0;s-pe>=o;o+=pe){const r=Ae.toBits(Be(t,o,o+pe));a&&e.hmac.update(r);const s=e.aesCtrGladman.update(r);a||e.hmac.update(s),n.set(Ae.fromBits(s),o+i)}return e.pendingInput=Be(t,o),n}async function We(e,t,i){const r=ce(t),a=await(async(e,t,n,i,r)=>Ee&&De&&"function"==typeof y.subtle.importKey?y.subtle.importKey("raw",t,n,!1,r):_e.importKey(t))(0,r,be,0,ye),s=await(async(e,t,n)=>Ee&&De&&"function"==typeof y.subtle.deriveBits?await y.subtle.deriveBits(e,t,n):_e.pbkdf2(t,e.salt,xe.iterations,n))(n.assign({salt:i},xe),a,8*(2*ke[e.strength]+2)),o=new d(s);e.keys={key:Ae.toBits(Be(o,0,ke[e.strength])),authentication:Ae.toBits(Be(o,ke[e.strength],2*ke[e.strength])),passwordVerification:Be(o,2*ke[e.strength])}}function Ie(e,t){let n=e;return e.length+t.length&&(n=new d(e.length+t.length),n.set(e,0),n.set(t,e.length)),n}function Be(e,t,n){return e.subarray(t,n)}class Le{constructor(e,t){n.assign(this,{password:e,passwordVerification:t}),Oe(this,e)}append(e){const t=this;if(t.password){const n=Ne(t,e.subarray(0,12));if(t.password=null,n[11]!=t.passwordVerification)throw new c(we);e=e.subarray(12)}return Ne(t,e)}flush(){return{valid:!0,data:new d(0)}}}class Me{constructor(e,t){n.assign(this,{password:e,passwordVerification:t}),Oe(this,e)}append(e){const t=this;let n,i;if(t.password){t.password=null;const r=y.getRandomValues(new d(12));r[11]=t.passwordVerification,n=new d(e.length+r.length),n.set(He(t,r),0),i=12}else n=new d(e.length),i=0;return n.set(He(t,e),i),n}flush(){return{data:new d(0)}}}function Ne(e,t){const n=new d(t.length);for(let i=0;i<t.length;i++)n[i]=Ve(e)^t[i],Pe(e,n[i]);return n}function He(e,t){const n=new d(t.length);for(let i=0;i<t.length;i++)n[i]=Ve(e)^t[i],Pe(e,t[i]);return n}function Oe(e,t){e.keys=[305419896,591751049,878082192],e.crcKey0=new le(e.keys[0]),e.crcKey2=new le(e.keys[2]);for(let n=0;n<t.length;n++)Pe(e,t.charCodeAt(n))}function Pe(e,t){e.crcKey0.append([t]),e.keys[0]=~e.crcKey0.get(),e.keys[1]=Ze(e.keys[1]+qe(e.keys[0])),e.keys[1]=Ze(a.imul(e.keys[1],134775813)+1),e.crcKey2.append([e.keys[1]>>>24]),e.keys[2]=~e.crcKey2.get()}function Ve(e){const t=2|e.keys[2];return qe(a.imul(t,1^t)>>>8)}function qe(e){return 255&e}function Ze(e){return 4294967295&e}const Ke="deflate",Ge="inflate",Ye="Invalid signature";class je{constructor(e,{signature:t,password:i,signed:r,compressed:a,zipCrypto:s,passwordVerification:o,encryptionStrength:l},{chunkSize:c}){const d=!!i;n.assign(this,{signature:t,encrypted:d,signed:r,compressed:a,inflate:a&&new e({chunkSize:c}),crc32:r&&new le,zipCrypto:s,decrypt:d&&s?new Le(i,o):new Te(i,r,l)})}async append(e){const t=this;return t.encrypted&&e.length&&(e=await t.decrypt.append(e)),t.compressed&&e.length&&(e=await t.inflate.append(e)),(!t.encrypted||t.zipCrypto)&&t.signed&&e.length&&t.crc32.append(e),e}async flush(){const e=this;let t,n=new d(0);if(e.encrypted){const t=e.decrypt.flush();if(!t.valid)throw new c(Ye);n=t.data}if((!e.encrypted||e.zipCrypto)&&e.signed){const n=new h(new d(4).buffer);if(t=e.crc32.get(),n.setUint32(0,t),e.signature!=n.getUint32(0,!1))throw new c(Ye)}return e.compressed&&(n=await e.inflate.append(n)||new d(0),await e.inflate.flush()),{data:n,signature:t}}}class Xe{constructor(e,{encrypted:t,signed:i,compressed:r,level:a,zipCrypto:s,password:o,passwordVerification:l,encryptionStrength:c},{chunkSize:d}){n.assign(this,{encrypted:t,signed:i,compressed:r,deflate:r&&new e({level:a||5,chunkSize:d}),crc32:i&&new le,zipCrypto:s,encrypt:t&&s?new Me(o,l):new Ue(o,c)})}async append(e){const t=this;let n=e;return t.compressed&&e.length&&(n=await t.deflate.append(e)),t.encrypted&&n.length&&(n=await t.encrypt.append(n)),(!t.encrypted||t.zipCrypto)&&t.signed&&e.length&&t.crc32.append(e),n}async flush(){const e=this;let t,n=new d(0);if(e.compressed&&(n=await e.deflate.flush()||new d(0)),e.encrypted){n=await e.encrypt.append(n);const i=e.encrypt.flush();t=i.signature;const r=new d(n.length+i.data.length);r.set(n,0),r.set(i.data,n.length),n=r}return e.encrypted&&!e.zipCrypto||!e.signed||(t=e.crc32.get()),{data:n,signature:t}}}const Je="init",Qe="append",$e="flush";let et=!0;var tt=(e,t,i,r,a,s,o)=>(n.assign(e,{busy:!0,codecConstructor:t,options:n.assign({},i),scripts:o,terminate(){e.worker&&!e.busy&&(e.worker.terminate(),e.interface=null)},onTaskFinished(){e.busy=!1,a(e)}}),s?((e,t)=>{let n;const i={type:"module"};if(!e.interface){if(et)try{e.worker=r({},t.baseURL)}catch(n){et=!1,e.worker=r(i,t.baseURL)}else e.worker=r(i,t.baseURL);e.worker.addEventListener("message",(t=>{const i=t.data;if(n){const t=i.error,r=i.type;if(t){const i=new c(t.message);i.stack=t.stack,n.reject(i),n=null,e.onTaskFinished()}else if(r==Je||r==$e||r==Qe){const t=i.data;r==$e?(n.resolve({data:new d(t),signature:i.signature}),n=null,e.onTaskFinished()):n.resolve(t&&new d(t))}}}),!1),e.interface={append:e=>a({type:Qe,data:e}),flush:()=>a({type:$e}),abort(){e.onTaskFinished()}}}return e.interface;function r(t,n){let i,r;i=e.scripts[0],"function"==typeof i&&(i=i());try{r=new l(i,n)}catch(e){r=i}return new Worker(r,t)}async function a(i){if(!n){const n=e.options,i=e.scripts.slice(1);await s({scripts:i,type:Je,options:n,config:{chunkSize:t.chunkSize}})}return s(i)}function s(t){const i=e.worker,r=new w(((e,t)=>n={resolve:e,reject:t}));try{if(t.data)try{t.data=t.data.buffer,i.postMessage(t,[t.data])}catch(e){i.postMessage(t)}else i.postMessage(t)}catch(t){n.reject(t),n=null,e.onTaskFinished()}return r}})(e,r):((e,t)=>{const n=((e,t,n)=>t.codecType.startsWith(Ke)?new Xe(e,t,n):t.codecType.startsWith(Ge)?new je(e,t,n):void 0)(e.codecConstructor,e.options,t);return{async append(t){try{return await n.append(t)}catch(t){throw e.onTaskFinished(),t}},async flush(){try{return await n.flush()}finally{e.onTaskFinished()}},abort(){e.onTaskFinished()}}})(e,r));let nt=[],it=[];function rt(e,t,n){const i=!(!t.compressed&&!t.signed&&!t.encrypted)&&(t.useWebWorkers||void 0===t.useWebWorkers&&n.useWebWorkers),r=i&&n.workerScripts?n.workerScripts[t.codecType]:[];if(nt.length<n.maxWorkers){const s={};return nt.push(s),tt(s,e,t,n,a,i,r)}{const s=nt.find((e=>!e.busy));return s?(at(s),tt(s,e,t,n,a,i,r)):new w((n=>it.push({resolve:n,codecConstructor:e,options:t,webWorker:i,scripts:r})))}function a(e){if(it.length){const[{resolve:t,codecConstructor:i,options:r,webWorker:s,scripts:o}]=it.splice(0,1);t(tt(e,i,r,n,a,s,o))}else e.worker?(at(e),Number.isFinite(n.terminateWorkerTimeout)&&n.terminateWorkerTimeout>=0&&(e.terminateTimeout=setTimeout((()=>{nt=nt.filter((t=>t!=e)),e.terminate()}),n.terminateWorkerTimeout))):nt=nt.filter((t=>t!=e))}}function at(e){e.terminateTimeout&&(clearTimeout(e.terminateTimeout),e.terminateTimeout=null)}const st="Abort error";async function ot(e,t,n,i,r,s,o){const l=a.max(s.chunkSize,64);return async function s(c=0,d=0){const f=o.signal;if(r>c){lt(f,e);const u=await t.readUint8Array(c+i,a.min(l,r-c)),h=u.length;lt(f,e);const _=await e.append(u);if(lt(f,e),d+=await ct(n,_),o.onprogress)try{o.onprogress(c+h,r)}catch(e){}return s(c+l,d)}{const t=await e.flush();return d+=await ct(n,t.data),{signature:t.signature,length:d}}}()}function lt(e,t){if(e&&e.aborted)throw t.abort(),new c(st)}async function ct(e,t){return t.length&&await e.writeUint8Array(t),t.length}const dt="HTTP error ",ft="HTTP Range not supported",ut="text/plain",ht="GET";class _t{constructor(){this.size=0}init(){this.initialized=!0}}class wt extends _t{}class pt extends _t{writeUint8Array(e){this.size+=e.length}}class gt extends wt{constructor(e){super(),this.blob=e,this.size=e.size}async readUint8Array(e,t){if(this.blob.arrayBuffer)return new d(await this.blob.slice(e,e+t).arrayBuffer());{const n=new b;return new w(((i,r)=>{n.onload=e=>i(new d(e.target.result)),n.onerror=()=>r(n.error),n.readAsArrayBuffer(this.blob.slice(e,e+t))}))}}}class bt extends pt{constructor(e){super(),this.contentType=e,this.arrayBuffers=[]}async writeUint8Array(e){super.writeUint8Array(e),this.arrayBuffers.push(e.buffer)}getData(){return this.blob||(this.blob=new _(this.arrayBuffers,{type:this.contentType})),this.blob}}class xt extends wt{constructor(e,t){super(),this.url=e,this.preventHeadRequest=t.preventHeadRequest,this.useRangeHeader=t.useRangeHeader,this.forceRangeRequests=t.forceRangeRequests,this.options=n.assign({},t),delete this.options.preventHeadRequest,delete this.options.useRangeHeader,delete this.options.forceRangeRequests,delete this.options.useXHR}async init(){super.init(),await mt(this,St,Et)}async readUint8Array(e,t){return kt(this,e,t,St,Et)}}class yt extends wt{constructor(e,t){super(),this.url=e,this.preventHeadRequest=t.preventHeadRequest,this.useRangeHeader=t.useRangeHeader,this.forceRangeRequests=t.forceRangeRequests,this.options=t}async init(){super.init(),await mt(this,zt,Dt)}async readUint8Array(e,t){return kt(this,e,t,zt,Dt)}}async function mt(e,t,n){if((e=>{if(void 0!==x){const t=x.createElement("a");return t.href=e,"http:"==t.protocol||"https:"==t.protocol}return/^https?:\/\//i.test(e)})(e.url)&&(e.useRangeHeader||e.forceRangeRequests)){const i=await t(ht,e,vt(e));if(!e.forceRangeRequests&&"bytes"!=i.headers.get("Accept-Ranges"))throw new c(ft);{let r;const a=i.headers.get("Content-Range");if(a){const e=a.trim().split(/\s*\/\s*/);if(e.length){const t=e[1];t&&"*"!=t&&(r=Number(t))}}void 0===r?await Ft(e,t,n):e.size=r}}else await Ft(e,t,n)}async function kt(e,t,n,i,r){if(e.useRangeHeader||e.forceRangeRequests){const r=await i(ht,e,vt(e,t,n));if(206!=r.status)throw new c(ft);return new d(await r.arrayBuffer())}return e.data||await r(e,e.options),new d(e.data.subarray(t,t+n))}function vt(e,t=0,i=1){return n.assign({},Rt(e),{Range:"bytes="+t+"-"+(t+i-1)})}function Rt(e){let t=e.options.headers;if(t)return Symbol.iterator in t?n.fromEntries(t):t}async function Et(e){await At(e,St)}async function Dt(e){await At(e,zt)}async function At(e,t){const n=await t(ht,e,Rt(e));e.data=new d(await n.arrayBuffer()),e.size||(e.size=e.data.length)}async function Ft(e,t,n){if(e.preventHeadRequest)await n(e,e.options);else{const i=(await t("HEAD",e,Rt(e))).headers.get("Content-Length");i?e.size=Number(i):await n(e,e.options)}}async function St(e,{options:t,url:i},r){const a=await fetch(i,n.assign({},t,{method:e,headers:r}));if(400>a.status)return a;throw new c(dt+(a.statusText||a.status))}function zt(e,{url:t},i){return new w(((r,a)=>{const s=new XMLHttpRequest;if(s.addEventListener("load",(()=>{if(400>s.status){const e=[];s.getAllResponseHeaders().trim().split(/[\r\n]+/).forEach((t=>{const n=t.trim().split(/\s*:\s*/);n[0]=n[0].trim().replace(/^[a-z]|-[a-z]/g,(e=>e.toUpperCase())),e.push(n)})),r({status:s.status,arrayBuffer:()=>s.response,headers:new o(e)})}else a(new c(dt+(s.statusText||s.status)))}),!1),s.addEventListener("error",(e=>a(e.detail.error)),!1),s.open(e,t),i)for(const e of n.entries(i))s.setRequestHeader(e[0],e[1]);s.responseType="arraybuffer",s.send()}))}class Tt extends wt{constructor(e,t={}){super(),this.url=e,t.useXHR?this.reader=new yt(e,t):this.reader=new xt(e,t)}set size(e){}get size(){return this.reader.size}async init(){super.init(),await this.reader.init()}async readUint8Array(e,t){return this.reader.readUint8Array(e,t)}}const Ut=4294967295,Ct=65535,Wt=67324752,It=33639248,Bt=101075792,Lt=117853008,Mt=21589,Nt=2048,Ht="/",Ot=new s(2107,11,31),Pt=new s(1980,0,1),Vt="\0☺☻♥♦♣♠•◘○◙♂♀♪♫☼►◄↕‼¶§▬↨↑↓→←∟↔▲▼ !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~⌂ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ".split("");async function qt(e,t){if(t&&"cp437"==t.trim().toLowerCase())return(e=>{let t="";for(let n=0;n<e.length;n++)t+=Vt[e[n]];return t})(e);if(void 0===g){const t=new b;return new w(((n,i)=>{t.onload=e=>n(e.target.result),t.onerror=()=>i(t.error),t.readAsText(new _([e]))}))}return new g(t).decode(e)}const Zt=["filename","rawFilename","directory","encrypted","compressedSize","uncompressedSize","lastModDate","rawLastModDate","comment","rawComment","signature","extraField","rawExtraField","bitFlag","extraFieldZip64","extraFieldUnicodePath","extraFieldUnicodeComment","extraFieldAES","filenameUTF8","commentUTF8","offset","zip64","compressionMethod","extraFieldNTFS","lastAccessDate","creationDate","extraFieldExtendedTimestamp","version","versionMadeBy","msDosCompatible","internalFileAttribute","externalFileAttribute"];class Kt{constructor(e){Zt.forEach((t=>this[t]=e[t]))}}const Gt="File format is not recognized",Yt="End of central directory not found",jt="End of Zip64 central directory not found",Xt="End of Zip64 central directory locator not found",Jt="Central directory header not found",Qt="Local file header not found",$t="Zip64 extra field not found",en="File contains encrypted entry",tn="Encryption method not supported",nn="Compression method not supported",rn="utf-8",an="cp437",sn=["uncompressedSize","compressedSize","offset"];class on{constructor(e,t,i){n.assign(this,{reader:e,config:t,options:i})}async getData(e,t,n={}){const i=this,{reader:r,offset:a,extraFieldAES:s,compressionMethod:o,config:l,bitFlag:d,signature:f,rawLastModDate:u,compressedSize:h}=i,_=i.localDirectory={};r.initialized||await r.init();let w=await xn(r,a,30);const p=bn(w);let g=fn(i,n,"password");if(g=g&&g.length&&g,s&&99!=s.originalCompressionMethod)throw new c(nn);if(0!=o&&8!=o)throw new c(nn);if(pn(p,0)!=Wt)throw new c(Qt);ln(_,p,4),w=await xn(r,a,30+_.filenameLength+_.extraFieldLength),_.rawExtraField=w.subarray(30+_.filenameLength),await cn(i,_,p,4),t.lastAccessDate=_.lastAccessDate,t.creationDate=_.creationDate;const b=i.encrypted&&_.encrypted,x=b&&!s;if(b){if(!x&&void 0===s.strength)throw new c(tn);if(!g)throw new c(en)}const y=await rt(l.Inflate,{codecType:Ge,password:g,zipCrypto:x,encryptionStrength:s&&s.strength,signed:fn(i,n,"checkSignature"),passwordVerification:x&&(d.dataDescriptor?u>>>8&255:f>>>24&255),signature:f,compressed:0!=o,encrypted:b,useWebWorkers:fn(i,n,"useWebWorkers")},l);e.initialized||await e.init();const m=fn(i,n,"signal"),k=a+30+_.filenameLength+_.extraFieldLength;return await ot(y,r,e,k,h,l,{onprogress:n.onprogress,signal:m}),e.getData()}}function ln(e,t,i){const r=e.rawBitFlag=wn(t,i+2),a=1==(1&r),s=pn(t,i+6);n.assign(e,{encrypted:a,version:wn(t,i),bitFlag:{level:(6&r)>>1,dataDescriptor:8==(8&r),languageEncodingFlag:(r&Nt)==Nt},rawLastModDate:s,lastModDate:un(s),filenameLength:wn(t,i+22),extraFieldLength:wn(t,i+24)})}async function cn(e,t,i,r){const l=t.rawExtraField,f=t.extraField=new o,u=bn(new d(l));let h=0;try{for(;h<l.length;){const e=wn(u,h),t=wn(u,h+2);f.set(e,{type:e,data:l.slice(h+4,h+4+t)}),h+=4+t}}catch(e){}const _=wn(i,r+4);t.signature=pn(i,r+10),t.uncompressedSize=pn(i,r+18),t.compressedSize=pn(i,r+14);const w=f.get(1);w&&(((e,t)=>{t.zip64=!0;const n=bn(e.data);e.values=[];for(let t=0;t<a.floor(e.data.length/8);t++)e.values.push(gn(n,0+8*t));const i=sn.filter((e=>t[e]==Ut));for(let t=0;t<i.length;t++)e[i[t]]=e.values[t];sn.forEach((n=>{if(t[n]==Ut){if(void 0===e[n])throw new c($t);t[n]=e[n]}}))})(w,t),t.extraFieldZip64=w);const p=f.get(28789);p&&(await dn(p,"filename","rawFilename",t,e),t.extraFieldUnicodePath=p);const g=f.get(25461);g&&(await dn(g,"comment","rawComment",t,e),t.extraFieldUnicodeComment=g);const b=f.get(39169);b?(((e,t,n)=>{const i=bn(e.data);e.vendorVersion=_n(i,0),e.vendorId=_n(i,2);const r=_n(i,4);e.strength=r,e.originalCompressionMethod=n,t.compressionMethod=e.compressionMethod=wn(i,5)})(b,t,_),t.extraFieldAES=b):t.compressionMethod=_;const x=f.get(10);x&&(((e,t)=>{const i=bn(e.data);let r,a=4;try{for(;a<e.data.length&&!r;){const t=wn(i,a),n=wn(i,a+2);1==t&&(r=e.data.slice(a+4,a+4+n)),a+=4+n}}catch(e){}try{if(r&&24==r.length){const i=bn(r),a=i.getBigUint64(0,!0),s=i.getBigUint64(8,!0),o=i.getBigUint64(16,!0);n.assign(e,{rawLastModDate:a,rawLastAccessDate:s,rawCreationDate:o});const l={lastModDate:hn(a),lastAccessDate:hn(s),creationDate:hn(o)};n.assign(e,l),n.assign(t,l)}}catch(e){}})(x,t),t.extraFieldNTFS=x);const y=f.get(Mt);y&&(((e,t)=>{const n=bn(e.data),i=_n(n,0),r=[],a=[];1==(1&i)&&(r.push("lastModDate"),a.push("rawLastModDate")),2==(2&i)&&(r.push("lastAccessDate"),a.push("rawLastAccessDate")),4==(4&i)&&(r.push("creationDate"),a.push("rawCreationDate"));let o=1;r.forEach(((i,r)=>{if(e.data.length>=o+4){const l=pn(n,o);t[i]=e[i]=new s(1e3*l);const c=a[r];e[c]=l}o+=4}))})(y,t),t.extraFieldExtendedTimestamp=y)}async function dn(e,t,n,i,r){const a=bn(e.data);e.version=_n(a,0),e.signature=pn(a,1);const s=new le;s.append(r[n]);const o=bn(new d(4));o.setUint32(0,s.get(),!0),e[t]=await qt(e.data.subarray(5)),e.valid=!r.bitFlag.languageEncodingFlag&&e.signature==pn(o,0),e.valid&&(i[t]=e[t],i[t+"UTF8"]=!0)}function fn(e,t,n){return void 0===t[n]?e.options[n]:t[n]}function un(e){const t=(4294901760&e)>>16,n=65535&e;try{return new s(1980+((65024&t)>>9),((480&t)>>5)-1,31&t,(63488&n)>>11,(2016&n)>>5,2*(31&n),0)}catch(e){}}function hn(e){return new s(Number(e/r(1e4)-r(116444736e5)))}function _n(e,t){return e.getUint8(t)}function wn(e,t){return e.getUint16(t,!0)}function pn(e,t){return e.getUint32(t,!0)}function gn(e,t){return Number(e.getBigUint64(t,!0))}function bn(e){return new h(e.buffer)}function xn(e,t,n){return e.readUint8Array(t,n)}const yn="File already exists",mn="Zip file comment exceeds 64KB",kn="File entry comment exceeds 64KB",vn="File entry name exceeds 64KB",Rn="Version exceeds 65535",En="The strength must equal 1, 2, or 3",Dn="Extra field type exceeds 65535",An="Extra field data exceeds 64KB",Fn="Zip64 is not supported",Sn=new d([7,0,2,0,65,69,3,0,0]);let zn=0;function Tn(e,t,n){if(e.arrayBuffer)return t||n?e.slice(t,n).arrayBuffer():e.arrayBuffer();{const i=new b;return new w(((r,a)=>{i.onload=e=>r(e.target.result),i.onerror=()=>a(i.error),i.readAsArrayBuffer(t||n?e.slice(t,n):e)}))}}function Un(e){if(e)return(r(e.getTime())+r(116444736e5))*r(1e4)}function Cn(e,t,n){return void 0===t[n]?e.options[n]:t[n]}function Wn(e,t,n){e.setUint8(t,n)}function In(e,t,n){e.setUint16(t,n,!0)}function Bn(e,t,n){e.setUint32(t,n,!0)}function Ln(e,t,n){e.setBigUint64(t,n,!0)}function Mn(e,t,n){e.set(t,n)}function Nn(e){return new h(e.buffer)}se({Deflate:function(e){const t=new M,n=(i=e&&e.chunkSize?e.chunkSize:65536)+5*(a.floor(i/16383)+1);var i;const r=new d(n);let s=e?e.level:-1;void 0===s&&(s=-1),t.deflateInit(s),t.next_out=r,this.append=(e,i)=>{let a,s,o=0,l=0,f=0;const u=[];if(e.length){t.next_in_index=0,t.next_in=e,t.avail_in=e.length;do{if(t.next_out_index=0,t.avail_out=n,a=t.deflate(0),0!=a)throw new c("deflating: "+t.msg);t.next_out_index&&(t.next_out_index==n?u.push(new d(r)):u.push(r.slice(0,t.next_out_index))),f+=t.next_out_index,i&&t.next_in_index>0&&t.next_in_index!=o&&(i(t.next_in_index),o=t.next_in_index)}while(t.avail_in>0||0===t.avail_out);return u.length>1?(s=new d(f),u.forEach((e=>{s.set(e,l),l+=e.length}))):s=u[0]||new d(0),s}},this.flush=()=>{let e,i,a=0,s=0;const o=[];do{if(t.next_out_index=0,t.avail_out=n,e=t.deflate(4),1!=e&&0!=e)throw new c("deflating: "+t.msg);n-t.avail_out>0&&o.push(r.slice(0,t.next_out_index)),s+=t.next_out_index}while(t.avail_in>0||0===t.avail_out);return t.deflateEnd(),i=new d(s),o.forEach((e=>{i.set(e,a),a+=e.length})),i}},Inflate:function(e){const t=new ne,n=e&&e.chunkSize?a.floor(2*e.chunkSize):131072,i=new d(n);let r=!1;t.inflateInit(),t.next_out=i,this.append=(e,a)=>{const s=[];let o,l,f=0,u=0,h=0;if(0!==e.length){t.next_in_index=0,t.next_in=e,t.avail_in=e.length;do{if(t.next_out_index=0,t.avail_out=n,0!==t.avail_in||r||(t.next_in_index=0,r=!0),o=t.inflate(0),r&&o===O){if(0!==t.avail_in)throw new c("inflating: bad input")}else if(0!==o&&1!==o)throw new c("inflating: "+t.msg);if((r||1===o)&&t.avail_in===e.length)throw new c("inflating: bad input");t.next_out_index&&(t.next_out_index===n?s.push(new d(i)):s.push(i.slice(0,t.next_out_index))),h+=t.next_out_index,a&&t.next_in_index>0&&t.next_in_index!=f&&(a(t.next_in_index),f=t.next_in_index)}while(t.avail_in>0||0===t.avail_out);return s.length>1?(l=new d(h),s.forEach((e=>{l.set(e,u),u+=e.length}))):l=s[0]||new d(0),l}},this.flush=()=>{t.inflateEnd()}}}),e.BlobReader=gt,e.BlobWriter=bt,e.Data64URIReader=class extends wt{constructor(e){super(),this.dataURI=e;let t=e.length;for(;"="==e.charAt(t-1);)t--;this.dataStart=e.indexOf(",")+1,this.size=a.floor(.75*(t-this.dataStart))}async readUint8Array(e,t){const n=new d(t),i=4*a.floor(e/3),r=atob(this.dataURI.substring(i+this.dataStart,4*a.ceil((e+t)/3)+this.dataStart)),s=e-3*a.floor(i/4);for(let e=s;s+t>e;e++)n[e-s]=r.charCodeAt(e);return n}},e.Data64URIWriter=class extends pt{constructor(e){super(),this.data="data:"+(e||"")+";base64,",this.pending=[]}async writeUint8Array(e){super.writeUint8Array(e);let t=0,n=this.pending;const r=this.pending.length;for(this.pending="",t=0;t<3*a.floor((r+e.length)/3)-r;t++)n+=i.fromCharCode(e[t]);for(;t<e.length;t++)this.pending+=i.fromCharCode(e[t]);n.length>2?this.data+=m(n):this.pending=n}getData(){return this.data+m(this.pending)}},e.ERR_ABORT=st,e.ERR_BAD_FORMAT=Gt,e.ERR_CENTRAL_DIRECTORY_NOT_FOUND=Jt,e.ERR_DUPLICATED_NAME=yn,e.ERR_ENCRYPTED=en,e.ERR_EOCDR_LOCATOR_ZIP64_NOT_FOUND=Xt,e.ERR_EOCDR_NOT_FOUND=Yt,e.ERR_EOCDR_ZIP64_NOT_FOUND=jt,e.ERR_EXTRAFIELD_ZIP64_NOT_FOUND=$t,e.ERR_HTTP_RANGE=ft,e.ERR_INVALID_COMMENT=mn,e.ERR_INVALID_ENCRYPTION_STRENGTH=En,e.ERR_INVALID_ENTRY_COMMENT=kn,e.ERR_INVALID_ENTRY_NAME=vn,e.ERR_INVALID_EXTRAFIELD_DATA=An,e.ERR_INVALID_EXTRAFIELD_TYPE=Dn,e.ERR_INVALID_PASSWORD=we,e.ERR_INVALID_SIGNATURE=Ye,e.ERR_INVALID_VERSION=Rn,e.ERR_LOCAL_FILE_HEADER_NOT_FOUND=Qt,e.ERR_UNSUPPORTED_COMPRESSION=nn,e.ERR_UNSUPPORTED_ENCRYPTION=tn,e.ERR_UNSUPPORTED_FORMAT=Fn,e.HttpRangeReader=class extends Tt{constructor(e,t={}){t.useRangeHeader=!0,super(e,t)}},e.HttpReader=Tt,e.Reader=wt,e.TextReader=class extends wt{constructor(e){super(),this.blobReader=new gt(new _([e],{type:ut}))}async init(){super.init(),this.blobReader.init(),this.size=this.blobReader.size}async readUint8Array(e,t){return this.blobReader.readUint8Array(e,t)}},e.TextWriter=class extends pt{constructor(e){super(),this.encoding=e,this.blob=new _([],{type:ut})}async writeUint8Array(e){super.writeUint8Array(e),this.blob=new _([this.blob,e.buffer],{type:ut})}getData(){if(this.blob.text)return this.blob.text();{const e=new b;return new w(((t,n)=>{e.onload=e=>t(e.target.result),e.onerror=()=>n(e.error),e.readAsText(this.blob,this.encoding)}))}}},e.Uint8ArrayReader=class extends wt{constructor(e){super(),this.array=e,this.size=e.length}async readUint8Array(e,t){return this.array.slice(e,e+t)}},e.Uint8ArrayWriter=class extends pt{constructor(){super(),this.array=new d(0)}async writeUint8Array(e){super.writeUint8Array(e);const t=this.array;this.array=new d(t.length+e.length),this.array.set(t),this.array.set(e,t.length)}getData(){return this.array}},e.WritableStreamWriter=class extends pt{constructor(e){super(),this.writableStream=e,this.writer=e.getWriter()}async writeUint8Array(e){return await this.writer.ready,this.writer.write(e)}async getData(){return await this.writer.ready,await this.writer.close(),this.writableStream}},e.Writer=pt,e.ZipReader=class{constructor(e,t={}){n.assign(this,{reader:e,options:t,config:ae()})}async getEntries(e={}){const t=this,i=t.reader;if(i.initialized||await i.init(),22>i.size)throw new c(Gt);const r=await(async(e,t,n)=>{const i=new d(4);return bn(i).setUint32(0,101010256,!0),await r(22)||await r(a.min(1048582,n));async function r(t){const r=n-t,a=await xn(e,r,t);for(let e=a.length-22;e>=0;e--)if(a[e]==i[0]&&a[e+1]==i[1]&&a[e+2]==i[2]&&a[e+3]==i[3])return{offset:r+e,buffer:a.slice(e,e+22).buffer}}})(i,0,i.size);if(!r)throw new c(Yt);const s=bn(r);let o=pn(s,12),l=pn(s,16),f=wn(s,8),u=0;if(l==Ut||o==Ut||f==Ct){const e=bn(await xn(i,r.offset-20,20));if(pn(e,0)!=Lt)throw new c(jt);l=gn(e,8);let t=await xn(i,l,56),n=bn(t);const a=r.offset-20-56;if(pn(n,0)!=Bt&&l!=a){const e=l;l=a,u=l-e,t=await xn(i,l,56),n=bn(t)}if(pn(n,0)!=Bt)throw new c(Xt);f=gn(n,32),o=gn(n,40),l-=o}if(0>l||l>=i.size)throw new c(Gt);let h=0,_=await xn(i,l,o),p=bn(_);if(o){const e=r.offset-o;if(pn(p,h)!=It&&l!=e){const t=l;l=e,u=l-t,_=await xn(i,l,o),p=bn(_)}}if(0>l||l>=i.size)throw new c(Gt);const g=[];for(let r=0;f>r;r++){const a=new on(i,t.config,t.options);if(pn(p,h)!=It)throw new c(Jt);ln(a,p,h+6);const s=!!a.bitFlag.languageEncodingFlag,o=h+46,l=o+a.filenameLength,d=l+a.extraFieldLength,b=wn(p,h+4),x=0==(0&b);n.assign(a,{versionMadeBy:b,msDosCompatible:x,compressedSize:0,uncompressedSize:0,commentLength:wn(p,h+32),directory:x&&16==(16&_n(p,h+38)),offset:pn(p,h+42)+u,internalFileAttribute:pn(p,h+34),externalFileAttribute:pn(p,h+38),rawFilename:_.subarray(o,l),filenameUTF8:s,commentUTF8:s,rawExtraField:_.subarray(l,d)});const y=d+a.commentLength;a.rawComment=_.subarray(d,y);const m=fn(t,e,"filenameEncoding"),k=fn(t,e,"commentEncoding"),[v,R]=await w.all([qt(a.rawFilename,a.filenameUTF8?rn:m||an),qt(a.rawComment,a.commentUTF8?rn:k||an)]);a.filename=v,a.comment=R,!a.directory&&a.filename.endsWith(Ht)&&(a.directory=!0),await cn(a,a,p,h+6);const E=new Kt(a);if(E.getData=(e,t)=>a.getData(e,E,t),g.push(E),h=y,e.onprogress)try{e.onprogress(r+1,f,new Kt(a))}catch(e){}}return g}async close(){}},e.ZipWriter=class{constructor(e,t={}){n.assign(this,{writer:e,options:t,config:ae(),files:new o,offset:e.size,pendingCompressedSize:0,pendingEntries:[]})}async add(e="",i,o={}){const l=this;if(zn>=l.config.maxWorkers)return new w(((t,n)=>l.pendingEntries.push({name:e,reader:i,options:o,resolve:t,reject:n})));zn++;try{return await(async(e,i,o,l)=>{if(i=i.trim(),l.directory&&!i.endsWith(Ht)?i+=Ht:l.directory=i.endsWith(Ht),e.files.has(i))throw new c(yn);const _=ce(i);if(_.length>Ct)throw new c(vn);const p=l.comment||"",g=ce(p);if(g.length>Ct)throw new c(kn);const b=e.options.version||l.version||0;if(b>Ct)throw new c(Rn);const x=e.options.versionMadeBy||l.versionMadeBy||20;if(x>Ct)throw new c(Rn);const y=Cn(e,l,"lastModDate")||new s,m=Cn(e,l,"lastAccessDate"),k=Cn(e,l,"creationDate"),v=Cn(e,l,"password"),R=Cn(e,l,"encryptionStrength")||3,E=Cn(e,l,"zipCrypto");if(void 0!==v&&void 0!==R&&(1>R||R>3))throw new c(En);let D=new d(0);const A=l.extraField;if(A){let e=0,t=0;A.forEach((t=>e+=4+t.length)),D=new d(e),A.forEach(((e,n)=>{if(n>Ct)throw new c(Dn);if(e.length>Ct)throw new c(An);Mn(D,new f([n]),t),Mn(D,new f([e.length]),t+2),Mn(D,e,t+4),t+=4+e.length}))}let F=Cn(e,l,"extendedTimestamp");void 0===F&&(F=!0);let S=0,z=Cn(e,l,"keepOrder");void 0===z&&(z=!0);let T=0,U=Cn(e,l,"msDosCompatible");void 0===U&&(U=!0);const C=Cn(e,l,"internalFileAttribute")||0,W=Cn(e,l,"externalFileAttribute")||0;o&&(o.initialized||await o.init(),T=o.size,S=(e=>e+5*(a.floor(e/16383)+1))(T));let I=l.zip64||e.options.zip64||!1;if(e.offset+e.pendingCompressedSize>=Ut||T>=Ut||S>=Ut){if(!1===l.zip64||!1===e.options.zip64||!z)throw new c(Fn);I=!0}e.pendingCompressedSize+=S,await w.resolve();const B=Cn(e,l,"level"),L=Cn(e,l,"useWebWorkers"),M=Cn(e,l,"bufferedWrite");let N=Cn(e,l,"dataDescriptor"),H=Cn(e,l,"dataDescriptorSignature");const O=Cn(e,l,"signal");void 0===N&&(N=!0),N&&void 0===H&&(H=!0);const P=await(async(e,i,s,o)=>{const l=e.files,f=e.writer,_=t.from(l.values()).pop();let p,g,b,x={};l.set(i,x);try{let y,m,k;if(o.keepOrder&&(y=_&&_.lock),x.lock=k=new w((e=>b=e)),o.bufferedWrite||e.lockWrite||!o.dataDescriptor?(m=new bt,m.init(),p=!0):(e.lockWrite=new w((e=>g=e)),f.initialized||await f.init(),m=f),x=await(async(e,t,i,s)=>{const{rawFilename:o,lastAccessDate:l,creationDate:c,password:f,level:h,zip64:_,zipCrypto:w,dataDescriptor:p,dataDescriptorSignature:g,directory:b,version:x,versionMadeBy:y,rawComment:m,rawExtraField:k,useWebWorkers:v,onprogress:R,signal:E,encryptionStrength:D,extendedTimestamp:A,msDosCompatible:F,internalFileAttribute:S,externalFileAttribute:z}=s,T=!(!f||!f.length),U=0!==h&&!b;let C,W,I;if(T&&!w){C=new d(Sn.length+2);const e=Nn(C);In(e,0,39169),Mn(C,Sn,2),Wn(e,8,D)}else C=new d(0);if(A){I=new d(9+(l?4:0)+(c?4:0));const e=Nn(I);In(e,0,Mt),In(e,2,I.length-4),Wn(e,4,1+(l?2:0)+(c?4:0)),Bn(e,5,a.floor(s.lastModDate.getTime()/1e3)),l&&Bn(e,9,a.floor(l.getTime()/1e3)),c&&Bn(e,13,a.floor(c.getTime()/1e3));try{W=new d(36);const e=Nn(W),t=Un(s.lastModDate);In(e,0,10),In(e,2,32),In(e,8,1),In(e,10,24),Ln(e,12,t),Ln(e,20,Un(l)||t),Ln(e,28,Un(c)||t)}catch(e){W=new d(0)}}else W=I=new d(0);const B={version:x||20,versionMadeBy:y,zip64:_,directory:!!b,filenameUTF8:!0,rawFilename:o,commentUTF8:!0,rawComment:m,rawExtraFieldZip64:new d(_?28:0),rawExtraFieldExtendedTimestamp:I,rawExtraFieldNTFS:W,rawExtraFieldAES:C,rawExtraField:k,extendedTimestamp:A,msDosCompatible:F,internalFileAttribute:S,externalFileAttribute:z};let L=B.uncompressedSize=0,M=Nt;p&&(M|=8);let N=0;U&&(N=8),_&&(B.version=B.version>45?B.version:45),T&&(M|=1,w||(B.version=B.version>51?B.version:51,N=99,U&&(B.rawExtraFieldAES[9]=8))),B.compressionMethod=N;const H=B.headerArray=new d(26),O=Nn(H);In(O,0,B.version),In(O,2,M),In(O,4,N);const P=new u(1),V=Nn(P);let q;q=s.lastModDate<Pt?Pt:s.lastModDate>Ot?Ot:s.lastModDate,In(V,0,(q.getHours()<<6|q.getMinutes())<<5|q.getSeconds()/2),In(V,2,(q.getFullYear()-1980<<4|q.getMonth()+1)<<5|q.getDate());const Z=P[0];Bn(O,6,Z),In(O,22,o.length);const K=C.length+I.length+W.length+B.rawExtraField.length;In(O,24,K);const G=new d(30+o.length+K);let Y;Bn(Nn(G),0,Wt),Mn(G,H,4),Mn(G,o,30),Mn(G,C,30+o.length),Mn(G,I,30+o.length+C.length),Mn(G,W,30+o.length+C.length+I.length),Mn(G,B.rawExtraField,30+o.length+C.length+I.length+W.length);let j=0;if(e){L=B.uncompressedSize=e.size;const n=await rt(i.Deflate,{codecType:Ke,level:h,password:f,encryptionStrength:D,zipCrypto:T&&w,passwordVerification:T&&w&&Z>>8&255,signed:!0,compressed:U,encrypted:T,useWebWorkers:v},i);await t.writeUint8Array(G),B.dataWritten=!0,Y=await ot(n,e,t,0,L,i,{onprogress:R,signal:E}),j=Y.length}else await t.writeUint8Array(G),B.dataWritten=!0;let X,J=new d(0),Q=0;if(p&&(J=new d(_?g?24:20:g?16:12),X=Nn(J),g&&(Q=4,Bn(X,0,134695760))),e){const e=Y.signature;if(T&&!w||void 0===e||(Bn(O,10,e),B.signature=e,p&&Bn(X,Q,e)),_){const e=Nn(B.rawExtraFieldZip64);In(e,0,1),In(e,2,24),Bn(O,14,Ut),Ln(e,12,r(j)),Bn(O,18,Ut),Ln(e,4,r(L)),p&&(Ln(X,Q+4,r(j)),Ln(X,Q+12,r(L)))}else Bn(O,14,j),Bn(O,18,L),p&&(Bn(X,Q+4,j),Bn(X,Q+8,L))}p&&await t.writeUint8Array(J);const $=G.length+j+J.length;return n.assign(B,{compressedSize:j,lastModDate:q,rawLastModDate:Z,creationDate:c,lastAccessDate:l,encrypted:T,length:$}),B})(s,m,e.config,o),x.lock=k,l.set(i,x),x.filename=i,p){let n=0;const i=m.getData();let r;await w.all([e.lockWrite,y]);do{r=t.from(l.values()).find((e=>e.writingBufferedData)),r&&await r.lock}while(r&&r.lock);if(x.writingBufferedData=!0,!o.dataDescriptor){const e=26,t=await Tn(i,0,e),r=new h(t);x.encrypted&&!o.zipCrypto||Bn(r,14,x.signature),x.zip64?(Bn(r,18,Ut),Bn(r,22,Ut)):(Bn(r,18,x.compressedSize),Bn(r,22,x.uncompressedSize)),await f.writeUint8Array(new d(t)),n=e}await(async(e,t,n=0)=>{const i=536870912;await async function r(){if(n<t.size){const a=await Tn(t,n,n+i);await e.writeUint8Array(new d(a)),n+=i,await r()}}()})(f,i,n),delete x.writingBufferedData}if(x.offset=e.offset,x.zip64)Ln(Nn(x.rawExtraFieldZip64),20,r(x.offset));else if(x.offset>=Ut)throw new c(Fn);return e.offset+=x.length,x}catch(t){throw(p&&x.writingBufferedData||!p&&x.dataWritten)&&(t.corruptedEntry=e.hasCorruptedEntries=!0,x.uncompressedSize&&(e.offset+=x.uncompressedSize)),l.delete(i),t}finally{b(),g&&g()}})(e,i,o,n.assign({},l,{rawFilename:_,rawComment:g,version:b,versionMadeBy:x,lastModDate:y,lastAccessDate:m,creationDate:k,rawExtraField:D,zip64:I,password:v,level:B,useWebWorkers:L,encryptionStrength:R,extendedTimestamp:F,zipCrypto:E,bufferedWrite:M,keepOrder:z,dataDescriptor:N,dataDescriptorSignature:H,signal:O,msDosCompatible:U,internalFileAttribute:C,externalFileAttribute:W}));return S&&(e.pendingCompressedSize-=S),n.assign(P,{name:i,comment:p,extraField:A}),new Kt(P)})(l,e,i,o)}finally{zn--;const e=l.pendingEntries.shift();e&&l.add(e.name,e.reader,e.options).then(e.resolve).catch(e.reject)}}async close(e=new d(0),n={}){return await(async(e,n,i)=>{const s=e.writer,o=e.files;let l=0,f=0,u=e.offset,h=o.size;for(const[,e]of o)f+=46+e.rawFilename.length+e.rawComment.length+e.rawExtraFieldZip64.length+e.rawExtraFieldAES.length+e.rawExtraFieldExtendedTimestamp.length+e.rawExtraFieldNTFS.length+e.rawExtraField.length;let _=i.zip64||e.options.zip64||!1;if(u>=Ut||f>=Ut||h>=Ct){if(!1===i.zip64||!1===e.options.zip64)throw new c(Fn);_=!0}const w=new d(f+(_?98:22)),p=Nn(w);if(n&&n.length){if(n.length>Ct)throw new c(mn);In(p,l+20,n.length)}for(const[e,n]of t.from(o.values()).entries()){const{rawFilename:t,rawExtraFieldZip64:r,rawExtraFieldAES:s,rawExtraField:c,rawComment:f,versionMadeBy:u,headerArray:h,directory:_,zip64:g,msDosCompatible:b,internalFileAttribute:x,externalFileAttribute:y}=n;let m,k;if(n.extendedTimestamp){k=n.rawExtraFieldNTFS,m=new d(9);const e=Nn(m);In(e,0,Mt),In(e,2,m.length-4),Wn(e,4,1),Bn(e,5,a.floor(n.lastModDate.getTime()/1e3))}else k=m=new d(0);const v=r.length+s.length+m.length+k.length+c.length;if(Bn(p,l,It),In(p,l+4,u),Mn(w,h,l+6),In(p,l+30,v),In(p,l+32,f.length),Bn(p,l+34,x),y?Bn(p,l+38,y):_&&b&&Wn(p,l+38,16),Bn(p,l+42,g?Ut:n.offset),Mn(w,t,l+46),Mn(w,r,l+46+t.length),Mn(w,s,l+46+t.length+r.length),Mn(w,m,l+46+t.length+r.length+s.length),Mn(w,k,l+46+t.length+r.length+s.length+m.length),Mn(w,c,l+46+t.length+r.length+s.length+m.length+k.length),Mn(w,f,l+46+t.length+v),l+=46+t.length+v+f.length,i.onprogress)try{i.onprogress(e+1,o.size,new Kt(n))}catch(e){}}_&&(Bn(p,l,Bt),Ln(p,l+4,r(44)),In(p,l+12,45),In(p,l+14,45),Ln(p,l+24,r(h)),Ln(p,l+32,r(h)),Ln(p,l+40,r(f)),Ln(p,l+48,r(u)),Bn(p,l+56,Lt),Ln(p,l+64,r(u)+r(f)),Bn(p,l+72,1),h=Ct,u=Ut,f=Ut,l+=76),Bn(p,l,101010256),In(p,l+8,h),In(p,l+10,h),Bn(p,l+12,f),Bn(p,l+16,u),await s.writeUint8Array(w),n&&n.length&&await s.writeUint8Array(n)})(this,e,n),this.writer.getData()}},e.configure=se,e.getMimeType=()=>"application/octet-stream",e.terminateWorkers=()=>{nt.forEach((e=>{at(e),e.terminate()}))},n.defineProperty(e,"__esModule",{value:!0})}));
