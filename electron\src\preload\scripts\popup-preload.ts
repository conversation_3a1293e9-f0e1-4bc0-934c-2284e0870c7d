import { ipc<PERSON><PERSON><PERSON> } from 'electron';

const updateBounds = () => {
  ipcRenderer.sendToHost(
    'webview-size',
    document.body.offsetWidth || document.body.scrollWidth,
    document.body.offsetHeight || document.body.scrollHeight,
  );
};

window.addEventListener('load', () => {
  updateBounds();

  // @ts-ignore
  const resizeObserver = new ResizeObserver(() => {
    updateBounds();
  });

  // Ensure document.body exists and is a valid Element before observing
  if (document.body && document.body instanceof Element) {
    resizeObserver.observe(document.body);
  }
});

const close = () => {
  ipcRenderer.sendToHost('webview-blur');
};

window.addEventListener('blur', close);

window.close = close;
