import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';

// Dialog 常量 - 与原版保持一致
export const DIALOG_TRANSITION = '0.2s opacity';
export const DIALOG_BOX_SHADOW = '0 12px 16px rgba(0, 0, 0, 0.12), 0 8px 10px rgba(0, 0, 0, 0.16)';
export const DIALOG_BORDER_RADIUS = '10';

interface DialogBaseProps {
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

interface DialogProps extends DialogBaseProps {
  // 标准 Dialog 带有 fadeIn 动画
}

interface PersistentDialogProps extends DialogBaseProps {
  visible: boolean;
  hideTransition?: boolean;
}

/**
 * 基础 Dialog 样式组件
 */
export const DialogBase: React.FC<DialogBaseProps> = ({ 
  children, 
  className, 
  style 
}) => {
  return (
    <div
      className={cn(
        // 基础样式 - 与 DialogBaseStyle 一致
        'mx-4 mt-1',
        'overflow-hidden relative',
        'bg-mario-dialog',
        className
      )}
      style={{
        boxShadow: DIALOG_BOX_SHADOW,
        borderRadius: `${DIALOG_BORDER_RADIUS}px`,
        ...style
      }}
    >
      {children}
    </div>
  );
};

/**
 * 标准 Dialog 组件 - 带有 fadeIn 动画
 */
export const Dialog: React.FC<DialogProps> = ({ 
  children, 
  className, 
  style 
}) => {
  return (
    <DialogBase
      className={cn(
        // fadeIn 动画 - 与 DialogStyle 一致
        'animate-fadeIn',
        className
      )}
      style={style}
    >
      {children}
    </DialogBase>
  );
};

/**
 * 持久化 Dialog 组件 - 可控制显示/隐藏
 */
export const PersistentDialog: React.FC<PersistentDialogProps> = ({ 
  children, 
  className, 
  style,
  visible,
  hideTransition = false
}) => {
  return (
    <DialogBase
      className={cn(
        // 过渡效果
        visible || !hideTransition ? 'transition-opacity duration-200' : '',
        className
      )}
      style={{
        opacity: visible ? 1 : 0,
        ...style
      }}
    >
      {children}
    </DialogBase>
  );
};

// 默认导出标准 Dialog
export default Dialog;
