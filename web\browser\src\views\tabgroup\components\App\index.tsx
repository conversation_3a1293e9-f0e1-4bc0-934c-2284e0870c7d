import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';
import { Textfield } from '@browser/core/components/Textfield';
import { eventUtils } from '@browser/core/utils/platform-lite';
import {
  BLUE_500,
  RED_500,
  PINK_500,
  PURPLE_500,
  DEEP_PURPLE_500,
  INDIGO_500,
  CYAN_500,
  LIGHT_BLUE_500,
  TEAL_500,
  GREEN_500,
  LIGHT_GREEN_500,
  LIME_500,
  YELLOW_500,
  AMBER_500,
  ORANGE_500,
  DEEP_ORANGE_500,
} from '@mario-ai/shared';
import { UIStyle } from '@browser/core/styles/default-styles';

const onChange = (e: any) => {
  eventUtils.send(`edit-tabgroup-${store.windowId}`, {
    name: store.inputRef.current.value,
    id: store.tabGroupId,
  });
};

const onColorClick = (color: string) => () => {
  eventUtils.send(`edit-tabgroup-${store.windowId}`, {
    color,
    id: store.tabGroupId,
  });
};

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  const appClasses = cn(
    'mt-[10px] p-4 rounded-[10px] shadow-dialog bg-mario-dialog',
    'animate-[fadeIn_0.15s_ease-out]'
  );

  // Colors 样式 - Tailwind 版本
  const colorsClasses = cn(
    'flex mt-2 flex-wrap justify-center' // margin-top: 8px
  );

  // Color 组件 - Tailwind 版本
  const ColorItem = ({ color, onClick }: { color: string; onClick: () => void }) => {
    const colorClasses = cn(
      'min-w-4 h-4 cursor-pointer rounded-full mx-1 mt-2 relative overflow-hidden',
      // hover 效果通过伪元素实现
      'after:content-[""] after:absolute after:inset-0 after:opacity-0 after:bg-white',
      'hover:after:opacity-30'
    );

    return (
      <div
        className={colorClasses}
        style={{ backgroundColor: color }}
        onClick={onClick}
      />
    );
  };

  return (
    <div className={appClasses}>
      <UIStyle />
      <Textfield
        dark={store.theme['dialog.lightForeground']}
        placeholder="Name"
        style={{ width: '100%' }}
        onChange={onChange}
        ref={store.inputRef}
      />

      <div className={colorsClasses}>
        {[
          BLUE_500,
          RED_500,
          PINK_500,
          PURPLE_500,
          DEEP_PURPLE_500,
          INDIGO_500,
          CYAN_500,
          LIGHT_BLUE_500,
          TEAL_500,
          GREEN_500,
          LIGHT_GREEN_500,
          LIME_500,
          YELLOW_500,
          AMBER_500,
          ORANGE_500,
          DEEP_ORANGE_500,
        ].map((color, key) => (
          <ColorItem
            color={color}
            onClick={onColorClick(color)}
            key={key}
          />
        ))}
      </div>
    </div>
  );
});
