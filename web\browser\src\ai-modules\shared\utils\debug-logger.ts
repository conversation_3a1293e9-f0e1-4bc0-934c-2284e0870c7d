// 调试日志工具
export const debugLogger = {
  printUtf8Debug: () => {
    console.log('🔧 Debug info printed');
  }
};

export const logMemoryOperation = (operation: string, data: any) => {
  console.log(`🧠 Memory Operation: ${operation}`, data);
};

export const logLLMRequest = (request: string, data: any) => {
  console.log(`🤖 LLM Request: ${request}`, data);
};

export const logContextBuilding = (context: string, data: any) => {
  console.log(`📝 Context Building: ${context}`, data);
};

export const logStateChange = (state: string, data: any) => {
  console.log(`🔄 State Change: ${state}`, data);
};
