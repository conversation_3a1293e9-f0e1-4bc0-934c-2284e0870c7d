// Mem0 服务占位符
export const mem0Service = {
  initialize: async () => {
    console.log('🧠 Mem0 service initialized (placeholder)');
    return Promise.resolve();
  },

  setApiKey: async (apiKey: string) => {
    console.log('🔑 Mem0 API key set (placeholder)');
    return Promise.resolve();
  },

  isServiceAvailable: async () => {
    return {
      available: false,
      success: true,
      status: 'placeholder',
      error: null
    };
  }
};
