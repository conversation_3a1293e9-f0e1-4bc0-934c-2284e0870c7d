import { useState, useEffect } from 'react';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';

export const useTheme = () => {
  const [isDark, setIsDark] = useState(TailwindThemeManager.isDarkTheme());

  useEffect(() => {
    const handleThemeChange = (event: CustomEvent) => {
      setIsDark(event.detail.theme === 'wexond-dark');
    };

    window.addEventListener('theme-changed', handleThemeChange as EventListener);
    
    return () => {
      window.removeEventListener('theme-changed', handleThemeChange as EventListener);
    };
  }, []);

  const toggleTheme = () => {
    const newTheme = isDark ? 'wexond-light' : 'wexond-dark';
    TailwindThemeManager.setTheme(newTheme);
  };

  return {
    isDark,
    theme: isDark ? 'wexond-dark' : 'wexond-light',
    toggleTheme
  };
};
