import React, { useState } from 'react';
import { Button } from '../../../shared/components/Button';
import { cn } from '../../../shared/utils/cn';
import { AVAILABLE_MODELS } from '../services/aliyun';

interface ModelSelectorProps {
  onModelChange: (model: string) => void;
  selectedModel?: string;
  className?: string;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  onModelChange,
  selectedModel = 'qwen-plus',
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const modelInfo = {
    'qwen-plus': {
      name: 'Qwen Plus',
      description: '平衡性能与成本',
      icon: '🚀'
    },
    'qwen-turbo': {
      name: 'Qwen Turbo',
      description: '快速响应',
      icon: '⚡'
    },
    'qwen-max': {
      name: 'Qwen Max',
      description: '最强性能',
      icon: '🎯'
    }
  };

  const handleModelSelect = (model: string) => {
    onModelChange(model);
    setIsOpen(false);
  };

  const currentModel = modelInfo[selectedModel as keyof typeof modelInfo] || modelInfo['qwen-plus'];

  return (
    <div className={cn("relative", className)}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 bg-mario-dialog border-mario-border text-mario-page-text hover:bg-mario-hover"
      >
        <span>{currentModel.icon}</span>
        <span className="font-medium">{currentModel.name}</span>
        <span className={cn(
          "transition-transform text-xs",
          isOpen ? "rotate-180" : ""
        )}>
          ▼
        </span>
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-64 bg-mario-dialog border border-mario-border rounded-md shadow-lg z-20">
            <div className="p-2">
              <div className="text-xs font-medium text-mario-page-text opacity-70 px-3 py-2">
                选择模型
              </div>
              {AVAILABLE_MODELS.map((model) => {
                const info = modelInfo[model as keyof typeof modelInfo];
                if (!info) return null;
                
                return (
                  <button
                    key={model}
                    onClick={() => handleModelSelect(model)}
                    className={cn(
                      "w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-colors",
                      selectedModel === model
                        ? "bg-blue-600 text-white"
                        : "text-mario-page-text hover:bg-mario-hover"
                    )}
                  >
                    <span className="text-lg">{info.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium">{info.name}</div>
                      <div className={cn(
                        "text-xs",
                        selectedModel === model
                          ? "text-white/80"
                          : "text-mario-page-text opacity-60"
                      )}>
                        {info.description}
                      </div>
                    </div>
                    {selectedModel === model && (
                      <span className="text-white">✓</span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ModelSelector;
