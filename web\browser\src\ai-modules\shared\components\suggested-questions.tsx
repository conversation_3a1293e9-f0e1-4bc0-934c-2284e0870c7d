import React from 'react';

interface SuggestedQuestionsProps {
  onQuestionClick?: (question: string) => void;
  className?: string;
}

export const SuggestedQuestions: React.FC<SuggestedQuestionsProps> = ({ 
  onQuestionClick, 
  className 
}) => {
  const questions = [
    "你好，请介绍一下自己",
    "今天天气怎么样？",
    "帮我写一个简单的Python程序",
    "解释一下人工智能的基本概念"
  ];

  return (
    <div className={`space-y-2 ${className}`}>
      <h3 className="text-sm font-medium text-muted-foreground mb-2">建议问题</h3>
      <div className="grid grid-cols-1 gap-2">
        {questions.map((question, index) => (
          <button
            key={index}
            onClick={() => onQuestionClick?.(question)}
            className="text-left p-2 text-sm border rounded-md hover:bg-muted transition-colors"
          >
            {question}
          </button>
        ))}
      </div>
    </div>
  );
};
