import { ITheme, lightTheme, darkTheme, futuristicTheme } from '@mario-ai/shared';

/**
 * 根据主题名称获取主题配置
 * @param themeName 主题名称
 * @returns 主题配置对象
 */
export const getTheme = (themeName: string): ITheme => {
  switch (themeName) {
    case 'wexond-dark':
      return darkTheme;
    case 'mario-futuristic':
    case 'futuristic':
      return futuristicTheme;
    case 'wexond-light':
    default:
      return lightTheme;
  }
};

/**
 * 检查是否为深色主题
 * @param themeName 主题名称
 * @returns 是否为深色主题
 */
export const isDarkTheme = (themeName: string): boolean => {
  return themeName === 'wexond-dark' || themeName === 'mario-futuristic' || themeName === 'futuristic';
};

/**
 * 获取主题的前景色类型
 * @param themeName 主题名称
 * @returns 是否使用浅色前景
 */
export const isLightForeground = (themeName: string): boolean => {
  const theme = getTheme(themeName);
  return theme['dialog.lightForeground'] || false;
};
