// 从 electron/main/index.ts 移动并更新导入路径
import { ipcMain, app, webContents } from 'electron';
import { setIpcMain } from '@wexond/rpc-electron';
setIpcMain(ipcMain);

require('@electron/remote/main').initialize();

// 添加全局错误处理
process.on('uncaughtException', (error) => {
  console.error('[Main] Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('[Main] Unhandled Rejection at:', promise, 'reason:', reason);
});

if (process.env.NODE_ENV === 'development') {
  require('source-map-support').install();
}

import { platform } from 'os';
import { Application } from '@electron/main/core/application';
import { getWebUIURL } from "@electron/main/utils/webui-main";

export const isNightly = app.name === '嗅觉-nightly';

app.name = isNightly ? '嗅觉 Nightly' : '嗅觉';

(process.env as any)['ELECTRON_DISABLE_SECURITY_WARNINGS'] = true;

app.commandLine.appendSwitch('--enable-transparent-visuals');
app.commandLine.appendSwitch(
  'enable-features',
  'CSSColorSchemeUARendering, ImpulseScrollAnimations, ParallelDownloading',
);

if (process.env.NODE_ENV === 'development') {
  app.commandLine.appendSwitch('remote-debugging-port', '9222');
}

ipcMain.setMaxListeners(0);

console.log('[Main] Starting application...');



const application = Application.instance;
application.start();
console.log('[Main] Application started successfully');

// 注意：导航功能现在使用现有的web-contents-call系统

process.on('uncaughtException', (error) => {
  console.error(error);
});

app.on('window-all-closed', () => {
  if (platform() !== 'darwin') {
    app.quit();
  }
});

ipcMain.on('get-webcontents-id', (e) => {
  e.returnValue = e.sender.id;
});

ipcMain.on('get-extensions', (e) => {
  e.returnValue = application.sessions.extensions;
});

ipcMain.on('get-window-id', (e) => {
  e.returnValue = (e.sender as any).windowId;
});

ipcMain.handle(
  `web-contents-call`,
  async (e, { webContentsId, method, args = [] }) => {
    try {
      const wc = webContents.fromId(webContentsId);
      if ("home" == method) {
        return await wc.loadURL(getWebUIURL("newtab"));
      }
      const result = (wc as any)[method](...args);

      if (result) {
        if (result instanceof Promise) {
          return await result;
        }
        return result;
      }
    } catch (e) {
      return false;
    }
  },
);

const backgroundPages: Electron.WebContents[] = [];

app.on('web-contents-created', (e, webContents) => {
  if (webContents.getType() === 'backgroundPage')
    backgroundPages.push(webContents);
});

// 添加上下文菜单支持
ipcMain.on('show-context-menu', (event, { items, position }) => {
  const { Menu } = require('electron');
  
  const template = items.map((item, index) => ({
    label: item.label,
    type: item.type || 'normal',
    enabled: item.enabled !== false,
    click: () => {
      event.reply('context-menu-click', index);
    }
  }));

  const menu = Menu.buildFromTemplate(template);
  menu.popup();
});

// 添加 shell 操作支持
ipcMain.handle('shell-open-path', async (event, path: string) => {
  const { shell } = require('electron');
  return await shell.openPath(path);
});

ipcMain.handle('shell-show-item-in-folder', async (event, path: string) => {
  const { shell } = require('electron');
  shell.showItemInFolder(path);
});

ipcMain.handle('shell-trash-item', async (event, path: string) => {
  const { shell } = require('electron');
  try {
    await shell.trashItem(path);
    return true;
  } catch (error) {
    console.error('Failed to trash item:', error);
    return false;
  }
});

// 添加对话框支持
ipcMain.handle('dialog-show-message-box', async (event, options) => {
  const { dialog, BrowserWindow } = require('electron');
  const window = BrowserWindow.fromWebContents(event.sender);

  return await dialog.showMessageBox(window, {
    type: options.type || 'info',
    title: options.title,
    message: options.message,
    buttons: options.buttons || ['确定']
  });
});

// 注意：数据库功能由 storage.ts 中的 StorageService 提供
// 使用 storage-get, storage-insert 等IPC调用

// 添加命令行参数支持
ipcMain.handle('get-command-line-args', async (event) => {
  console.log('[Main] Get command line args:', process.argv);
  return process.argv;
});

// 添加文件存在检查支持
ipcMain.handle('file-exists', async (event, filePath) => {
  const { existsSync } = require('fs');
  try {
    const exists = existsSync(filePath);
    console.log('[Main] File exists check:', filePath, exists);
    return exists;
  } catch (error) {
    console.error('[Main] File exists check error:', error);
    return false;
  }
});

// 注意：view-method功能应该由现有的视图管理系统处理
// 移除了错误的处理程序，让现有系统正常工作

// 注意：导航功能现在使用现有的web-contents-call系统，不需要单独的处理程序
