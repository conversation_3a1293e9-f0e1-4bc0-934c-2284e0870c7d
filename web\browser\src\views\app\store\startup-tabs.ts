import {makeObservable, observable, toJS} from 'mobx';
import {Store} from '.';
import {prefixHttp, isURL} from '@mario-ai/shared';
import { eventUtils } from '@browser/core/utils/platform-lite';
import {IStartupTab} from '@mario-ai/shared';
import {ITab} from '../models';
import {getWebUIURL} from '@browser/core/utils/webui';

export class StartupTabsStore {
  // ✅ 重构: 移除复杂的数据库抽象层，改为直接IPC调用 (借鉴原始工程)

  public isLoaded = false;

  public list: IStartupTab[] = [];

  private store: Store;

  public constructor(store: Store) {
    makeObservable(this, {list: observable});

    this.store = store;
  }

  private addTabs(options: chrome.tabs.CreateProperties[]) {
    console.log("addTabs", document.readyState);

    function ready(fn: any) {
      if (document.readyState == 'complete') {
        fn();
      } else {
        document.addEventListener('readystatechange', () => {
          if (document.readyState == 'complete') {
            fn();
          }
        });
        // document.addEventListener('DOMContentLoaded', fn);
      }
    }

    ready(() => {
      this.store.tabs.addTabs(options);
    });
  }

  public async load() {
    if (this.isLoaded) return;

    this.isLoaded = true;

    let tabsToLoad: IStartupTab[] = [];

    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      if (this.store.settings.object.startupBehavior.type === 'continue') {
        const result = await eventUtils.invoke('storage-get', {
          scope: 'startupTabs',
          query: { windowId: this.store.windowId }
        });
        tabsToLoad = result || [];
      } else if (this.store.windowId === 1) {
        if (this.store.settings.object.startupBehavior.type === 'urls') {
          const result = await eventUtils.invoke('storage-get', {
            scope: 'startupTabs',
            query: { $or: [{ isUserDefined: true }, { pinned: true }] }
          });
          tabsToLoad = result || [];
          this.list = tabsToLoad.filter((x) => x.isUserDefined);
        } else if (this.store.settings.object.startupBehavior.type === 'empty') {
          const result = await eventUtils.invoke('storage-get', {
            scope: 'startupTabs',
            query: { pinned: true }
          });
          tabsToLoad = result || [];
        }
      }
      console.log('[AppStartupTabsStore] Loaded startup tabs via IPC:', tabsToLoad.length);
    } catch (error) {
      console.error('[AppStartupTabsStore] Error loading startup tabs:', error);
      tabsToLoad = [];
    }

    if (this.store.settings.object.startupBehavior.type !== 'continue') {
      this.clearStartupTabs(false, false);
    }

    // 首先获取命令行参数
    const commandLineArgs = await eventUtils.invoke('get-command-line-args');

    let needsNewTabPage = false;

    // If we have tabs saved, load them
    if (tabsToLoad && tabsToLoad.length > 0) {
      this.clearStartupTabs(true, false);

      this.addTabs(
        tabsToLoad
          .sort((x, y) =>
            x.pinned && y.pinned
              ? x.order - y.order
              : x.pinned
                ? -1
                : y.pinned
                  ? 1
                  : x.order - y.order,
          )
          .map((tab, i) => ({
            url: prefixHttp(tab.url),
            pinned: tab.pinned,
            active:
              i === tabsToLoad.length - 1 &&
              !(commandLineArgs && commandLineArgs.length > 1 && isURL(commandLineArgs[commandLineArgs.length - 1])),
          })),
      );

      // If we only load up pinned tabs, add a new tab page
      if (tabsToLoad.filter((x) => !x.pinned).length == 0) {
        needsNewTabPage = true;
      }
    } else {
      // No tabs saved. Just load a new tab page.
      needsNewTabPage = true;
    }

    // load up command line args. If there are any, we don't need a new tab page.
    if (commandLineArgs && commandLineArgs.length > 1 && this.store.windowId === 1) {
      const path = commandLineArgs[1];
      // 简单的扩展名检查，避免使用Node.js path模块
      const ext = path.substring(path.lastIndexOf('.'));

      // 通过IPC检查文件是否存在，避免直接使用fs模块
      const fileExists = await eventUtils.invoke('file-exists', path);
      if (fileExists && ext === '.html') {
        this.addTabs([{url: `file:///${path}`, active: true}]);
        needsNewTabPage = false;
      } else if (isURL(path)) {
        this.addTabs([
          {
            url: prefixHttp(path),
            active: true,
          },
        ]);
        needsNewTabPage = false;
      }
    }

    if (needsNewTabPage) {
      // 首先添加Chat标签页作为首页
      this.store.tabs.addChatTab({ active: true });

      // 然后添加新标签页（如果需要的话）
      // this.addTabs([{
      //   url: getWebUIURL("newtab"),
      //   active: false
      // }]);
    }
  }

  public async addStartupTabItem(item: IStartupTab) {
    if (!item.url || item.url.startsWith("chrome-ex") || item.url.endsWith(".crx") || item.url.endsWith(".zip")) {
      return;
    }

    // 序列化MobX对象，确保数据库操作安全
    const serializedItem = toJS(item);

    const itemToReplace = this.list.find((x) => x.id === item.id);
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      if (itemToReplace) {
        await eventUtils.invoke('storage-update', {
          scope: 'startupTabs',
          query: { id: item.id },
          value: serializedItem
        });
        this.list[this.list.indexOf(itemToReplace)] = {
          ...itemToReplace,
          ...serializedItem,
        };
      } else {
        const doc = await eventUtils.invoke('storage-insert', {
          scope: 'startupTabs',
          item: serializedItem
        });
        this.list.push(doc);
      }
      console.log('[AppStartupTabsStore] Added/updated startup tab via IPC:', item.id);
    } catch (error) {
      console.error('[AppStartupTabsStore] Error adding/updating startup tab:', error);
    }
  }

  public async removeStartupTabItem(tabId: number) {
    const itemToDelete = this.list.find((x) => x.id === tabId);
    if (itemToDelete) {
      try {
        // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: { id: tabId }
        });
        this.list = this.list.filter((x) => x.id !== tabId);
        console.log('[AppStartupTabsStore] Removed startup tab via IPC:', tabId);
      } catch (error) {
        console.error('[AppStartupTabsStore] Error removing startup tab:', error);
      }
    }
  }

  public async updateStartupTabItem(tab: ITab) {
    if (!tab.url || tab.url.startsWith("chrome-ex") || tab.url.endsWith(".crx") || tab.url.endsWith(".zip")) {
      return;
    }
    this.addStartupTabItem({
      id: tab.id,
      windowId: this.store.windowId,
      url: tab.url,
      pinned: tab.isPinned,
      title: tab.title,
      favicon: tab.favicon,
      isUserDefined: false,
    });
  }

  public async clearStartupTabs(removePinned: boolean, removeUserDefined: boolean) {
    try {
      // ✅ 重构: 使用直接IPC调用替代复杂的数据库抽象 (借鉴原始工程)
      if (removePinned && removeUserDefined) {
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: {},
          multi: true
        });
        this.list = [];
      } else if (!removePinned) {
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: { pinned: false },
          multi: true
        });
        this.list = this.list.filter((x) => x.pinned);
      } else if (!removeUserDefined) {
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: { isUserDefined: false },
          multi: true
        });
        this.list = this.list.filter((x) => x.isUserDefined);
      } else {
        await eventUtils.invoke('storage-remove', {
          scope: 'startupTabs',
          query: { isUserDefined: false, pinned: false },
          multi: true
        });
        this.list = this.list.filter((x) => x.isUserDefined || x.pinned);
      }
      console.log('[AppStartupTabsStore] Cleared startup tabs via IPC');
    } catch (error) {
      console.error('[AppStartupTabsStore] Error clearing startup tabs:', error);
    }
  }
}
