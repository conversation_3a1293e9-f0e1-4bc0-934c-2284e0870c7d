import { StorageService } from '../storage';
import { LibSQLStorageService } from '../storage-libsql';
import { IBookmark, IHistoryItem, IFavicon } from '@electron/types';
import { promises as fs } from 'fs';
import { join } from 'path';
import { getPath } from '@electron/renderer/utils/paths';

/**
 * 数据迁移服务
 * 负责将NeDB数据安全地迁移到LibSQL
 */
export class MigrationService {
  private nedbStorage: StorageService;
  private libsqlStorage: LibSQLStorageService;
  private backupPath: string;

  constructor() {
    this.nedbStorage = new StorageService();
    this.libsqlStorage = new LibSQLStorageService();
    this.backupPath = join(getPath('userData'), 'migration-backup');
  }

  async performMigration(): Promise<void> {
    console.log('🚀 开始数据迁移...');

    try {
      // 1. 创建备份
      await this.createBackup();

      // 2. 初始化NeDB存储服务
      await this.nedbStorage.run();

      // 3. 初始化LibSQL
      await this.libsqlStorage.initialize();

      // 4. 迁移各个数据类型
      await this.migrateBookmarks();
      await this.migrateHistory();
      await this.migrateFavicons();
      await this.migrateFormFill();
      await this.migrateStartupTabs();
      await this.migratePermissions();

      // 5. 验证迁移结果
      await this.validateMigration();

      console.log('🎉 数据迁移完成！');
    } catch (error) {
      console.error('❌ 数据迁移失败:', error);
      await this.rollback();
      throw error;
    }
  }

  private async createBackup(): Promise<void> {
    console.log('📦 创建数据备份...');
    
    try {
      await fs.mkdir(this.backupPath, { recursive: true });
      
      const userDataPath = getPath('userData');
      const dbFiles = [
        'bookmarks.db',
        'history.db', 
        'favicons.db',
        'formfill.db',
        'startupTabs.db',
        'permissions.db'
      ];

      for (const dbFile of dbFiles) {
        const sourcePath = join(userDataPath, dbFile);
        const backupFilePath = join(this.backupPath, dbFile);
        
        try {
          await fs.copyFile(sourcePath, backupFilePath);
          console.log(`✅ 备份 ${dbFile}`);
        } catch (error) {
          console.warn(`⚠️  ${dbFile} 不存在，跳过备份`);
        }
      }
      
      console.log('✅ 数据备份完成');
    } catch (error) {
      console.error('❌ 创建备份失败:', error);
      throw error;
    }
  }

  private async migrateBookmarks(): Promise<void> {
    console.log('📚 迁移书签数据...');
    
    try {
      const nedbBookmarks = await this.nedbStorage.find<IBookmark>({
        scope: 'bookmarks',
        query: {}
      });

      console.log(`发现 ${nedbBookmarks.length} 个书签`);

      for (const bookmark of nedbBookmarks) {
        await this.libsqlStorage.insert({
          scope: 'bookmarks',
          item: bookmark
        });
      }

      console.log('✅ 书签迁移完成');
    } catch (error) {
      console.error('❌ 书签迁移失败:', error);
      throw error;
    }
  }

  private async migrateHistory(): Promise<void> {
    console.log('📜 迁移历史记录...');
    
    try {
      const nedbHistory = await this.nedbStorage.find<IHistoryItem>({
        scope: 'history',
        query: {}
      });

      console.log(`发现 ${nedbHistory.length} 条历史记录`);

      // 分批迁移，避免内存问题
      const batchSize = 100;
      for (let i = 0; i < nedbHistory.length; i += batchSize) {
        const batch = nedbHistory.slice(i, i + batchSize);
        
        for (const historyItem of batch) {
          await this.libsqlStorage.insert({
            scope: 'history',
            item: historyItem
          });
        }
        
        console.log(`已迁移 ${Math.min(i + batchSize, nedbHistory.length)}/${nedbHistory.length} 条历史记录`);
      }

      console.log('✅ 历史记录迁移完成');
    } catch (error) {
      console.error('❌ 历史记录迁移失败:', error);
      throw error;
    }
  }

  private async migrateFavicons(): Promise<void> {
    console.log('🎨 迁移网站图标...');
    
    try {
      const nedbFavicons = await this.nedbStorage.find<IFavicon>({
        scope: 'favicons',
        query: {}
      });

      console.log(`发现 ${nedbFavicons.length} 个网站图标`);

      for (const favicon of nedbFavicons) {
        await this.libsqlStorage.insert({
          scope: 'favicons',
          item: favicon
        });
      }

      console.log('✅ 网站图标迁移完成');
    } catch (error) {
      console.error('❌ 网站图标迁移失败:', error);
      throw error;
    }
  }

  private async migrateFormFill(): Promise<void> {
    console.log('📝 迁移表单填充数据...');
    
    try {
      const nedbFormFill = await this.nedbStorage.find({
        scope: 'formfill',
        query: {}
      });

      console.log(`发现 ${nedbFormFill.length} 条表单填充数据`);

      for (const formFillItem of nedbFormFill) {
        await this.libsqlStorage.insert({
          scope: 'formfill',
          item: formFillItem
        });
      }

      console.log('✅ 表单填充数据迁移完成');
    } catch (error) {
      console.error('❌ 表单填充数据迁移失败:', error);
      throw error;
    }
  }

  private async migrateStartupTabs(): Promise<void> {
    console.log('🚀 迁移启动标签页...');
    
    try {
      const nedbStartupTabs = await this.nedbStorage.find({
        scope: 'startupTabs',
        query: {}
      });

      console.log(`发现 ${nedbStartupTabs.length} 个启动标签页`);

      for (const startupTab of nedbStartupTabs) {
        await this.libsqlStorage.insert({
          scope: 'startupTabs',
          item: startupTab
        });
      }

      console.log('✅ 启动标签页迁移完成');
    } catch (error) {
      console.error('❌ 启动标签页迁移失败:', error);
      throw error;
    }
  }

  private async migratePermissions(): Promise<void> {
    console.log('🔐 迁移权限设置...');
    
    try {
      const nedbPermissions = await this.nedbStorage.find({
        scope: 'permissions',
        query: {}
      });

      console.log(`发现 ${nedbPermissions.length} 条权限设置`);

      for (const permission of nedbPermissions) {
        await this.libsqlStorage.insert({
          scope: 'permissions',
          item: permission
        });
      }

      console.log('✅ 权限设置迁移完成');
    } catch (error) {
      console.error('❌ 权限设置迁移失败:', error);
      throw error;
    }
  }

  private async validateMigration(): Promise<void> {
    console.log('🔍 验证迁移结果...');
    
    try {
      // 验证书签数量
      const nedbBookmarkCount = (await this.nedbStorage.find({
        scope: 'bookmarks',
        query: {}
      })).length;
      
      const libsqlBookmarkCount = (await this.libsqlStorage.find({
        scope: 'bookmarks',
        query: {}
      })).length;

      if (nedbBookmarkCount !== libsqlBookmarkCount) {
        throw new Error(`书签数量不匹配: NeDB=${nedbBookmarkCount}, LibSQL=${libsqlBookmarkCount}`);
      }

      // 验证历史记录数量
      const nedbHistoryCount = (await this.nedbStorage.find({
        scope: 'history',
        query: {}
      })).length;
      
      const libsqlHistoryCount = (await this.libsqlStorage.find({
        scope: 'history',
        query: {}
      })).length;

      if (nedbHistoryCount !== libsqlHistoryCount) {
        throw new Error(`历史记录数量不匹配: NeDB=${nedbHistoryCount}, LibSQL=${libsqlHistoryCount}`);
      }

      console.log('✅ 迁移验证通过');
      console.log(`   书签: ${libsqlBookmarkCount} 条`);
      console.log(`   历史记录: ${libsqlHistoryCount} 条`);
    } catch (error) {
      console.error('❌ 迁移验证失败:', error);
      throw error;
    }
  }

  private async rollback(): Promise<void> {
    console.log('🔄 执行回滚操作...');
    
    try {
      // 关闭LibSQL连接
      await this.libsqlStorage.close();
      
      // 删除LibSQL数据库文件
      const libsqlDbPath = join(getPath('userData'), 'mario-ai.db');
      try {
        await fs.unlink(libsqlDbPath);
        console.log('✅ 已删除LibSQL数据库文件');
      } catch (error) {
        console.warn('⚠️  LibSQL数据库文件不存在或删除失败');
      }
      
      console.log('✅ 回滚完成，系统恢复到迁移前状态');
    } catch (error) {
      console.error('❌ 回滚失败:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    await this.libsqlStorage.close();
  }
}

// 导出迁移函数供外部调用
export async function runDataMigration(): Promise<void> {
  const migration = new MigrationService();
  try {
    await migration.performMigration();
  } finally {
    await migration.cleanup();
  }
}

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  runDataMigration().catch(console.error);
}
