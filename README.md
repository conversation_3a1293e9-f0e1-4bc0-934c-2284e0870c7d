# Wexond Browser - Refactored

This is a refactored version of the Wexond browser project, split into separate Electron and Web components with Vite build system.

## Project Structure

```
├── electron/          # Electron main process and preload scripts
├── web/              # Web frontend (React + Vite)
├── scripts/          # Development and build scripts
├── shared/           # Shared constants and types
└── package.json      # Root package.json with workspace configuration
```

## Development

1. Install dependencies:
```bash
pnpm install
# or
pnpm run install-deps
```

2. Start development:
```bash
# Traditional way
pnpm run dev

# PNPM parallel way (recommended)
pnpm run dev:pnpm

# Individual packages
pnpm run dev:electron
pnpm run dev:web
pnpm run dev:koodo
```

## Building

Build the entire project:
```bash
pnpm run build
```

Individual builds:
```bash
pnpm run build:electron
pnpm run build:web
pnpm run build:koodo
```

## Platform-specific builds

- Windows: `pnpm run compile-win32`
- macOS: `pnpm run compile-darwin`
- Linux: `pnpm run compile-linux`

## Architecture

- **Electron**: Contains the main process, preload scripts, and Electron-specific code
- **Web**: Contains the React frontend built with Vite
- **Shared**: Contains shared constants and type definitions
- **Scripts**: Contains development and build automation scripts