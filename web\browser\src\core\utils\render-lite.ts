/**
 * 轻量级渲染工具 - 为独立入口文件提供平台无关的渲染能力
 * 解决renderUI对electron renderer-process的直接依赖问题
 */

import React from 'react';
import ReactDOM from 'react-dom';
import { configure } from 'mobx';

/**
 * 轻量级UI渲染函数
 * 替代原有的renderUI，支持浏览器和Electron环境
 */
export const renderUI = (Component: React.ComponentType, containerId: string = 'app') => {
  // 配置MobX：关闭严格模式，允许在action外修改observable
  configure({ enforceActions: 'never' });
  // 检查是否在electron环境中
  const isElectron = typeof window !== 'undefined' &&
                    (window as any).require !== undefined &&
                    process?.versions?.electron !== undefined;

  // 统一使用标准React渲染，无论是浏览器还是Electron环境
  console.log('[Render-Lite] Rendering component in', isElectron ? 'Electron' : 'Browser', 'environment');
  let container = document.getElementById(containerId);

  // 确保有根元素
  if (!container) {
    const appDiv = document.createElement('div');
    appDiv.id = containerId;
    document.body.appendChild(appDiv);
    container = appDiv;
  }

  // 使用React 17的渲染方式
  ReactDOM.render(React.createElement(Component), container);
};

/**
 * 轻量级WebUI渲染函数
 * 替代原有的renderWebUI，支持设置页面等WebUI界面
 */
export const renderWebUI = (Component: React.ComponentType) => {
  // 检查是否在electron环境中
  const isElectron = typeof window !== 'undefined' && 
                    (window as any).require !== undefined &&
                    process?.versions?.electron !== undefined;

  if (isElectron) {
    // Electron环境：使用原有的electron WebUI渲染逻辑
    try {
      // 动态导入electron的renderWebUI
      const electronWebUIRenderer = (window as any).require('@electron/shared/utils/webui-entry');
      if (electronWebUIRenderer && electronWebUIRenderer.renderWebUI) {
        electronWebUIRenderer.renderWebUI(Component);
        return;
      }
    } catch (error) {
      console.warn('[Render-Lite] Failed to load electron WebUI renderer, falling back to web rendering:', error);
    }
  }

  // 浏览器环境或electron fallback：使用标准React渲染
  let container = document.getElementById('app');
  
  // 确保有根元素
  if (!container) {
    const appDiv = document.createElement('div');
    appDiv.id = 'app';
    appDiv.style.cssText = `
      width: 100%;
      height: 100vh;
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    document.body.appendChild(appDiv);
    container = appDiv;
  }

  // 使用React 17的渲染方式
  ReactDOM.render(React.createElement(Component), container);
};

/**
 * 检查当前运行环境
 */
export const getRenderEnvironment = () => {
  const isElectron = typeof window !== 'undefined' && 
                    (window as any).require !== undefined &&
                    process?.versions?.electron !== undefined;
  
  return {
    isElectron,
    isBrowser: !isElectron,
    platform: isElectron ? 'electron' : 'browser'
  };
};

/**
 * 为开发调试提供的环境信息
 */
if (typeof window !== 'undefined') {
  const env = getRenderEnvironment();
  console.log(`[Render-Lite] Running in ${env.platform} environment`);
}