import { ipcMain } from 'electron';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  thinking?: string;
  isStreaming?: boolean;
  metadata?: string; // JSON string for additional data
}

interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  timestamp: Date;
  systemPrompt?: string;
  messageCount: number;
}

class ChatService {
  private sessions: Map<string, ChatSession> = new Map();
  private currentSessionId: string | null = null;

  constructor() {
    this.setupIpcHandlers();
  }

  private setupIpcHandlers() {
    // Get chat history
    ipcMain.handle('chat:get-history', async (event, params?: { sessionId?: string; limit?: number; offset?: number }) => {
      try {
        const { sessionId, limit = 50, offset = 0 } = params || {};
        const targetSessionId = sessionId || this.currentSessionId || 'default';
        const session = this.sessions.get(targetSessionId);

        const messages = session?.messages || [];
        const paginatedMessages = messages.slice(offset, offset + limit);

        return {
          success: true,
          data: {
            messages: paginatedMessages,
            total: messages.length,
            sessionId: targetSessionId,
            hasMore: offset + limit < messages.length
          }
        };
      } catch (error) {
        console.error('Error getting chat history:', error);
        return {
          success: false,
          error: 'Failed to get chat history'
        };
      }
    });

    // Send message
    ipcMain.handle('chat:send-message', async (event, data) => {
      try {
        const { message, sessionId, model, systemPrompt } = data;
        
        if (!message) {
          return {
            success: false,
            error: 'Message is required'
          };
        }

        const targetSessionId = sessionId || this.currentSessionId || 'default';
        
        // Get or create session
        let session = this.sessions.get(targetSessionId);
        if (!session) {
          session = {
            id: targetSessionId,
            title: message.substring(0, 50) + '...',
            messages: [],
            timestamp: new Date(),
            systemPrompt: systemPrompt || '',
            messageCount: 0
          };
          this.sessions.set(targetSessionId, session);
        }

        // Add user message
        const userMessage: ChatMessage = {
          id: `msg-${Date.now()}-user`,
          role: 'user',
          content: message,
          timestamp: new Date()
        };
        session.messages.push(userMessage);

        // TODO: Implement actual AI response
        // For now, return a placeholder response
        const assistantMessage: ChatMessage = {
          id: `msg-${Date.now()}-assistant`,
          role: 'assistant',
          content: '这是一个测试回复。AI服务正在开发中...',
          timestamp: new Date()
        };
        session.messages.push(assistantMessage);
        session.messageCount = session.messages.length;

        this.currentSessionId = targetSessionId;

        return {
          success: true,
          data: {
            id: assistantMessage.id,
            message: assistantMessage.content,
            sessionId: targetSessionId,
            model: model || 'default',
            timestamp: assistantMessage.timestamp.toISOString(),
            usage: {
              promptTokens: 0,
              completionTokens: 0,
              totalTokens: 0
            }
          }
        };
      } catch (error) {
        console.error('Error sending message:', error);
        return {
          success: false,
          error: 'Failed to send message'
        };
      }
    });

    // Create new chat session
    ipcMain.handle('chat:new-session', async (event, params?: { title?: string; systemPrompt?: string }) => {
      try {
        const { title, systemPrompt } = params || {};
        const sessionId = `session-${Date.now()}`;
        const session: ChatSession = {
          id: sessionId,
          title: title || 'New Chat',
          messages: [],
          timestamp: new Date(),
          systemPrompt: systemPrompt || '',
          messageCount: 0
        };

        this.sessions.set(sessionId, session);
        this.currentSessionId = sessionId;

        return {
          success: true,
          data: session
        };
      } catch (error) {
        console.error('Error creating new chat session:', error);
        return {
          success: false,
          error: 'Failed to create new chat session'
        };
      }
    });

    // Delete chat session
    ipcMain.handle('chat:delete-session', async (event, sessionId) => {
      try {
        if (!sessionId) {
          return {
            success: false,
            error: 'Session ID is required'
          };
        }

        const deleted = this.sessions.delete(sessionId);
        
        if (this.currentSessionId === sessionId) {
          this.currentSessionId = null;
        }

        return {
          success: true,
          data: { deleted }
        };
      } catch (error) {
        console.error('Error deleting chat session:', error);
        return {
          success: false,
          error: 'Failed to delete chat session'
        };
      }
    });

    // Clear chat history
    ipcMain.handle('chat:clear-history', async (event, sessionId) => {
      try {
        const targetSessionId = sessionId || this.currentSessionId;
        
        if (targetSessionId) {
          const session = this.sessions.get(targetSessionId);
          if (session) {
            session.messages = [];
          }
        }

        return {
          success: true,
          data: { cleared: true }
        };
      } catch (error) {
        console.error('Error clearing chat history:', error);
        return {
          success: false,
          error: 'Failed to clear chat history'
        };
      }
    });

    // Get all sessions
    ipcMain.handle('chat:get-sessions', async (event) => {
      try {
        const sessions = Array.from(this.sessions.values());
        
        return {
          success: true,
          data: {
            sessions,
            currentSessionId: this.currentSessionId
          }
        };
      } catch (error) {
        console.error('Error getting chat sessions:', error);
        return {
          success: false,
          error: 'Failed to get chat sessions'
        };
      }
    });
  }

  // Get current session
  getCurrentSession(): ChatSession | null {
    if (!this.currentSessionId) return null;
    return this.sessions.get(this.currentSessionId) || null;
  }

  // Set current session
  setCurrentSession(sessionId: string): boolean {
    if (this.sessions.has(sessionId)) {
      this.currentSessionId = sessionId;
      return true;
    }
    return false;
  }
}

export const chatService = new ChatService();
