import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';

// Chat sessions table
export const chatSessions = sqliteTable('chat_sessions', {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
});

// Chat messages table
export const chatMessages = sqliteTable('chat_messages', {
  id: text('id').primaryKey(),
  sessionId: text('session_id').notNull().references(() => chatSessions.id, { onDelete: 'cascade' }),
  role: text('role', { enum: ['user', 'assistant', 'system'] }).notNull(),
  content: text('content').notNull(),
  thinking: text('thinking'), // For assistant thinking process
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  isStreaming: integer('is_streaming', { mode: 'boolean' }).default(false),
  metadata: text('metadata'), // JSON string for additional data
});

// Chat settings table
export const chatSettings = sqliteTable('chat_settings', {
  id: text('id').primaryKey(),
  userId: text('user_id').default('default'),
  apiKey: text('api_key'),
  endpoint: text('endpoint'),
  selectedModel: text('selected_model').default('qwen-plus'),
  systemPrompt: text('system_prompt'),
  conversationRounds: integer('conversation_rounds').default(6),
  memoryModeEnabled: integer('memory_mode_enabled', { mode: 'boolean' }).default(false),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
});

// Chat files table (for file uploads)
export const chatFiles = sqliteTable('chat_files', {
  id: text('id').primaryKey(),
  messageId: text('message_id').notNull().references(() => chatMessages.id, { onDelete: 'cascade' }),
  fileName: text('file_name').notNull(),
  fileType: text('file_type').notNull(),
  fileSize: integer('file_size').notNull(),
  filePath: text('file_path'), // Local file path
  uploadedAt: integer('uploaded_at', { mode: 'timestamp' }).notNull(),
});

export type ChatSession = typeof chatSessions.$inferSelect;
export type NewChatSession = typeof chatSessions.$inferInsert;

export type ChatMessage = typeof chatMessages.$inferSelect;
export type NewChatMessage = typeof chatMessages.$inferInsert;

export type ChatSettings = typeof chatSettings.$inferSelect;
export type NewChatSettings = typeof chatSettings.$inferInsert;

export type ChatFile = typeof chatFiles.$inferSelect;
export type NewChatFile = typeof chatFiles.$inferInsert;
