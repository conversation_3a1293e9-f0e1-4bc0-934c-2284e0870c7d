import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { encryptedStorage } from '@shared/lib/encrypted-storage';
import { ChatHistoryCacheManager } from '@shared/services/ChatHistoryCacheManager';
import { getMemoryService } from '../../shared/services/MemoryService';

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  thinking?: string;
  isStreaming?: boolean;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  timestamp: Date;
  lastUpdated: Date;
}

export interface ChatGroup {
  title: string;
  chats: ChatSession[];
}

interface ChatHistoryContextType {
  // Current session management
  currentChatId: string;
  currentMessages: Message[];
  setCurrentMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updatedMessage: Partial<Message>) => void;

  // Session management
  chatSessions: ChatSession[];
  createNewChat: () => string;
  loadChatSession: (sessionId: string) => void;
  updateCurrentChatTitle: (title: string) => void;
  deleteChatSession: (sessionId: string) => void;

  // History organization
  getChatGroups: () => ChatGroup[];
  searchChats: (query: string) => ChatSession[];

  // Loading state
  isLoading: boolean;

  // Memory management
  clearMemoryCache: () => void;
  getMemoryUsage: () => { sessionCount: number; messageCount: number };
  saveConversationToMemory: (messages: Message[], title: string) => Promise<void>;

  // Cache management
  getCacheStats: () => { totalSize: number; sessionCount: number; oldestSession?: Date; newestSession?: Date };
  getCacheUsagePercentage: () => number;
  performManualCleanup: () => number;

  // Import/Export
  exportChatHistory: () => Promise<string>;
  importChatHistory: (data: string) => Promise<void>;
  getStorageInfo: () => { size: number; encrypted: boolean };
}

const ChatHistoryContext = createContext<ChatHistoryContextType | undefined>(undefined);

export const useChatHistory = () => {
  const context = useContext(ChatHistoryContext);
  if (!context) {
    throw new Error('useChatHistory must be used within a ChatHistoryProvider');
  }
  return context;
};

interface ChatHistoryProviderProps {
  children: React.ReactNode;
  maxSessions?: number;
  maxMessagesPerSession?: number;
}

export const ChatHistoryProvider: React.FC<ChatHistoryProviderProps> = ({
  children,
  maxSessions = 100,
  maxMessagesPerSession = 200
}) => {
  const [currentChatId, setCurrentChatId] = useState<string>(() => `chat-${Date.now()}`);
  const [currentMessages, setCurrentMessages] = useState<Message[]>([]);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load initial data from encrypted storage
  useEffect(() => {
    const loadSessions = async () => {
      console.log('🔄 Loading chat sessions from storage...');
      setIsLoading(true);

      try {
        const sessions = await encryptedStorage.loadChatSessions();
        console.log(`✅ Loaded ${sessions.length} chat sessions from encrypted storage`);
        setChatSessions(sessions);

        if (sessions.length > 0) {
          console.log('📊 Chat sessions loaded:', sessions.map(s => ({
            id: s.id,
            title: s.title,
            messageCount: s.messages.length,
            lastUpdated: s.lastUpdated
          })));
        }
      } catch (error) {
        console.error('❌ Failed to load chat sessions from encrypted storage:', error);
        // Fallback to localStorage for backward compatibility
        const savedSessions = localStorage.getItem('chat_sessions');
        if (savedSessions) {
          console.log('🔄 Attempting to migrate from legacy localStorage...');
          try {
            const sessions = JSON.parse(savedSessions).map((session: any) => ({
              ...session,
              timestamp: new Date(session.timestamp),
              lastUpdated: new Date(session.lastUpdated || session.timestamp),
              messages: session.messages.map((msg: any) => ({
                ...msg,
                timestamp: new Date(msg.timestamp)
              }))
            }));
            console.log(`✅ Migrated ${sessions.length} sessions from localStorage`);
            setChatSessions(sessions);
            // Migrate to encrypted storage
            await encryptedStorage.saveChatSessions(sessions);
            localStorage.removeItem('chat_sessions');
          } catch (migrationError) {
            console.error('❌ Failed to migrate chat sessions:', migrationError);
          }
        } else {
          console.log('ℹ️ No existing chat sessions found in storage');
        }
      } finally {
        setIsLoading(false);
        console.log('✅ Chat history loading completed');
      }
    };

    loadSessions();
  }, []);

  // Save sessions to encrypted storage whenever they change with cache management
  useEffect(() => {
    if (chatSessions.length > 0) {
      // Apply cache management and FIFO cleanup
      const managedSessions = ChatHistoryCacheManager.validateAndCleanSessions(chatSessions);

      // Update state if sessions were cleaned up
      if (managedSessions.length !== chatSessions.length) {
        console.log(`🧹 Chat history cleaned: ${chatSessions.length - managedSessions.length} sessions removed`);
        setChatSessions(managedSessions);
        return; // Let the next effect cycle handle the save
      }

      // Log cache status for monitoring
      ChatHistoryCacheManager.logCacheStatus(managedSessions);

      encryptedStorage.saveChatSessions(managedSessions).catch(error => {
        console.error('Failed to save chat sessions:', error);
        // Fallback to localStorage
        localStorage.setItem('chat_sessions', JSON.stringify(managedSessions));
      });
    }
  }, [chatSessions]);

  const addMessage = useCallback((message: Message) => {
    setCurrentMessages(prev => {
      const newMessages = [...prev, message];
      // Limit messages per session
      if (newMessages.length > maxMessagesPerSession) {
        return newMessages.slice(-maxMessagesPerSession);
      }
      return newMessages;
    });
  }, [maxMessagesPerSession]);

  const updateMessage = useCallback((messageId: string, updatedMessage: Partial<Message>) => {
    setCurrentMessages(prev => {
      return prev.map(msg =>
        msg.id === messageId
          ? { ...msg, ...updatedMessage }
          : msg
      );
    });
  }, []);

  const createNewChat = useCallback(() => {
    // Save current chat if it has messages
    if (currentMessages.length > 0) {
      const currentSession: ChatSession = {
        id: currentChatId,
        title: currentMessages[0]?.content.slice(0, 50) + (currentMessages[0]?.content.length > 50 ? '...' : '') || '新对话',
        messages: currentMessages,
        timestamp: new Date(),
        lastUpdated: new Date()
      };

      setChatSessions(prev => {
        const updated = [currentSession, ...prev.filter(s => s.id !== currentChatId)];
        // Limit total sessions
        return updated.slice(0, maxSessions);
      });

      // Save conversation to memory if it has meaningful content
      saveConversationToMemory(currentMessages, currentSession.title);
    }

    // Create new chat
    const newChatId = `chat-${Date.now()}`;
    setCurrentChatId(newChatId);
    setCurrentMessages([]);
    return newChatId;
  }, [currentChatId, currentMessages, maxSessions]);

  // Save conversation to memory service
  const saveConversationToMemory = useCallback(async (messages: Message[], title: string) => {
    try {
      console.log('💾 Attempting to save conversation to memory...', {
        messageCount: messages.length,
        title: title,
        hasStreamingMessages: messages.some(m => m.isStreaming)
      });

      const memoryService = getMemoryService();
      if (!memoryService || !memoryService.isReady()) {
        console.warn('⚠️ Memory service not available, skipping conversation save');
        return;
      }

      // Filter out streaming messages and format for memory
      const completedMessages = messages.filter(msg => !msg.isStreaming);
      console.log(`📝 Filtered ${completedMessages.length} completed messages from ${messages.length} total`);

      // Only save conversations with at least one user message and one assistant response
      if (completedMessages.length < 2) {
        console.log('⏭️ Skipping conversation save: insufficient messages (need at least 2)');
        return;
      }

      // Check if conversation has meaningful content
      const totalContent = completedMessages.map(m => m.content).join(' ');
      if (totalContent.length < 50) {
        console.log('⏭️ Skipping conversation save: content too short (<50 chars)');
        return;
      }

      // Format messages for memory storage
      const formattedMessages = completedMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      console.log('🚀 Saving conversation to memory...', {
        formattedMessageCount: formattedMessages.length,
        totalContentLength: totalContent.length,
        preview: totalContent.substring(0, 100) + '...'
      });

      // Save to memory
      const memoryId = await memoryService.addConversationMemory(formattedMessages, title);
      if (memoryId) {
        console.log('✅ Conversation saved to memory successfully:', memoryId);
      } else {
        console.error('❌ Failed to save conversation: no memory ID returned');
      }
    } catch (error) {
      console.error('❌ Failed to save conversation to memory:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }, []);

  const loadChatSession = useCallback((sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (session) {
      // Save current chat first if it has messages
      if (currentMessages.length > 0 && currentChatId !== sessionId) {
        const currentSession: ChatSession = {
          id: currentChatId,
          title: currentMessages[0]?.content.slice(0, 50) + (currentMessages[0]?.content.length > 50 ? '...' : '') || '新对话',
          messages: currentMessages,
          timestamp: new Date(),
          lastUpdated: new Date()
        };

        setChatSessions(prev => [currentSession, ...prev.filter(s => s.id !== currentChatId)]);

        // Save conversation to memory if it has meaningful content
        saveConversationToMemory(currentMessages, currentSession.title);
      }

      setCurrentChatId(sessionId);
      setCurrentMessages(session.messages);
    }
  }, [chatSessions, currentChatId, currentMessages, saveConversationToMemory]);

  const updateCurrentChatTitle = useCallback((title: string) => {
    setChatSessions(prev => 
      prev.map(session => 
        session.id === currentChatId 
          ? { ...session, title, lastUpdated: new Date() }
          : session
      )
    );
  }, [currentChatId]);

  const deleteChatSession = useCallback((sessionId: string) => {
    setChatSessions(prev => prev.filter(s => s.id !== sessionId));
    if (currentChatId === sessionId) {
      const newChatId = `chat-${Date.now()}`;
      setCurrentChatId(newChatId);
      setCurrentMessages([]);
    }
  }, [currentChatId]);

  const getChatGroups = useCallback((): ChatGroup[] => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const groups: ChatGroup[] = [
      { title: '今天', chats: [] },
      { title: '昨天', chats: [] },
      { title: '本周', chats: [] },
      { title: '本月', chats: [] },
      { title: '更早', chats: [] }
    ];

    chatSessions.forEach(session => {
      const sessionDate = new Date(session.lastUpdated);
      if (sessionDate >= today) {
        groups[0].chats.push(session);
      } else if (sessionDate >= yesterday) {
        groups[1].chats.push(session);
      } else if (sessionDate >= weekAgo) {
        groups[2].chats.push(session);
      } else if (sessionDate >= monthAgo) {
        groups[3].chats.push(session);
      } else {
        groups[4].chats.push(session);
      }
    });

    // Sort chats within each group by lastUpdated (newest first)
    groups.forEach(group => {
      group.chats.sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime());
    });

    // Filter out empty groups
    return groups.filter(group => group.chats.length > 0);
  }, [chatSessions]);

  const searchChats = useCallback((query: string): ChatSession[] => {
    if (!query.trim()) return [];
    
    const lowercaseQuery = query.toLowerCase();
    return chatSessions.filter(session => 
      session.title.toLowerCase().includes(lowercaseQuery) ||
      session.messages.some(message => 
        message.content.toLowerCase().includes(lowercaseQuery)
      )
    );
  }, [chatSessions]);

  const clearMemoryCache = useCallback(async () => {
    console.warn('🗑️ Clearing all chat history cache - this action cannot be undone');
    setChatSessions([]);
    setCurrentMessages([]);
    try {
      await encryptedStorage.clearStorage();
    } catch (error) {
      console.error('Failed to clear encrypted storage:', error);
      localStorage.removeItem('chat_sessions');
    }
    // Clear cache stats
    localStorage.removeItem('chat_cache_stats');
  }, []);

  const getCacheStats = useCallback(() => {
    return ChatHistoryCacheManager.getCacheStats(chatSessions);
  }, [chatSessions]);

  const getCacheUsagePercentage = useCallback(() => {
    return ChatHistoryCacheManager.getCacheUsagePercentage(chatSessions);
  }, [chatSessions]);

  const performManualCleanup = useCallback(() => {
    console.log('🧹 Performing manual chat history cleanup...');
    const cleanedSessions = ChatHistoryCacheManager.performFIFOCleanup(chatSessions);
    setChatSessions(cleanedSessions);
    return cleanedSessions.length;
  }, [chatSessions]);

  const getMemoryUsage = useCallback(() => {
    const messageCount = chatSessions.reduce((total, session) => total + session.messages.length, 0) + currentMessages.length;
    return {
      sessionCount: chatSessions.length + (currentMessages.length > 0 ? 1 : 0),
      messageCount
    };
  }, [chatSessions, currentMessages]);

  const exportChatHistory = useCallback(async () => {
    try {
      return await encryptedStorage.exportChatSessions();
    } catch (error) {
      console.error('Failed to export chat history:', error);
      throw new Error('Failed to export chat history');
    }
  }, []);

  const importChatHistory = useCallback(async (data: string) => {
    try {
      await encryptedStorage.importChatSessions(data);
      const sessions = await encryptedStorage.loadChatSessions();
      setChatSessions(sessions);
    } catch (error) {
      console.error('Failed to import chat history:', error);
      throw new Error('Failed to import chat history');
    }
  }, []);

  const getStorageInfo = useCallback(() => {
    return {
      size: encryptedStorage.getStorageSize(),
      encrypted: encryptedStorage.isEncryptionAvailable()
    };
  }, []);

  const value: ChatHistoryContextType = {
    currentChatId,
    currentMessages,
    setCurrentMessages,
    addMessage,
    updateMessage,
    chatSessions,
    createNewChat,
    loadChatSession,
    updateCurrentChatTitle,
    deleteChatSession,
    getChatGroups,
    searchChats,
    isLoading,
    clearMemoryCache,
    getMemoryUsage,
    getCacheStats,
    getCacheUsagePercentage,
    performManualCleanup,
    exportChatHistory,
    importChatHistory,
    getStorageInfo,
    saveConversationToMemory
  };

  return (
    <ChatHistoryContext.Provider value={value}>
      {children}
    </ChatHistoryContext.Provider>
  );
};
