import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { IHistoryItem } from '@mario-ai/shared';
import store from '../../store';
import { ICON_PAGE, ICON_ADD } from '@mario-ai/shared';
import {getWebUIURL} from '@browser/core/utils/webui';
import { cn } from '@browser/utils/tailwind-helpers';

const onClick = (url: string) => () => {
  console.log("TopSite", url);
  if (url != '' && url != null) {
    window.location.href = url;
  } else {
    window.location.href = getWebUIURL('bookmarks');
  }
};

export const TopSite = observer(({ item }: { item?: IHistoryItem }) => {
  const { title, favicon, url } = item || {};
  const custom = favicon === '' || favicon == null;
  const isAdd = url == null || url == '';
  const imageSet = store.imageVisible && store.image;

  let fav = ICON_PAGE;

  if (!custom) {
    fav = favicon;
  } else if(url && url.startsWith("http")){
    try {
      fav = new URL('/favicon.ico', url).href;
    } catch (e) {
      console.log(e);
    }
  }

  // 背景色计算函数
  const getBgColor = (imageSet: boolean, dark: boolean, hover: boolean = false) => {
    if (imageSet) {
      if (!dark) {
        return hover ? 'rgba(255, 255, 255, 0.5)' : 'rgba(255, 255, 255, 0.4)';
      } else {
        return hover ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.3)';
      }
    } else {
      if (dark) {
        return hover ? 'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.2)';
      } else {
        return hover ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.1)';
      }
    }
  };

  // Item 样式 - Tailwind 版本
  const itemClasses = cn(
    'rounded-md w-[120px] h-[90px] transition-all duration-200 cursor-pointer',
    'flex flex-col items-center justify-center overflow-hidden',
    'backdrop-blur-sm relative z-[1]',
    'hover:shadow-lg'
  );

  const itemStyle = {
    backgroundColor: getBgColor(imageSet, store.theme['pages.lightForeground']),
  };

  const itemHoverStyle = {
    backgroundColor: getBgColor(imageSet, store.theme['pages.lightForeground'], true),
  };

  // Icon 样式 - Tailwind 版本
  const iconClasses = cn(
    'w-8 h-8 bg-center bg-no-repeat bg-contain transition-all duration-200',
    'rounded-sm relative overflow-hidden',
    // 添加图标的样式
    isAdd && [
      'border-2 border-dashed',
      imageSet && !store.theme['pages.lightForeground']
        ? 'border-white/30'
        : 'border-black/20'
    ]
  );

  const iconStyle = {
    backgroundImage: isAdd ? `url(${ICON_ADD})` : `url(${fav})`,
    filter: isAdd && imageSet && store.theme['pages.lightForeground'] ? 'invert(100%)' : 'none',
  };

  // Title 样式 - Tailwind 版本
  const titleClasses = cn(
    'mt-2 text-xs text-center max-w-full overflow-hidden text-ellipsis',
    'whitespace-nowrap pointer-events-none',
    imageSet && store.theme['pages.lightForeground'] ? 'text-white' : 'text-black'
  );

  return (
    <div
      className={itemClasses}
      style={itemStyle}
      onClick={onClick(url)}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = getBgColor(imageSet, store.theme['pages.lightForeground'], true);
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = getBgColor(imageSet, store.theme['pages.lightForeground']);
      }}
    >
      <div
        className={iconClasses}
        style={iconStyle}
        onError={(e) => {
          (e.target as HTMLElement).style.backgroundImage = `url(${ICON_PAGE})`;
        }}
      ></div>
      {title && <div className={titleClasses}>{title}</div>}
    </div>
  );
});
