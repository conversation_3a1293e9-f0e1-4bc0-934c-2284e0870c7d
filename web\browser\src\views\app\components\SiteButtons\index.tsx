import * as React from 'react';
import {observer} from 'mobx-react-lite';

import {
  ICON_STAR,
  ICON_STAR_FILLED,
  ICON_KEY,
  ICON_MAGNIFY_PLUS,
  ICON_MAGNIFY_MINUS,
  ICON_SHIELD, ICON_VIDEO,
} from '@mario-ai/shared';
import { eventUtils, menuUtils } from '@browser/core/utils/platform-lite';
import {parse} from 'url';
import store from '../../store';
import {ToolbarButton} from '../ToolbarButton';

const showAddBookmarkDialog = async () => {
  const star = document.getElementById('star');
  if (star) {
    const rect = star.getBoundingClientRect();
    // ✅ 重构: 确保DOM坐标值被正确序列化，避免IPC序列化错误 (修复序列化问题)
    const right = Number(rect.right);
    const bottom = Number(rect.bottom);
    console.log('[SiteButtons] Window ID:', store.windowId);
    console.log('[SiteButtons] Invoking bookmark dialog event:', `show-add-bookmark-dialog-${store.windowId}`, right, bottom);
    try {
      const result = await eventUtils.invoke(`show-add-bookmark-dialog-${store.windowId}`, right, bottom);
      console.log('[SiteButtons] Bookmark dialog result:', result);
    } catch (error) {
      console.error('[SiteButtons] Bookmark dialog error:', error);
    }
  } else {
    console.warn('[SiteButtons] Star element not found');
  }
};

const showZoomDialog = async () => {
  if (store.zoomFactor != 1) {
    const zoom = document.getElementById('zoom');
    if (zoom) {
      const rect = zoom.getBoundingClientRect();
      // ✅ 重构: 确保DOM坐标值被正确序列化，避免IPC序列化错误 (修复序列化问题)
      const right = Number(rect.right);
      const bottom = Number(rect.bottom);
      eventUtils.send(`show-zoom-dialog-${store.windowId}`, right, bottom);
    }
  }
};

const onStarClick = (e?: React.MouseEvent<HTMLDivElement>) => {
  if (e) {
    e.preventDefault();
    e.stopPropagation();
  }
  showAddBookmarkDialog();
};
const onBlockClick = (e?: React.MouseEvent<HTMLDivElement>) => {
  if (e) {
    e.preventDefault();
    e.stopPropagation();
  }
  const btn = document.getElementById('blocker-btn');
  if (btn) {
    const rect = btn.getBoundingClientRect();
    // ✅ 重构: 确保DOM坐标值被正确序列化，避免IPC序列化错误 (修复序列化问题)
    const right = Number(rect.right);
    const bottom = Number(rect.bottom);
    const {selectedTab} = store.tabs;
    let blockedAds = [];
    let count = 0;
    if (selectedTab) {
      blockedAds = selectedTab.blockedAds;
      count = selectedTab.filtersCount;
    }
    if (blockedAds && blockedAds.length) {
      console.log(blockedAds[0].request, blockedAds[0].result);
    }
    console.log('[SiteButtons] Sending block dialog event:', `show-block-dialog-${store.windowId}`, right, bottom);
    eventUtils.send(`show-block-dialog-${store.windowId}`, right, bottom, {
      data: blockedAds.map(it => ({
        url: it.request,
        filter: it.result
      })), count
    });
  } else {
    console.warn('[SiteButtons] Blocker button element not found');
  }
};
const onVideoContextClick = (e?: React.MouseEvent<HTMLDivElement>) => {
  if (e) {
    e.preventDefault();
    e.stopPropagation();
  }
  menuUtils.showContextMenu([
    {
      label: '全屏播放',
      click: () => {
        const {selectedTab} = store.tabs;
        if (selectedTab) {
          eventUtils.send(`show-full-video-dialog-${selectedTab.id}`);
        }
      },
    },
    {
      label: '悬浮播放',
      click: () => {
        const {selectedTab} = store.tabs;
        if (selectedTab) {
          eventUtils.send(`show-float-video-dialog-${selectedTab.id}`);
        }
      },
    },
  ]);
};
const onVideoClick = (e?: React.MouseEvent<HTMLDivElement>) => {
  if (e) {
    e.preventDefault();
    e.stopPropagation();
  }
  const star = document.getElementById('video-btn');
  if (star) {
    const rect = star.getBoundingClientRect();
    // ✅ 重构: 确保DOM坐标值被正确序列化，避免IPC序列化错误 (修复序列化问题)
    const right = Number(rect.right);
    const bottom = Number(rect.bottom);
    eventUtils.send(`show-video-dialog-${store.windowId}`, right, bottom);
  }
};

const onZoomClick = (e?: React.MouseEvent<HTMLDivElement>) => {
  if (e) {
    e.preventDefault();
    e.stopPropagation();
  }
  showZoomDialog();
};

const onKeyClick = () => {
  eventUtils.send(`check-form-autofill-${store.windowId}`);
};

eventUtils.on('show-add-bookmark-dialog', () => {
  showAddBookmarkDialog();
});

eventUtils.on('show-zoom-dialog', () => {
  showZoomDialog();
});

eventUtils.on('zoom-factor-updated', (e, zoomFactor, showDialog) => {
  store.zoomFactor = zoomFactor;
  if (!store.dialogsVisibility['zoom'] && showDialog) {
    showZoomDialog();
  }
});

const onShieldContextMenu = (e?: React.MouseEvent<HTMLDivElement>) => {
  if (e) {
    e.preventDefault();
    e.stopPropagation();
  }
  menuUtils.showContextMenu([
    {
      label: '启用广告拦截',
      type: 'checkbox',
      click: () => {
        store.settings.object.shield = !store.settings.object.shield;
        store.settings.save();
      },
    },
  ]);
};

export const SiteButtons = observer(() => {
  const {selectedTab} = store.tabs;

  let hasCredentials = false;
  let blockedAds = [];
  let videoUrls = [];

  if (selectedTab) {
    hasCredentials = selectedTab.hasCredentials;
    blockedAds = selectedTab.blockedAds;
    videoUrls = selectedTab.videoUrls;
  }

  const dense = !store.isCompact;

  return (
    <>
      {process.env.ENABLE_AUTOFILL && hasCredentials && (
        <ToolbarButton
          dense={dense}
          icon={ICON_KEY}
          size={16}
          onClick={onKeyClick}
          tooltip="自动填充"
        />
      )}
      {(store.dialogsVisibility['zoom'] || store.zoomFactor !== 1) && (
        <ToolbarButton
          id="zoom"
          toggled={store.dialogsVisibility['zoom']}
          icon={store.zoomFactor >= 1 ? ICON_MAGNIFY_PLUS : ICON_MAGNIFY_MINUS}
          size={18}
          dense={dense}
          onClick={onZoomClick}
          tooltip={`缩放 (${Math.round(store.zoomFactor * 100)}%)`}
        />
      )}
      <ToolbarButton
        id="star"
        toggled={store.dialogsVisibility['add-bookmark']}
        icon={store.isBookmarked ? ICON_STAR_FILLED : ICON_STAR}
        size={18}
        dense={dense}
        onClick={onStarClick}
        tooltip={store.isBookmarked ? "已收藏" : "收藏"}
      />
      <ToolbarButton
        id="blocker-btn"
        size={16}
        dense={dense}
        divRef={(ref) => (store.adblockBtnRef = ref)}
        badge={store.settings.object.shield && blockedAds.length > 0}
        badgeText={blockedAds.length.toString()}
        icon={ICON_SHIELD}
        opacity={store.settings.object.shield ? 0.87 : 0.54}
        onContextMenu={onShieldContextMenu}
        onClick={onBlockClick}
        tooltip={store.settings.object.shield ? `广告拦截 (已拦截 ${blockedAds.length} 个)` : "广告拦截 (已禁用)"}
      />
      {videoUrls.length > 0 && (
        <ToolbarButton
          id="video-btn"
          size={16}
          dense={dense}
          badge={videoUrls.length > 0}
          badgeText={videoUrls.length.toString()}
          icon={ICON_VIDEO}
          opacity={0.87}
          onClick={onVideoClick}
          onContextMenu={onVideoContextClick}
          style={{ marginLeft: '2px', marginRight: '2px' }} // 减少左右间距
          tooltip={`视频 (${videoUrls.length} 个)`}
        />
      )}
    </>
  );
});
