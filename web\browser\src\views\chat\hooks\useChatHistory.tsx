import React, { createContext, useContext, useState, useCallback } from 'react';

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  thinking?: string;
  isStreaming?: boolean;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  timestamp: Date;
  lastUpdated: Date;
}

export interface ChatGroup {
  title: string;
  chats: ChatSession[];
}

interface ChatHistoryContextType {
  // Current session management
  currentChatId: string;
  currentMessages: Message[];
  setCurrentMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  updateMessage: (id: string, updates: Partial<Message>) => void;

  // Session management
  chatSessions: ChatSession[];
  createNewChat: () => string;
  loadChatSession: (sessionId: string) => void;
  updateCurrentChatTitle: (title: string) => void;
  deleteChatSession: (sessionId: string) => void;

  // History organization
  getChatGroups: () => ChatGroup[];
  searchChats: (query: string) => ChatSession[];

  // Loading state
  isLoading: boolean;
}

const ChatHistoryContext = createContext<ChatHistoryContextType | undefined>(undefined);

export const useChatHistory = () => {
  const context = useContext(ChatHistoryContext);
  if (!context) {
    throw new Error('useChatHistory must be used within a ChatHistoryProvider');
  }
  return context;
};

interface ChatHistoryProviderProps {
  children: React.ReactNode;
}

export const ChatHistoryProvider: React.FC<ChatHistoryProviderProps> = ({ children }) => {
  const [currentChatId, setCurrentChatId] = useState<string>(() => `chat-${Date.now()}`);
  const [currentMessages, setCurrentMessages] = useState<Message[]>([]);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addMessage = useCallback((message: Message) => {
    setCurrentMessages(prev => [...prev, message]);
  }, []);

  const updateMessage = useCallback((id: string, updates: Partial<Message>) => {
    setCurrentMessages(prev =>
      prev.map(msg =>
        msg.id === id ? { ...msg, ...updates } : msg
      )
    );
  }, []);

  const createNewChat = useCallback(() => {
    const newChatId = `chat-${Date.now()}`;
    setCurrentChatId(newChatId);
    setCurrentMessages([]);
    return newChatId;
  }, []);

  const loadChatSession = useCallback((sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (session) {
      setCurrentChatId(sessionId);
      setCurrentMessages(session.messages);
    }
  }, [chatSessions]);

  const updateCurrentChatTitle = useCallback((title: string) => {
    setChatSessions(prev =>
      prev.map(session =>
        session.id === currentChatId
          ? { ...session, title, lastUpdated: new Date() }
          : session
      )
    );
  }, [currentChatId]);

  const deleteChatSession = useCallback((sessionId: string) => {
    setChatSessions(prev => prev.filter(s => s.id !== sessionId));
    if (sessionId === currentChatId) {
      createNewChat();
    }
  }, [currentChatId, createNewChat]);

  const getChatGroups = useCallback((): ChatGroup[] => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const groups: ChatGroup[] = [
      { title: '今天', chats: [] },
      { title: '昨天', chats: [] },
      { title: '本周', chats: [] },
      { title: '本月', chats: [] },
      { title: '更早', chats: [] },
    ];

    // Add current session if it has messages
    if (currentMessages.length > 0) {
      const currentSession: ChatSession = {
        id: currentChatId,
        title: currentMessages[0]?.content.substring(0, 30) + '...' || '新对话',
        messages: currentMessages,
        timestamp: new Date(),
        lastUpdated: new Date()
      };
      groups[0].chats.push(currentSession);
    }

    chatSessions.forEach(session => {
      const sessionDate = new Date(session.lastUpdated);
      if (sessionDate >= today) {
        groups[0].chats.push(session);
      } else if (sessionDate >= yesterday) {
        groups[1].chats.push(session);
      } else if (sessionDate >= weekAgo) {
        groups[2].chats.push(session);
      } else if (sessionDate >= monthAgo) {
        groups[3].chats.push(session);
      } else {
        groups[4].chats.push(session);
      }
    });

    return groups.filter(group => group.chats.length > 0);
  }, [chatSessions, currentMessages, currentChatId]);

  const searchChats = useCallback((query: string): ChatSession[] => {
    return chatSessions.filter(session =>
      session.title.toLowerCase().includes(query.toLowerCase()) ||
      session.messages.some(msg =>
        msg.content.toLowerCase().includes(query.toLowerCase())
      )
    );
  }, [chatSessions]);

  const value: ChatHistoryContextType = {
    currentChatId,
    currentMessages,
    setCurrentMessages,
    addMessage,
    updateMessage,
    chatSessions,
    createNewChat,
    loadChatSession,
    updateCurrentChatTitle,
    deleteChatSession,
    getChatGroups,
    searchChats,
    isLoading,
  };

  return (
    <ChatHistoryContext.Provider value={value}>
      {children}
    </ChatHistoryContext.Provider>
  );
};
