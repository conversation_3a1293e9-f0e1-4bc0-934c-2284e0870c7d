import { daysList, monthsList } from '@mario-ai/shared';

/**
 * 比较两个日期是否为同一天
 */
export const compareDates = (first: Date, second: Date) => {
  return (
    first != null &&
    second != null &&
    first.getFullYear() === second.getFullYear() &&
    first.getMonth() === second.getMonth() &&
    first.getDate() === second.getDate()
  );
};

/**
 * 获取日期的显示标签（支持国际化）
 */
export const getSectionLabel = (date: Date, locale: 'en' | 'zh' = 'zh') => {
  let prefix = '';
  const current = new Date();

  if (
    date.getFullYear() === current.getFullYear() &&
    date.getMonth() === current.getMonth()
  ) {
    if (current.getDate() === date.getDate()) {
      prefix = locale === 'zh' ? '今天 - ' : 'Today - ';
    } else if (current.getDate() - 1 === date.getDate()) {
      prefix = locale === 'zh' ? '昨天 - ' : 'Yesterday - ';
    }
  }

  return `${prefix}${daysList[date.getDay()]}, ${
    monthsList[date.getMonth()]
  } ${date.getDate()}, ${date.getFullYear()}`;
};

/**
 * 格式化时间为 HH:MM 格式
 */
export const formatTime = (date: Date) => {
  return `${date
    .getHours()
    .toString()
    .padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};
