import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { hashCode } from '@mario-ai/shared';
import { toJS } from 'mobx';

export class IpcEvent {
  private scope: string;
  private name: string;
  private callbacks: Function[] = [];
  private listener = false;

  constructor(scope: string, name: string) {
    this.name = name;
    this.scope = scope;

    this.emit = this.emit.bind(this);
  }

  public emit = (e: any, ...args: any[]) => {
    this.callbacks.forEach((callback) => {
      callback(...args);
    });
  };

  public addListener(callback: Function) {
    this.callbacks.push(callback);

    const id = hashCode(callback.toString());
    // 序列化MobX对象，确保IPC通信安全
    const serializedData = toJS({
      scope: this.scope,
      name: this.name,
      id,
    });
    ipcRenderer.send(`api-addListener`, serializedData);

    if (!this.listener) {
      ipcRenderer.on(`api-emit-event-${this.scope}-${this.name}`, this.emit);
      this.listener = true;
    }
  }

  public removeListener(callback: Function) {
    this.callbacks = this.callbacks.filter((x) => x !== callback);

    const id = hashCode(callback.toString());
    // 序列化MobX对象，确保IPC通信安全
    const serializedData = toJS({
      scope: this.scope,
      name: this.name,
      id,
    });
    ipcRenderer.send(`api-removeListener`, serializedData);

    if (this.callbacks.length === 0) {
      ipcRenderer.removeListener(
        `api-emit-event-${this.scope}-${this.name}`,
        this.emit,
      );
      this.listener = false;
    }
  }
}
