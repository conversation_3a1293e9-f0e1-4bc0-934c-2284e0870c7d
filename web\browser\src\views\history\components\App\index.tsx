import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store, { QuickRange } from '../../store';
import { NavigationDrawer } from '@browser/core/components/NavigationDrawer';
import { SelectionDialog } from '@browser/core/components/SelectionDialog';
import { HistorySection } from '../HistorySection';
import { Container, Content, LeftContent } from '@browser/core/components/Pages';
import { GlobalNavigationDrawer } from '@browser/core/components/GlobalNavigationDrawer';
import {
  ICON_HISTORY,
  ICON_ALL,
  ICON_TODAY,
  ICON_WEEK,
  ICON_CALENDAR,
  ICON_TRASH,
} from '@mario-ai/shared';
import { WebUIStyle } from '@browser/core/styles/default-styles';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';

const onScroll = (e: any) => {
  const scrollPos = e.target.scrollTop;
  const scrollMax = e.target.scrollHeight - e.target.clientHeight - 256;

  if (scrollPos >= scrollMax) {
    store.itemsLoaded += store.getDefaultLoaded();
  }
};

const RangeItem = observer(
  ({
    range,
    children,
    icon,
  }: {
    range: QuickRange;
    children: any;
    icon: string;
  }) => (
    <NavigationDrawer.Item
      onClick={() => (store.selectedRange = range)}
      selected={store.selectedRange === range}
      icon={icon}
    >
      {children}
    </NavigationDrawer.Item>
  ),
);

const onCancelClick = (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
  store.selectedItems = [];
};

const onDeleteClick = (e: React.MouseEvent<HTMLDivElement>) => {
  e.stopPropagation();
  store.deleteSelected();
};

const HistorySections = observer(() => {
  return (
    <LeftContent style={{ margin: '32px 64px' }}>
      <SelectionDialog
        theme={store.theme}
        visible={store.selectedItems.length > 0}
        amount={store.selectedItems.length}
        onDeleteClick={onDeleteClick}
        onCancelClick={onCancelClick}
      />
      {store.sections.map((data) => (
        <HistorySection data={data} key={data.date.getTime()} />
      ))}
    </LeftContent>
  );
});

const onInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  store.search(e.currentTarget.value);
};

const onClearClick = () => {
  store.clear();

  // TODO: eventUtils.send('clear-browsing-data');
};

export default observer(() => {
  // 初始化主题
  React.useEffect(() => {
    console.log('[HistoryApp] Initializing theme');
    TailwindThemeManager.setThemeWithAuto(store.settings.theme, store.settings.themeAuto);

    // 调试：检查data-theme属性
    setTimeout(() => {
      const dataTheme = document.documentElement.getAttribute('data-theme');
      console.log('[HistoryApp] Current data-theme attribute:', dataTheme);

      // 检查CSS变量是否存在
      const computedStyle = getComputedStyle(document.documentElement);
      const pageBg = computedStyle.getPropertyValue('--mario-page-bg');
      const pageText = computedStyle.getPropertyValue('--mario-page-text');
      console.log('[HistoryApp] CSS variables:', { pageBg, pageText });
    }, 100);
  }, []);

  // 监听主题变化
  React.useEffect(() => {
    console.log('[HistoryApp] Theme changed:', store.settings.theme);
    TailwindThemeManager.setThemeWithAuto(store.settings.theme, store.settings.themeAuto);

    // 调试：检查主题变化后的状态
    setTimeout(() => {
      const dataTheme = document.documentElement.getAttribute('data-theme');
      console.log('[HistoryApp] After theme change, data-theme:', dataTheme);
    }, 100);
  }, [store.settings.theme, store.settings.themeAuto]);

  return (
    <Container style={{
      background: 'var(--mario-page-bg)',
      color: 'var(--mario-page-text)',
      minHeight: '100vh'
    }}>
      <WebUIStyle />

      {/* 调试：测试前卫主题是否生效 */}
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        padding: '10px',
        background: 'var(--mario-page-bg)',
        color: 'var(--mario-page-text)',
        border: '1px solid var(--mario-page-text)',
        borderRadius: '5px',
        zIndex: 9999,
        fontSize: '12px'
      }}>
        主题测试：{document.documentElement.getAttribute('data-theme')}
      </div>

      <GlobalNavigationDrawer></GlobalNavigationDrawer>
      <NavigationDrawer title="历史" search onSearchInput={onInput}>
        <RangeItem icon={ICON_ALL} range="all">
          全部
        </RangeItem>
        <RangeItem icon={ICON_TODAY} range="today">
          今天
        </RangeItem>
        <RangeItem icon={ICON_HISTORY} range="yesterday">
          昨天
        </RangeItem>
        <RangeItem icon={ICON_WEEK} range="last-week">
          上周
        </RangeItem>
        <RangeItem icon={ICON_CALENDAR} range="older">
          更久以前
        </RangeItem>
        <div style={{ flex: 1 }} />
        <NavigationDrawer.Item icon={ICON_TRASH} onClick={onClearClick}>
          清除浏览记录
        </NavigationDrawer.Item>
      </NavigationDrawer>
      <Content onScroll={onScroll}>
        <HistorySections />
      </Content>
    </Container>
  );
});
