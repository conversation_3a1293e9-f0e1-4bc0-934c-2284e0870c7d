import { eventUtils } from '@browser/core/utils/platform-lite';
import { observable, computed, makeObservable } from 'mobx';
// 使用浏览器原生URL API替代Node.js的url模块
import { BROWSER_WEBUI_BASE_URL, BROWSER_WEBUI_PROTOCOL } from '@mario-ai/shared';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  private timeout: any;
  private timeout1: any;

  // Observable

  public title = '';

  public url = '';

  public x = 0;

  public xTransition = false;

  // Computed

  public get domain() {
    try {
      // 使用浏览器原生URL API
      const parsed = new URL(this.url);
      if (
        BROWSER_WEBUI_BASE_URL.startsWith(BROWSER_WEBUI_PROTOCOL) &&
        this.url.startsWith(BROWSER_WEBUI_BASE_URL)
      ) {
        return parsed.protocol + '//' + parsed.hostname;
      }

      if (parsed.protocol === 'file:') {
        return 'local or shared file';
      }

      return parsed.hostname;
    } catch (error) {
      // 如果URL无效，返回原始URL或默认值
      return this.url || 'unknown';
    }
  }

  constructor() {
    super({ visibilityWrapper: false, persistent: true });

    makeObservable(this, {
      title: observable,
      url: observable,
      x: observable,
      xTransition: observable,
      domain: computed,
    });

    eventUtils.on('visible', (e, visible, tab) => {
      clearTimeout(this.timeout);
      clearTimeout(this.timeout1);

      if (!visible) {
        this.visible = false;
      }

      if (visible) {
        this.timeout1 = setTimeout(() => {
          this.xTransition = true;
        }, 80);
      } else if (!visible) {
        this.timeout = setTimeout(() => {
          this.xTransition = false;
        }, 100);
      }

      if (tab) {
        this.title = tab.title;
        this.url = tab.url;
        this.x = tab.x;

        if (visible && this.title !== '' && this.url !== '') {
          this.visible = visible;
        }
      }
    });
  }
}

export default new Store();