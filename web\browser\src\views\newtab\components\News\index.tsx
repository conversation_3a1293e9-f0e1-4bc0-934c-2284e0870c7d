import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../store';
import { NewsItem } from '../NewsItem';
import { cn } from '@browser/utils/tailwind-helpers';

export const News = observer(() => {
  // StyledNews 样式 - Tailwind 版本
  const newsClasses = cn(
    'grid grid-flow-dense justify-center',
    'grid-cols-[repeat(auto-fit,300px)] auto-rows-[270px]',
    'gap-4 mt-8 mb-8'
  );

  return (
    <div className={newsClasses}>
      {store.news.map((item, key) => {
        if (!item.urlToImage) return null;

        return <NewsItem item={item} key={key}></NewsItem>;
      })}
    </div>
  );
});
