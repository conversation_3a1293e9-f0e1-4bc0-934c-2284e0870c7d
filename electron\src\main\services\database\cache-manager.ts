import { IBookmark, IFavicon, ISettings } from '@electron/types';

/**
 * LibSQL缓存管理器
 * 提供内存缓存功能，提升数据访问性能
 */
export class CacheManager {
  private bookmarksCache: Map<string, IBookmark> = new Map();
  private faviconsCache: Map<string, string> = new Map();
  private bookmarksList: IBookmark[] = [];
  private settingsCache: ISettings | null = null;
  private cacheEnabled: boolean = true;
  private lastBookmarksUpdate: number = 0;
  private lastFaviconsUpdate: number = 0;
  private lastSettingsUpdate: number = 0;

  constructor() {
    console.log('[CacheManager] 初始化缓存管理器');
  }

  // 书签缓存管理
  public setBookmarksCache(bookmarks: IBookmark[]): void {
    if (!this.cacheEnabled) return;

    this.bookmarksCache.clear();
    this.bookmarksList = [...bookmarks];
    
    for (const bookmark of bookmarks) {
      this.bookmarksCache.set(bookmark._id, bookmark);
    }
    
    this.lastBookmarksUpdate = Date.now();
    console.log(`[CacheManager] 书签缓存已更新，共${bookmarks.length}条记录`);
  }

  public getBookmarksCache(): IBookmark[] {
    if (!this.cacheEnabled) return [];
    return [...this.bookmarksList];
  }

  public getBookmarkById(id: string): IBookmark | undefined {
    if (!this.cacheEnabled) return undefined;
    return this.bookmarksCache.get(id);
  }

  public addBookmarkToCache(bookmark: IBookmark): void {
    if (!this.cacheEnabled) return;

    this.bookmarksCache.set(bookmark._id, bookmark);
    this.bookmarksList.push(bookmark);
    this.lastBookmarksUpdate = Date.now();
    
    console.log(`[CacheManager] 书签已添加到缓存: ${bookmark.title}`);
  }

  public updateBookmarkInCache(id: string, updates: Partial<IBookmark>): void {
    if (!this.cacheEnabled) return;

    const existingBookmark = this.bookmarksCache.get(id);
    if (existingBookmark) {
      const updatedBookmark = { ...existingBookmark, ...updates };
      this.bookmarksCache.set(id, updatedBookmark);
      
      // 更新列表中的书签
      const index = this.bookmarksList.findIndex(b => b._id === id);
      if (index !== -1) {
        this.bookmarksList[index] = updatedBookmark;
      }
      
      this.lastBookmarksUpdate = Date.now();
      console.log(`[CacheManager] 书签缓存已更新: ${id}`);
    }
  }

  public removeBookmarkFromCache(id: string): void {
    if (!this.cacheEnabled) return;

    this.bookmarksCache.delete(id);
    this.bookmarksList = this.bookmarksList.filter(b => b._id !== id);
    this.lastBookmarksUpdate = Date.now();
    
    console.log(`[CacheManager] 书签已从缓存移除: ${id}`);
  }

  // 网站图标缓存管理
  public setFaviconsCache(favicons: Map<string, string>): void {
    if (!this.cacheEnabled) return;

    this.faviconsCache = new Map(favicons);
    this.lastFaviconsUpdate = Date.now();
    
    console.log(`[CacheManager] 网站图标缓存已更新，共${favicons.size}个图标`);
  }

  public getFaviconsCache(): Map<string, string> {
    if (!this.cacheEnabled) return new Map();
    return new Map(this.faviconsCache);
  }

  public getFaviconByUrl(url: string): string | undefined {
    if (!this.cacheEnabled) return undefined;
    return this.faviconsCache.get(url);
  }

  public addFaviconToCache(url: string, data: string): void {
    if (!this.cacheEnabled) return;

    this.faviconsCache.set(url, data);
    this.lastFaviconsUpdate = Date.now();
    
    console.log(`[CacheManager] 网站图标已添加到缓存: ${url}`);
  }

  public removeFaviconFromCache(url: string): void {
    if (!this.cacheEnabled) return;

    this.faviconsCache.delete(url);
    this.lastFaviconsUpdate = Date.now();

    console.log(`[CacheManager] 网站图标已从缓存移除: ${url}`);
  }

  // 设置缓存管理
  public setSettingsCache(settings: ISettings): void {
    if (!this.cacheEnabled) return;

    this.settingsCache = { ...settings };
    this.lastSettingsUpdate = Date.now();

    console.log('[CacheManager] 设置缓存已更新');
  }

  public getSettingsCache(): ISettings | null {
    if (!this.cacheEnabled) return null;
    return this.settingsCache ? { ...this.settingsCache } : null;
  }

  public updateSettingInCache(key: string, value: any): void {
    if (!this.cacheEnabled || !this.settingsCache) return;

    (this.settingsCache as any)[key] = value;
    this.lastSettingsUpdate = Date.now();

    console.log(`[CacheManager] 设置项已更新: ${key}`);
  }

  public clearSettingsCache(): void {
    this.settingsCache = null;
    this.lastSettingsUpdate = 0;
    console.log('[CacheManager] 设置缓存已清空');
  }

  // 缓存查询方法
  public findBookmarksInCache(query: any): IBookmark[] {
    if (!this.cacheEnabled) return [];

    let results = this.bookmarksList;

    // 简单的查询过滤
    if (query._id) {
      const bookmark = this.bookmarksCache.get(query._id);
      return bookmark ? [bookmark] : [];
    }

    if (query.title) {
      results = results.filter(b => 
        b.title && b.title.toLowerCase().includes(query.title.toLowerCase())
      );
    }

    if (query.url) {
      results = results.filter(b => 
        b.url && b.url.toLowerCase().includes(query.url.toLowerCase())
      );
    }

    if (query.isFolder !== undefined) {
      results = results.filter(b => b.isFolder === query.isFolder);
    }

    if (query.parent !== undefined) {
      results = results.filter(b => b.parent === query.parent);
    }

    if (query.static !== undefined) {
      results = results.filter(b => b.static === query.static);
    }

    return results;
  }

  // 缓存统计和管理
  public getCacheStats(): {
    bookmarksCount: number;
    faviconsCount: number;
    settingsLoaded: boolean;
    lastBookmarksUpdate: number;
    lastFaviconsUpdate: number;
    lastSettingsUpdate: number;
    enabled: boolean;
  } {
    return {
      bookmarksCount: this.bookmarksCache.size,
      faviconsCount: this.faviconsCache.size,
      settingsLoaded: this.settingsCache !== null,
      lastBookmarksUpdate: this.lastBookmarksUpdate,
      lastFaviconsUpdate: this.lastFaviconsUpdate,
      lastSettingsUpdate: this.lastSettingsUpdate,
      enabled: this.cacheEnabled
    };
  }

  public clearCache(): void {
    this.bookmarksCache.clear();
    this.faviconsCache.clear();
    this.bookmarksList = [];
    this.settingsCache = null;
    this.lastBookmarksUpdate = 0;
    this.lastFaviconsUpdate = 0;
    this.lastSettingsUpdate = 0;

    console.log('[CacheManager] 所有缓存已清空');
  }

  /**
   * 清除浏览数据相关的缓存，但保留书签缓存
   */
  public clearBrowsingDataCache(): void {
    this.faviconsCache.clear();
    this.lastFaviconsUpdate = 0;
    console.log('[CacheManager] 浏览数据缓存已清除（保留书签）');
  }

  public enableCache(): void {
    this.cacheEnabled = true;
    console.log('[CacheManager] 缓存已启用');
  }

  public disableCache(): void {
    this.cacheEnabled = false;
    this.clearCache();
    console.log('[CacheManager] 缓存已禁用');
  }

  public isCacheEnabled(): boolean {
    return this.cacheEnabled;
  }

  // 缓存预热
  public async warmupCache(
    loadBookmarks: () => Promise<IBookmark[]>,
    loadFavicons: () => Promise<Map<string, string>>
  ): Promise<void> {
    if (!this.cacheEnabled) return;

    console.log('[CacheManager] 开始缓存预热...');
    
    try {
      // 预热书签缓存
      const bookmarks = await loadBookmarks();
      this.setBookmarksCache(bookmarks);
      
      // 预热图标缓存
      const favicons = await loadFavicons();
      this.setFaviconsCache(favicons);
      
      console.log('[CacheManager] 缓存预热完成');
    } catch (error) {
      console.error('[CacheManager] 缓存预热失败:', error);
      throw error;
    }
  }

  // 缓存失效检查
  public isCacheStale(maxAge: number = 5 * 60 * 1000): boolean { // 默认5分钟
    const now = Date.now();
    const bookmarksStale = (now - this.lastBookmarksUpdate) > maxAge;
    const faviconsStale = (now - this.lastFaviconsUpdate) > maxAge;
    
    return bookmarksStale || faviconsStale;
  }

  // 智能缓存刷新
  public async refreshCacheIfNeeded(
    loadBookmarks: () => Promise<IBookmark[]>,
    loadFavicons: () => Promise<Map<string, string>>,
    maxAge: number = 5 * 60 * 1000
  ): Promise<void> {
    if (!this.cacheEnabled || !this.isCacheStale(maxAge)) return;

    console.log('[CacheManager] 缓存已过期，开始刷新...');
    await this.warmupCache(loadBookmarks, loadFavicons);
  }
}
