import * as React from 'react';
import { observer } from 'mobx-react-lite';

import store from '../../../store';
import { IFormFillData } from '@mario-ai/shared';
import { Section, onMoreClick } from '../Section';
import { ICON_LOCATION, ICON_MORE } from '@mario-ai/shared';
import { cn } from '@browser/utils/tailwind-helpers';

const Item = observer(({ data }: { data: IFormFillData }) => {
  // StyledItem 样式 - Tailwind 版本
  const itemClasses = cn(
    'w-full h-12 flex items-center px-4 cursor-pointer',
    'hover:bg-black/4'
  );

  // More 样式 - Tailwind 版本
  const moreClasses = cn(
    'w-6 h-6 bg-center bg-no-repeat bg-contain opacity-54 ml-auto',
    'hover:opacity-80 transition-opacity duration-200'
  );

  return (
    <div className={itemClasses}>
      {data.fields.address}
      <div
        className={moreClasses}
        style={{ backgroundImage: `url(${ICON_MORE})` }}
        onClick={onMoreClick(data)}
      />
    </div>
  );
});

export const Addresses = observer(() => {
  const style = {
    flexDirection: 'column',
    padding: '0px 16px 8px 16px',
  };

  return (
    <Section label="Addresses" icon={ICON_LOCATION} style={style}>
      {store.autoFill.addresses.map((item) => (
        <Item key={item._id} data={item} />
      ))}
    </Section>
  );
});
