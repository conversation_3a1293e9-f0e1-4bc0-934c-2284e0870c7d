import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  root: '.',
  build: {
    outDir: 'build',
    ssr: true,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/main/core/index.ts'),
        'view-preload': resolve(__dirname, 'src/preload/scripts/view-preload.ts'),
        'popup-preload': resolve(__dirname, 'src/preload/scripts/popup-preload.ts'),
        'dialog-preload': resolve(__dirname, 'src/preload/scripts/dialog-preload.ts'),
      },
      external: [
        'electron',
        'fs',
        'path',
        'os',
        'crypto',
        'stream',
        'util',
        'events',
        'url',
        'keytar',
        '@electron/remote',
        '@wexond/rpc-core',
        '@wexond/rpc-electron',
        '@cliqz/adblocker-electron',
        'electron-chrome-extensions',

        'node-fetch',
        'jszip',
        'icojs',
        'file-type',
        'node-bookmarks-parser',
        'pretty-bytes',
        'source-map-support',
        'cross-fetch',
        'react',
        'react-dom',
        'mobx',

        '@libsql/client',
        'drizzle-orm',
        'better-sqlite3'
      ],
      output: {
        entryFileNames: '[name].bundle.js',
        format: 'cjs',
      },
    },
    minify: process.env.NODE_ENV === 'production',
    sourcemap: process.env.NODE_ENV === 'development',
  },
  resolve: {
    alias: {
      '@electron': resolve(__dirname, 'src/'),
    },
  },
  define: {
    // 检查是否是开发模式（通过DEV环境变量或NODE_ENV）
    'process.env.NODE_ENV': JSON.stringify(
      process.env.DEV === '1' || process.env.NODE_ENV === 'development'
        ? 'development'
        : 'production'
    ),
    'process.env.ENABLE_EXTENSIONS': JSON.stringify(process.env.ENABLE_EXTENSIONS || 'true'),
    'process.env.ENABLE_AUTOFILL': JSON.stringify(process.env.ENABLE_AUTOFILL || 'true'),
    'process.env.VERSION_CODE': JSON.stringify('1.1.2'),
  },
})