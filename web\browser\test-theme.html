<!DOCTYPE html>
<html lang="zh-CN" data-theme="futuristic">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前卫主题测试</title>
    <style>
        /* 导入前卫主题CSS变量 */
        @import url('./src/styles/tailwind-theme.css');
        
        body {
            margin: 0;
            padding: 20px;
            font-family: system-ui, sans-serif;
            background: var(--mario-page-bg);
            color: var(--mario-page-text);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: var(--mario-nav-drawer1-bg);
            border-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-button {
            background: var(--mario-control-bg);
            color: var(--mario-control-value);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: var(--mario-control-hover-bg);
        }
        
        .css-var-display {
            font-family: monospace;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 前卫主题测试页面</h1>
        
        <div class="test-item">
            <h3>主题状态</h3>
            <p>当前data-theme: <span id="current-theme"></span></p>
            <p>页面背景应该显示渐变色</p>
            <p>文字应该是白色</p>
        </div>
        
        <div class="test-item">
            <h3>CSS变量测试</h3>
            <div class="css-var-display" id="css-vars"></div>
        </div>
        
        <div class="test-item">
            <h3>按钮测试</h3>
            <button class="test-button">测试按钮1</button>
            <button class="test-button">测试按钮2</button>
            <button class="test-button">测试按钮3</button>
        </div>
        
        <div class="test-item">
            <h3>主题切换测试</h3>
            <button class="test-button" onclick="setTheme('futuristic')">前卫主题</button>
            <button class="test-button" onclick="setTheme('dark')">深色主题</button>
            <button class="test-button" onclick="setTheme('light')">浅色主题</button>
        </div>
    </div>

    <script>
        function updateThemeInfo() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            document.getElementById('current-theme').textContent = currentTheme || 'none';
            
            const computedStyle = getComputedStyle(document.documentElement);
            const cssVars = {
                '--mario-page-bg': computedStyle.getPropertyValue('--mario-page-bg'),
                '--mario-page-text': computedStyle.getPropertyValue('--mario-page-text'),
                '--mario-control-bg': computedStyle.getPropertyValue('--mario-control-bg'),
                '--mario-nav-drawer1-bg': computedStyle.getPropertyValue('--mario-nav-drawer1-bg')
            };
            
            let cssVarText = '';
            for (const [key, value] of Object.entries(cssVars)) {
                cssVarText += `${key}: ${value || 'undefined'}\n`;
            }
            document.getElementById('css-vars').textContent = cssVarText;
        }
        
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            setTimeout(updateThemeInfo, 100);
        }
        
        // 初始化
        updateThemeInfo();
        
        // 每秒更新一次，用于调试
        setInterval(updateThemeInfo, 1000);
    </script>
</body>
</html>
