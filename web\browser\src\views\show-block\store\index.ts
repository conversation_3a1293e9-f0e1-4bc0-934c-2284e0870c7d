import { eventUtils } from '@browser/core/utils/platform-lite';
import {makeObservable, observable} from 'mobx';
import * as React from 'react';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public titleRef = React.createRef<HTMLInputElement>();

  public list: any[] = [];
  public adListUrls: String = "";
  public filterCount = 0;

  private initialized = false; // 防止重复初始化

  public constructor() {
    // 广告拦截对话框启用失焦隐藏，但使用延迟避免误触发
    super({ hideOnBlur: true });
    makeObservable(this, {
      list: observable,
      adListUrls: observable,
      filterCount: observable
    });

    // 防止重复初始化
    if (!this.initialized) {
      this.initialized = true;

      (async () => {
        this.adListUrls = await eventUtils.invoke('get-ad-lists');
        this.filterCount = await eventUtils.invoke('get-ad-filter-count');
      })();

      eventUtils.on('data', async (_, data) => {
        this.list = data.data;
        this.filterCount = data.count;
      });
    }

    // 添加广告拦截对话框特有的失焦隐藏功能
    // 使用更长的延迟，因为用户需要时间查看内容
    if (this.hideOnBlur) {
      this.setupBlurHiding();
    }
  }

  private setupBlurHiding() {
    // 防止重复设置失焦隐藏
    if ((this as any).__blurHidingSetup) {
      return;
    }
    (this as any).__blurHidingSetup = true;

    let blurTimer: NodeJS.Timeout | null = null;

    const handleBlur = () => {
      blurTimer = setTimeout(() => {
        console.log('[ShowBlockStore] Window blur detected, hiding dialog');
        this.hide();
      }, 300); // 减少延迟时间，提高响应性
    };

    const handleFocus = () => {
      if (blurTimer) {
        clearTimeout(blurTimer);
        blurTimer = null;
      }
    };

    window.addEventListener('blur', handleBlur);
    window.addEventListener('focus', handleFocus);

    // 清理函数
    const cleanup = () => {
      window.removeEventListener('blur', handleBlur);
      window.removeEventListener('focus', handleFocus);
      if (blurTimer) {
        clearTimeout(blurTimer);
      }
    };

    // 当对话框隐藏时清理事件监听器
    const originalHide = this.hide.bind(this);
    this.hide = () => {
      cleanup();
      originalHide();
    };
  }

}

export default new Store();
