import {observable, action, computed, makeObservable} from 'mobx';
import { eventUtils } from '@browser/core/utils/platform-lite';

import {ISettings} from '@browser/core/types';
import { LITE_DEFAULT_SETTINGS as DEFAULT_SETTINGS } from '@browser/core/utils/platform-lite';
import {Store} from '.';
import {updateStoreNewTab, updateStoreNewTabTemp} from '@browser/core/utils/webui';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';

export type SettingsSection =
  | 'appearance'
  | 'autofill'
  | 'address-bar'
  | 'privacy'
  | 'permissions'
  | 'startup'
  | 'language'
  | 'shortcuts'
  | 'downloads'
  | 'system';

export class SettingsStore {
  public selectedSection: SettingsSection = 'appearance';

  public object: ISettings = DEFAULT_SETTINGS;

  public store: Store;

  public constructor(store: Store) {
    makeObservable(this, {
      selectedSection: observable,
      object: observable,
      searchEngine: computed,
      updateSettings: action,
    });

    this.store = store;

    let firstTime = false;

    // 获取初始设置
    eventUtils.invoke('get-settings').then(settings => {
      if (settings) {
        this.updateSettings(settings);
        if (!firstTime) {
          store.startupTabs.load();
          firstTime = true;
        }
      }
    }).catch(error => {
      console.error('[Settings] 获取设置失败:', error);
    });

    eventUtils.on('update-settings', (e, settings: ISettings) => {
      this.updateSettings(settings);
      if (!firstTime) {
        store.startupTabs.load();
        firstTime = true;
      }
    });

    eventUtils.on('update-newtab-url', (e, newtab: string, url: string) => {
      updateStoreNewTab(newtab);
      updateStoreNewTabTemp(url);
      this.object = {...this.object, newtab: newtab};
    });
  }

  public get searchEngine() {
    return this.object.searchEngines[this.object.searchEngine];
  }

  public updateSettings(newSettings: ISettings) {
    //console.log("updateSettings", newSettings.newtab);
    const prevState = {...this.object};
    this.object = {...this.object, ...newSettings};

    if (newSettings.newtab != prevState.newtab) {
      updateStoreNewTab(newSettings.newtab);
    }

    // 处理主题变化
    if (prevState.theme !== newSettings.theme || prevState.themeAuto !== newSettings.themeAuto) {
      console.log('[SettingsStore] Theme changed from', prevState.theme, 'to', newSettings.theme, 'themeAuto:', newSettings.themeAuto);
      TailwindThemeManager.setThemeWithAuto(newSettings.theme, newSettings.themeAuto);
    }

    if (prevState.topBarVariant !== newSettings.topBarVariant) {
      requestAnimationFrame(() => {
        this.store.tabs.updateTabsBounds(true);
      });
    }
  }

  public async save() {
    try {
      // 使用新的配置管理系统API
      const result = await eventUtils.invoke('save-settings', this.object);
      if (!result.success) {
        console.error('[SettingsStore] 保存设置失败:', result.error);
      }
    } catch (error) {
      console.error('[SettingsStore] 保存设置异常:', error);
    }
  }
}
