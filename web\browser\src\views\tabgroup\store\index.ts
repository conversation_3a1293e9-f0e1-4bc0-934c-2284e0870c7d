import { eventUtils } from '@browser/core/utils/platform-lite';
import * as React from 'react';
import { Textfield } from '@browser/core/components/Textfield';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public inputRef = React.createRef<Textfield>();

  public tabGroupId: number;

  public constructor() {
    super();
    this.init();
  }

  public async init() {
    const tabGroup = await this.invoke('tabgroup');

    this.tabGroupId = tabGroup.id;
    this.inputRef.current.inputRef.current.focus();
    this.inputRef.current.inputRef.current.value = tabGroup.name;
    this.inputRef.current.inputRef.current.select();
  }
}

export default new Store();
