/**
 * 优化的主题管理器 - 解决主题切换延迟问题
 */

export class OptimizedThemeManager {
  private static instance: OptimizedThemeManager;
  private static currentTheme: string = 'mario-futuristic';
  private static isInitialized = false;
  private static pendingThemeChange: string | null = null;
  private static themeChangeTimeout: NodeJS.Timeout | null = null;

  // 预创建所有主题的style标签，避免动态创建
  private static themeStyles: Map<string, HTMLStyleElement> = new Map();

  /**
   * 获取单例实例
   */
  static getInstance(): OptimizedThemeManager {
    if (!this.instance) {
      this.instance = new OptimizedThemeManager();
    }
    return this.instance;
  }

  /**
   * 初始化主题系统 - 只执行一次
   */
  static initialize() {
    if (this.isInitialized) return;

    console.log('[OptimizedThemeManager] Initializing theme system...');
    
    // 预创建所有主题的style标签
    this.preCreateThemeStyles();
    
    // 设置CSS变量过渡动画
    this.setupCSSTransitions();
    
    this.isInitialized = true;
    console.log('[OptimizedThemeManager] Theme system initialized');
  }

  /**
   * 预创建所有主题的style标签
   */
  private static preCreateThemeStyles() {
    const themes = ['light', 'dark', 'futuristic'];
    
    themes.forEach(theme => {
      const style = document.createElement('style');
      style.id = `theme-${theme}-style`;
      style.disabled = true; // 默认禁用
      document.head.appendChild(style);
      this.themeStyles.set(theme, style);
    });

    // 设置前卫主题的样式内容
    const futuristicStyle = this.themeStyles.get('futuristic');
    if (futuristicStyle) {
      futuristicStyle.textContent = `
        [data-theme="futuristic"] body {
          background: var(--mario-page-bg) !important;
          color: var(--mario-page-text) !important;
        }
        [data-theme="futuristic"] .bg-mario-page {
          background: var(--mario-page-bg) !important;
        }
        [data-theme="futuristic"] .text-mario-page-text {
          color: var(--mario-page-text) !important;
        }
        [data-theme="futuristic"] #app {
          background: var(--mario-page-bg) !important;
          color: var(--mario-page-text) !important;
        }
      `;
    }
  }

  /**
   * 设置CSS变量过渡动画
   */
  private static setupCSSTransitions() {
    const transitionStyle = document.createElement('style');
    transitionStyle.id = 'theme-transitions';
    transitionStyle.textContent = `
      :root {
        transition: 
          background-color 0.15s ease-out,
          color 0.15s ease-out;
      }
      
      * {
        transition: 
          background-color 0.15s ease-out,
          color 0.15s ease-out,
          border-color 0.15s ease-out;
      }
      
      /* 优化渐变背景的过渡 */
      [style*="gradient"] {
        transition: opacity 0.15s ease-out;
      }
    `;
    document.head.appendChild(transitionStyle);
  }

  /**
   * 快速设置主题 - 防抖优化
   */
  static setTheme(themeName: string) {
    // 防抖：如果短时间内多次调用，只执行最后一次
    if (this.themeChangeTimeout) {
      clearTimeout(this.themeChangeTimeout);
    }

    this.pendingThemeChange = themeName;
    
    this.themeChangeTimeout = setTimeout(() => {
      this.applyThemeImmediate(this.pendingThemeChange!);
      this.pendingThemeChange = null;
      this.themeChangeTimeout = null;
    }, 16); // 一帧的时间，确保流畅
  }

  /**
   * 立即应用主题 - 优化版本
   */
  private static applyThemeImmediate(themeName: string) {
    if (!this.isInitialized) {
      this.initialize();
    }

    const root = document.documentElement;
    const normalizedTheme = this.normalizeThemeName(themeName);

    console.log('[OptimizedThemeManager] Applying theme:', normalizedTheme);

    // 使用requestAnimationFrame确保DOM更新的流畅性
    requestAnimationFrame(() => {
      // 1. 快速更新data-theme属性
      root.setAttribute('data-theme', normalizedTheme);
      
      // 2. 更新class（如果需要）
      root.classList.remove('dark');
      if (normalizedTheme === 'dark') {
        root.classList.add('dark');
      }

      // 3. 启用对应的style标签，禁用其他的
      this.themeStyles.forEach((style, theme) => {
        style.disabled = theme !== normalizedTheme;
      });

      // 4. 更新当前主题
      this.currentTheme = themeName;

      // 5. 触发主题变更事件（延迟触发，避免重复处理）
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('theme-changed', {
          detail: { theme: themeName }
        }));
      }, 0);
    });
  }

  /**
   * 标准化主题名称
   */
  private static normalizeThemeName(themeName: string): string {
    switch (themeName) {
      case 'wexond-dark':
        return 'dark';
      case 'mario-futuristic':
      case 'futuristic':
        return 'futuristic';
      case 'wexond-light':
      case 'auto':
      default:
        return 'light';
    }
  }

  /**
   * 设置主题（支持自动检测）
   */
  static setThemeWithAuto(themeName: string, isAuto: boolean = false) {
    if (isAuto || themeName === 'auto') {
      const systemTheme = this.detectSystemTheme();
      this.setTheme(systemTheme);
    } else {
      this.setTheme(themeName);
    }
  }

  /**
   * 检测系统主题偏好
   */
  private static detectSystemTheme(): string {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'wexond-dark';
    }
    return 'wexond-light';
  }

  /**
   * 获取当前主题
   */
  static getCurrentTheme(): string {
    return this.currentTheme;
  }

  /**
   * 检查是否为深色主题
   */
  static isDarkTheme(): boolean {
    const normalized = this.normalizeThemeName(this.currentTheme);
    return normalized === 'dark' || normalized === 'futuristic';
  }

  /**
   * 预加载主题资源（如果有的话）
   */
  static preloadThemeResources() {
    // 预加载可能的主题相关资源
    // 这里可以添加图标、字体等资源的预加载逻辑
  }

  /**
   * 清理资源
   */
  static cleanup() {
    if (this.themeChangeTimeout) {
      clearTimeout(this.themeChangeTimeout);
    }
    
    // 清理预创建的style标签
    this.themeStyles.forEach(style => {
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
    });
    this.themeStyles.clear();
    
    // 清理过渡样式
    const transitionStyle = document.getElementById('theme-transitions');
    if (transitionStyle) {
      transitionStyle.remove();
    }
    
    this.isInitialized = false;
  }
}
