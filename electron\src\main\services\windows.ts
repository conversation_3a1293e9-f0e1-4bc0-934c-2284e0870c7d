import { AppWindow } from '@electron/main/ui/windows/app';
import { BrowserWindow, ipcMain } from 'electron';

export class WindowsService {
  public list: AppWindow[] = [];

  public current: AppWindow;

  public lastFocused: AppWindow;

  constructor() {
    if (process.env.ENABLE_EXTENSIONS) {
      // extensions.tabs.on('activated', (tabId, windowId, focus) => {
      //   const win = this.list.find((x) => x.id === windowId);
      //   win.viewManager.select(tabId, focus === undefined ? true : focus);
      // });
      //
      // extensions.tabs.onCreateDetails = (tab, details) => {
      //   const win = this.findByBrowserView(tab.id);
      //   details.windowId = win.id;
      // };
      //
      // extensions.windows.onCreate = async (details) => {
      //   return this.open(details.incognito).id;
      // };
      //
      // extensions.tabs.onCreate = async (details) => {
      //   const win =
      //     this.list.find((x) => x.id === details.windowId) || this.lastFocused;
      //
      //   if (!win) return -1;
      //
      //   const view = win.viewManager.create(details, true, true);
      //   return view.id;
      // };
    }

    ipcMain.handle('get-tab-zoom', (e, tabId) => {
      return this.findByBrowserView(tabId).viewManager.views.get(tabId)
        .webContents.zoomFactor;
    });
  }

  public open(incognito = false) {
    const window = new AppWindow(incognito);
    this.list.push(window);

    // 立即设置为当前窗口，避免在SessionsService初始化时current为undefined
    this.current = window;

    if (process.env.ENABLE_EXTENSIONS) {
      //extensions.windows.observe(window.win);
    }

    window.win.on('focus', () => {
      this.lastFocused = window;
    });

    return window;
  }

  public findByBrowserView(webContentsId: number) {
    return this.list.find((x) => !!x.viewManager.views.get(webContentsId));
  }

  public fromBrowserWindow(browserWindow: BrowserWindow) {
    return this.list.find((x) => x.id === browserWindow.id);
  }

  public broadcast(channel: string, ...args: unknown[]) {
    // 序列化所有参数，确保IPC通信安全
    const serializedArgs = args.map(arg => {
      if (arg === null || arg === undefined) return arg;
      if (typeof arg === 'function') return '[Function]';
      if (typeof arg === 'symbol') return '[Symbol]';
      if (arg instanceof Error) return { message: (arg as Error).message, stack: (arg as Error).stack };

      // 对于对象，进行深度序列化
      if (typeof arg === 'object') {
        try {
          return JSON.parse(JSON.stringify(arg));
        } catch (error) {
          console.warn(`[Windows] Failed to serialize argument for broadcast ${channel}:`, arg, error);
          return '[Unserializable Object]';
        }
      }

      return arg;
    });

    this.list.forEach((appWindow) =>
      appWindow.win.webContents.send(channel, ...serializedArgs),
    );
  }
}
