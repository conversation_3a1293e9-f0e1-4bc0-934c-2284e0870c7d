import { makeObservable, observable } from 'mobx';
import { eventUtils } from '@browser/core/utils/platform-lite';

import { getDomain } from '@mario-ai/shared';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public permissions: string[] = [];

  public domain = '';

  public constructor() {
    super({ hideOnBlur: false });

    makeObservable(this, { permissions: observable, domain: observable });

    eventUtils.on('update-tab-info', (e, tabId, { url, name, details }) => {
      this.domain = getDomain(url);
      this.permissions = [];

      if (name === 'notifications' || name === 'geolocation') {
        this.permissions.push(name);
      } else if (name === 'media') {
        if (details.mediaTypes.includes('audio')) {
          this.permissions.push('microphone');
        }

        if (details.mediaTypes.includes('video')) {
          this.permissions.push('camera');
        }
      }
    });
  }
}

export default new Store();
