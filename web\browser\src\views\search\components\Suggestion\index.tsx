import { observer } from 'mobx-react-lite';
import * as React from 'react';

import { transparency, ICON_PAGE, ICON_SEARCH } from '@mario-ai/shared';
import { ISuggestion } from '@mario-ai/shared';
import store from '../../store';
import { viewUtils } from '@browser/core/utils/platform-lite';
import { cn } from '@browser/utils/tailwind-helpers';

interface Props {
  suggestion: ISuggestion;
}

const onMouseEnter = (suggestion: ISuggestion) => () => {
  suggestion.hovered = true;
};

const onMouseLeave = (suggestion: ISuggestion) => () => {
  suggestion.hovered = false;
};

const onClick = (suggestion: ISuggestion) => () => {
  let url = suggestion.isSearch ? suggestion.primaryText : suggestion.url;

  if (suggestion.isSearch) {
    url = store.searchEngine.url.replace('%s', url);
  } else if (url.indexOf('://') === -1) {
    url = `http://${url}`;
  }

  viewUtils.loadURL(store.tabId, url);

  store.hide();
};

export const Suggestion = observer(({ suggestion }: Props) => {
  const { hovered } = suggestion;
  const { primaryText, secondaryText, url } = suggestion;

  const selected = store.suggestions.selected === suggestion.id;

  let { favicon } = suggestion;

  if (favicon == null || favicon.trim() === '') {
    favicon = ICON_PAGE;
  }

  const customFavicon = favicon !== ICON_PAGE && favicon !== ICON_SEARCH;

  // StyledSuggestion 样式 - Tailwind 版本
  const suggestionClasses = cn(
    'w-full h-[38px] min-h-[38px] flex relative items-center overflow-hidden',
    // 背景色根据状态和主题
    selected
      ? store.theme['searchBox.lightForeground']
        ? 'bg-white/6'
        : 'bg-black/6'
      : hovered
      ? store.theme['searchBox.lightForeground']
        ? 'bg-white/3'
        : 'bg-black/3'
      : 'bg-transparent'
  );

  // Icon 样式 - Tailwind 版本
  const iconClasses = cn(
    'ml-[11px] w-4 min-w-4 h-4 mr-3 bg-center bg-no-repeat bg-contain'
  );

  // SuggestionText 基础样式
  const suggestionTextClasses = cn(
    'whitespace-nowrap overflow-hidden text-ellipsis text-sm'
  );

  // PrimaryText 样式 - Tailwind 版本
  const primaryTextClasses = cn(
    suggestionTextClasses,
    'opacity-87' // transparency.text.high
  );

  // RightText 样式 - Tailwind 版本
  const rightTextClasses = cn(
    suggestionTextClasses,
    'pr-4 flex-1'
  );

  // Url 样式 - Tailwind 版本
  const urlClasses = cn(
    rightTextClasses,
    store.theme['searchBox.lightForeground'] ? 'text-[#81C7F5]' : 'text-[#3297FD]' // BLUE_300 : '#3297FD'
  );

  // SecondaryText 样式 - Tailwind 版本
  const secondaryTextClasses = cn(
    rightTextClasses,
    'opacity-60' // transparency.text.medium
  );

  // Dash 样式 - Tailwind 版本
  const dashClasses = cn(
    'ml-1 mr-1 opacity-60' // transparency.text.medium
  );

  return (
    <div
      className={suggestionClasses}
      onClick={onClick(suggestion)}
      onMouseEnter={onMouseEnter(suggestion)}
      onMouseLeave={onMouseLeave(suggestion)}
    >
      <div
        className={iconClasses}
        style={{
          backgroundImage: `url(${favicon})`,
          opacity: customFavicon ? 1 : transparency.icons.inactive,
          filter: !customFavicon
            ? store.theme['searchBox.lightForeground']
              ? 'invert(100%)'
              : 'none'
            : 'none',
        }}
      />
      {primaryText && <div className={primaryTextClasses}>{primaryText}</div>}
      {primaryText && (secondaryText || url) && <div className={dashClasses}>&ndash;</div>}
      {url ? <div className={urlClasses}>{url}</div> : <div className={secondaryTextClasses}>{secondaryText}</div>}
    </div>
  );
});
