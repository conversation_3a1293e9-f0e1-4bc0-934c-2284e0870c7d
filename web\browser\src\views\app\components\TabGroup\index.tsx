import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { ITabGroup } from '../../models';
import { eventUtils } from '@browser/core/utils/platform-lite';
import store from '../../store';
import { cn } from '@browser/utils/tailwind-helpers';

const onPlaceholderClick = (tabGroup: ITabGroup) => () => {
  const { left, bottom } = tabGroup.ref.current.getBoundingClientRect();
  eventUtils.send(`show-tabgroup-dialog-${store.windowId}`, {
    name: tabGroup.name,
    id: tabGroup.id,
    x: left,
    y: bottom,
  });
};

export const TabGroup = observer(({ tabGroup }: { tabGroup: ITabGroup }) => {
  const hasName = tabGroup.name !== '';

  // StyledTabGroup 样式 - Tailwind 版本
  const tabGroupClasses = cn(
    'absolute left-0 flex items-center h-full'
  );

  // Placeholder 样式 - Tailwind 版本
  const placeholderClasses = cn(
    'overflow-hidden relative transition-all duration-200 text-black text-[11px]',
    '[&]:[-webkit-app-region:no-drag]',
    // 根据是否有名称调整样式
    hasName ? 'p-1 rounded-md' : 'p-[7px] rounded-full'
  );

  const placeholderStyle = {
    backgroundColor: tabGroup.color,
  };

  // Line 样式 - Tailwind 版本
  const lineClasses = cn(
    'h-0.5 absolute bottom-0 rounded'
  );

  const lineStyle = {
    backgroundColor: tabGroup.color,
  };

  return (
    <>
      <div className={tabGroupClasses} ref={tabGroup.ref}>
        <div
          className={placeholderClasses}
          style={placeholderStyle}
          onMouseDown={onPlaceholderClick(tabGroup)}
          ref={tabGroup.placeholderRef}
        >
          {tabGroup.name}
          {/* after 伪元素的替代 - hover 效果 */}
          <div className="absolute left-0 top-0 right-0 bottom-0 bg-white opacity-0 transition-opacity duration-100 hover:opacity-20 pointer-events-none" />
        </div>
        <div
          className={lineClasses}
          style={lineStyle}
          ref={tabGroup.lineRef}
        />
      </div>
    </>
  );
});
