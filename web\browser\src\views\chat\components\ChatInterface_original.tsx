import type React from "react"
import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react"
import {
  Send,
  User,
  Loader2,
  ChevronDown,
  ChevronUp,
  Brain,
  ToggleLeft,
  ToggleRight,
} from "lucide-react"
import { motion } from "framer-motion"

import { But<PERSON> } from '@shared/components/button'
import { ScrollArea } from "@shared/components/scroll-area"
import { Textarea } from "@shared/components/textarea"
import { SuggestedQuestions } from "@shared/components/suggested-questions"
import { ModelSelector } from "@shared/components/model-selector"
import MarkdownRenderer from "@shared/components/MarkdownRenderer"
import TypewriterText from "@shared/components/TypewriterText"
import ChatFileUpload from "./ChatFileUpload"
import { cn } from '@shared/lib/utils'
import { useChatHistory } from '@shared/contexts/ChatHistoryContext'
import { getResponse, getStreamingResponse, AVAILABLE_MODELS } from "@shared/lib/aliyun.ts"
import { getLogoUrl } from '@shared/utils/resource-url'
import { mem0Service } from '@shared/services/mem0-service'
import { debugLogger, logMemoryOperation, logLLMRequest, logContextBuilding, logStateChange } from '@shared/utils/debug-logger'


// Use Message type from context
import type { Message } from '@shared/contexts/ChatHistoryContext'

interface ChatInterfaceProps {
  apiKey?: string
  endpoint?: string
  onNewChat?: () => void
  conversationRounds?: number
}

type ChatSession = {
  id: string
  title: string
  messages: Message[]
  timestamp: Date
}

const ChatInterface = forwardRef<{ createNewChat: () => void }, ChatInterfaceProps>(({ apiKey: propApiKey, endpoint: propEndpoint, onNewChat, conversationRounds = 6 }, ref) => {
  const { currentMessages, addMessage, updateMessage, createNewChat: createNewChatContext } = useChatHistory()
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [selectedModel, setSelectedModel] = useState("qwen-plus")
  const [collapsedThinking, setCollapsedThinking] = useState<Set<number>>(new Set())
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [mem0Available, setMem0Available] = useState(false)

  const [memoryModeEnabled, setMemoryModeEnabled] = useState(() => {
    // Load memory mode state from localStorage
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('memory_aware_mode');
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  })
  const [relevantMemories, setRelevantMemories] = useState<any[]>([])
  const [memorySearching, setMemorySearching] = useState(false)

  // Save memory mode state to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('memory_aware_mode', JSON.stringify(memoryModeEnabled));

      // Enhanced debugging for memory mode state changes
      logStateChange('memory_mode', {
        enabled: memoryModeEnabled,
        mem0Available,
        timestamp: new Date().toISOString(),
        localStorage: localStorage.getItem('memory_aware_mode')
      });

      console.log('🧠 Memory mode state saved to localStorage:', memoryModeEnabled);
      debugLogger.printUtf8Debug(); // Print UTF-8 debug info on state changes
    }
  }, [memoryModeEnabled]);

  // 使用传入的API key，如果没有传入则从localStorage读取
  const apiKey = propApiKey || (typeof window !== 'undefined' ? localStorage.getItem('aliyun_api_key') || '' : '')

  // Initialize mem0 service and check availability
  useEffect(() => {
    const initializeMem0 = async () => {
      try {
        await mem0Service.initialize();

        // Set API key if available
        if (apiKey) {
          await mem0Service.setApiKey(apiKey);
        }

        const availability = await mem0Service.isServiceAvailable();
        setMem0Available(availability.available);
        console.log('🧠 Mem0 service availability check:', {
          available: availability.available,
          success: availability.success,
          status: availability.status,
          error: availability.error,
          apiKey: apiKey ? `${apiKey.substring(0, 12)}...` : 'Not set'
        });
      } catch (error) {
        console.error('Failed to initialize mem0 service:', error);
        setMem0Available(false);
      }
    };

    initializeMem0();
  }, [apiKey]); // Re-run when API key changes

  // Build conversation context for API requests
  const buildConversationContext = (currentMessages: Message[]): string => {
    if (currentMessages.length === 0) return ''

    // Get the last N rounds of conversation (user + assistant pairs)
    const rounds = Math.min(conversationRounds, Math.floor(currentMessages.length / 2))
    const contextMessages = currentMessages.slice(-rounds * 2)

    let context = ''
    for (let i = 0; i < contextMessages.length; i += 2) {
      const userMsg = contextMessages[i]
      const assistantMsg = contextMessages[i + 1]

      if (userMsg && userMsg.role === 'user') {
        context += `用户: ${userMsg.content}\n`
      }
      if (assistantMsg && assistantMsg.role === 'assistant') {
        context += `助手: ${assistantMsg.content}\n`
      }
    }

    return context.trim()
  }

  // Expose createNewChat method to parent component
  useImperativeHandle(ref, () => ({
    createNewChat
  }))
  const endpoint = propEndpoint || (typeof window !== 'undefined' ? localStorage.getItem('aliyun_endpoint') || '' : '')

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [textareaFocused, setTextareaFocused] = useState(false)

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [currentMessages])

  // Focus management for textarea
  useEffect(() => {
    const handleFocusRecovery = () => {
      if (textareaRef.current && !textareaFocused) {
        console.log('🎯 Recovering textarea focus after modal interaction');
        setTimeout(() => {
          textareaRef.current?.focus();
        }, 100);
      }
    };

    // Listen for modal close events and recover focus
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleFocusRecovery();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [textareaFocused]);

  // Handle textarea focus/blur events
  const handleTextareaFocus = () => {
    console.log('🎯 Textarea focused');
    setTextareaFocused(true);
  };

  const handleTextareaBlur = () => {
    console.log('🎯 Textarea blurred');
    setTextareaFocused(false);
  };

  const handleSuggestedQuestion = (question: string) => {
    setInput(question)
    if (textareaRef.current) {
      textareaRef.current.focus()
    }
  }

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId)
  }

  const toggleThinking = (index: number) => {
    setCollapsedThinking(prev => {
      const newSet = new Set(prev)
      if (newSet.has(index)) {
        newSet.delete(index)
      } else {
        newSet.add(index)
      }
      return newSet
    })
  }

  // Typewriter functionality simplified for context integration
  const handleThinkingTypewriterComplete = (index: number) => {
    console.log('Thinking typewriter complete for message', index)
  }

  const handleContentTypewriterComplete = (index: number) => {
    console.log('Content typewriter complete for message', index)
  }

  const createNewChat = () => {
    // Use context to create new chat
    createNewChatContext()
    setInput('')
    setIsLoading(false)

    // Call the callback if provided
    onNewChat?.()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleFilesSelected = async (files: File[]) => {
    setUploadedFiles(prev => [...prev, ...files]);
    console.log('Files selected:', files.map(f => f.name));

    // Process files and store in memory if mem0 is available
    if (mem0Available && files.length > 0) {
      try {
        for (const file of files) {
          // Read file content
          const content = await readFileContent(file);
          if (content && content.trim()) {
            // Store file content in memory
            await mem0Service.addFileMemory(content, file.name, {
              fileType: file.type || 'unknown',
              fileSize: file.size,
              lastModified: new Date(file.lastModified).toISOString()
            });
            console.log(`File content stored in memory: ${file.name}`);
          }
        }
      } catch (error) {
        console.error('Error processing uploaded files:', error);
      }
    }
  };

  // Helper function to read file content
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        resolve(content || '');
      };
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      reader.readAsText(file);
    });
  };

  const handleSend = async () => {
    if (!input.trim() && uploadedFiles.length === 0) return

    // 检查API Key
    if (!apiKey) {
      alert('请先设置阿里云API Key')
      return
    }

    // Prepare message content with files
    let messageContent = input;
    if (uploadedFiles.length > 0) {
      const fileList = uploadedFiles.map(f => `📎 ${f.name}`).join('\n');
      messageContent = `${input}\n\n附件:\n${fileList}`;
    }

    // Add user message
    const userMessage = {
      id: `msg-${Date.now()}-user`,
      role: "user" as const,
      content: messageContent,
      timestamp: new Date(),
    }
    addMessage(userMessage)
    setInput("")
    setUploadedFiles([])

    // 调用阿里云模型
    setIsLoading(true)

    // Create assistant message placeholder for streaming
    const assistantMessageId = `msg-${Date.now()}-assistant`;
    const assistantMessage = {
      id: assistantMessageId,
      role: "assistant" as const,
      content: "",
      timestamp: new Date(),
      thinking: "正在思考中...",
      isStreaming: true,
    }
    addMessage(assistantMessage)

    // Track accumulated content for streaming
    let accumulatedContent = "";

    try {
      // Enhanced debugging for message processing start
      logLLMRequest({
        phase: 'message_processing_start',
        originalInput: input,
        memoryModeEnabled,
        mem0Available,
        inputLength: input.length,
        conversationRounds,
        selectedModel,
        messageCount: currentMessages.length
      }, input);

      // Get enhanced prompt with memory context if memory mode is enabled
      let enhancedUserInput = input;
      let memoryContext = '';

      // Critical debugging: Check memory mode state before processing
      // NOTE: Memory toggle ONLY affects retrieval for LLM context, NOT storage
      logMemoryOperation('memory_mode_check', {
        memoryModeEnabled,
        mem0Available,
        shouldUseMemory: mem0Available && memoryModeEnabled,
        willSearchMemories: mem0Available && memoryModeEnabled,
        note: 'Toggle controls retrieval only - storage is always enabled'
      });

      // Memory retrieval for LLM context (controlled by toggle)
      if (mem0Available && memoryModeEnabled) {
        try {
          setMemorySearching(true);
          updateMessage(assistantMessageId, {
            thinking: "🧠 Searching relevant memories...",
          });

          // Enhanced memory search debugging
          logMemoryOperation('memory_search_start', {
            query: input,
            memoryModeEnabled,
            mem0Available,
            timestamp: new Date().toISOString(),
            queryLength: input.length
          }, input);

          console.log('🔍 Starting memory search process:', {
            query: input,
            memoryModeEnabled,
            mem0Available,
            timestamp: new Date().toISOString()
          });

          // Search for relevant memories using the new MemoryService with timeout
          const { getMemoryService } = await import('@shared/services/MemoryService');
          const memoryService = getMemoryService();

          const serviceStatus = {
            serviceExists: !!memoryService,
            isReady: memoryService?.isReady(),
            serviceType: typeof memoryService
          };

          logMemoryOperation('memory_service_status', serviceStatus);
          console.log('📡 Memory service status:', serviceStatus);

          if (memoryService && memoryService.isReady()) {
            // Add timeout for memory search (5 seconds)
            const searchPromise = memoryService.searchMemories({ query: input, limit: 5 });
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Memory search timeout')), 5000)
            );

            const searchResult = await Promise.race([searchPromise, timeoutPromise]);

            // Enhanced memory search result debugging
            const searchResultDebug = {
              success: searchResult.success,
              dataExists: !!searchResult.data,
              memoriesCount: searchResult.data?.length || 0,
              rawResult: searchResult,
              memoryDetails: searchResult.data?.map((memory: any, index: number) => ({
                index,
                id: memory.id,
                content: memory.memory || memory.content,
                contentLength: (memory.memory || memory.content)?.length || 0,
                score: memory.score,
                metadata: memory.metadata
              })) || []
            };

            logMemoryOperation('memory_search_result', searchResultDebug,
              searchResult.data?.map((m: any) => m.memory || m.content).join('\n---\n') || ''
            );

            console.log('📊 Memory search result:', searchResultDebug);

            if (searchResult.success && searchResult.data && searchResult.data.length > 0) {
              setRelevantMemories(searchResult.data);

              console.log('🧠 Raw memories found:', searchResult.data.map((memory, index) => ({
                index: index + 1,
                id: memory.id,
                memoryField: memory.memory,
                contentField: memory.content,
                textField: memory.text,
                metadata: memory.metadata
              })));

              // Create memory context with limited length (1000 chars max)
              const memoryTexts = searchResult.data
                .slice(0, 5) // Use top 5 most relevant memories
                .map((memory, index) => {
                  // Fix: Use memory.memory field which is the actual content field in the API response
                  const content = memory.memory || memory.content || memory.text || '';
                  console.log(`📝 Processing memory ${index + 1}:`, {
                    id: memory.id,
                    extractedContent: content,
                    contentLength: content.length,
                    source: memory.metadata?.source
                  });
                  return content.length > 200 ? content.substring(0, 200) + '...' : content;
                })
                .filter(text => text.trim().length > 0);

              console.log('✂️ Processed memory texts:', {
                totalTexts: memoryTexts.length,
                texts: memoryTexts
              });

              if (memoryTexts.length > 0) {
                memoryContext = memoryTexts
                  .map((text, index) => `- Memory ${index + 1}: ${text}`)
                  .join('\n');

                // Limit total context to 1000 characters
                if (memoryContext.length > 1000) {
                  memoryContext = memoryContext.substring(0, 1000) + '...';
                }

                enhancedUserInput = `[MEMORY CONTEXT]\nRelevant memories:\n${memoryContext}\n\n[CURRENT QUESTION]\n${input}`;

                // Enhanced context building debugging
                const contextDetails = {
                  originalQuery: input,
                  memoriesFound: searchResult.data.length,
                  memoryContextLength: memoryContext.length,
                  memoryContext: memoryContext,
                  enhancedPromptLength: enhancedUserInput.length,
                  enhancedPrompt: enhancedUserInput,
                  tokenEstimate: Math.ceil(enhancedUserInput.length / 4), // Rough token estimate
                  memoryToInputRatio: memoryContext.length / input.length
                };

                logContextBuilding(contextDetails, enhancedUserInput);

                console.log('✅ Enhanced prompt with memory context, found', searchResult.data.length, 'relevant memories');
                console.log('🧠 Memory Context Details:', contextDetails);

                console.log('📤 FINAL ENHANCED PROMPT TO BE SENT TO LLM:');
                console.log('┌' + '─'.repeat(80) + '┐');
                console.log('│' + ' '.repeat(25) + 'ENHANCED USER INPUT' + ' '.repeat(34) + '│');
                console.log('├' + '─'.repeat(80) + '┤');
                enhancedUserInput.split('\n').forEach(line => {
                  const paddedLine = line.padEnd(78);
                  console.log(`│ ${paddedLine.substring(0, 78)} │`);
                });
                console.log('└' + '─'.repeat(80) + '┘');
              }
            } else {
              console.log('ℹ️ No relevant memories found for query');
              setRelevantMemories([]);
            }
          }
        } catch (error) {
          console.error('❌ Failed to search memories:', error);
          setRelevantMemories([]);
          // Fallback to standard mode for this query
          updateMessage(assistantMessageId, {
            thinking: "Memory unavailable, using standard mode...",
          });
        } finally {
          setMemorySearching(false);
        }
      } else {
        // Memory retrieval disabled (but storage will still happen)
        logMemoryOperation('memory_retrieval_disabled', {
          memoryModeEnabled,
          mem0Available,
          reason: !mem0Available ? 'service_unavailable' : 'toggle_disabled',
          willUseStandardMode: true,
          storageStillEnabled: true,
          note: 'No memory context for LLM, but conversation will still be stored'
        });

        console.log('🚫 Memory retrieval disabled (storage still enabled):', {
          memoryModeEnabled,
          mem0Available,
          reason: !mem0Available ? 'Service unavailable' : 'Toggle disabled by user',
          impact: 'No memory context in LLM request, but Q&A will still be stored'
        });

        setRelevantMemories([]);
      }

      // 构建消息历史，使用配置的对话轮数
      const allMessages = [...currentMessages, userMessage]

      // 如果消息数量超过配置的轮数，只使用最近的N轮对话
      let contextMessages = allMessages
      if (allMessages.length > conversationRounds * 2) {
        // 保留最近的N轮对话（每轮包含用户消息和助手回复）
        contextMessages = allMessages.slice(-conversationRounds * 2)
      }

      // Create chat messages and replace the last user message with enhanced version
      const chatMessages = contextMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Replace the last user message with enhanced version if mem0 is available
      const willUseMemoryContext = mem0Available && memoryModeEnabled && chatMessages.length > 0 && chatMessages[chatMessages.length - 1].role === 'user';

      if (willUseMemoryContext) {
        chatMessages[chatMessages.length - 1].content = enhancedUserInput;

        const memoryContextData = {
          mode: 'memory_enhanced',
          memoryModeEnabled,
          mem0Available,
          totalMessages: chatMessages.length,
          lastUserMessage: chatMessages[chatMessages.length - 1].content,
          lastUserMessageLength: chatMessages[chatMessages.length - 1].content.length,
          memoryContextIncluded: memoryContext.length > 0,
          memoryContextLength: memoryContext.length,
          fullChatContext: chatMessages,
          tokenEstimate: chatMessages.reduce((total, msg) => total + Math.ceil(msg.content.length / 4), 0)
        };

        logLLMRequest(memoryContextData, chatMessages[chatMessages.length - 1].content);

        console.log('🚀 Final context sent to LLM (with memory):', memoryContextData);
      } else {
        const standardContextData = {
          mode: 'standard',
          memoryModeEnabled,
          mem0Available,
          totalMessages: chatMessages.length,
          lastUserMessage: chatMessages[chatMessages.length - 1]?.content,
          lastUserMessageLength: chatMessages[chatMessages.length - 1]?.content?.length || 0,
          memoryContextIncluded: false,
          fullChatContext: chatMessages,
          tokenEstimate: chatMessages.reduce((total, msg) => total + Math.ceil(msg.content.length / 4), 0),
          reason: !mem0Available ? 'service_unavailable' : !memoryModeEnabled ? 'mode_disabled' : 'no_user_message'
        };

        logLLMRequest(standardContextData, chatMessages[chatMessages.length - 1]?.content || '');

        console.log('🚀 Final context sent to LLM (standard mode):', standardContextData);
      }

      // Use streaming response
      await getStreamingResponse(
        chatMessages,
        apiKey,
        selectedModel,
        {
          onChunk: (chunk: string) => {
            // Accumulate content and update the assistant message
            accumulatedContent += chunk;
            updateMessage(assistantMessageId, {
              content: accumulatedContent,
              thinking: "正在生成回复...",
            })
          },
          onComplete: async (fullResponse: string) => {
            const finalResponse = fullResponse || '抱歉，我无法生成回复。';

            // Final update with complete response
            updateMessage(assistantMessageId, {
              content: finalResponse,
              thinking: "让我分析一下这个问题。首先，我需要理解用户的具体需求和上下文。然后，我会基于我的知识库提供最相关和有用的回答。我会确保回答准确、清晰，并且对用户有实际帮助。",
              isStreaming: false,
            });

            // Store Q&A in memory for ALL conversations (independent of memory toggle)
            if (mem0Available && finalResponse !== '抱歉，我无法生成回复。') {
              try {
                const qaStorageData = {
                  questionLength: input.length,
                  answerLength: finalResponse.length,
                  model: selectedModel,
                  memoryModeEnabled, // For debugging only - storage is independent of toggle
                  storageReason: 'automatic_conversation_archiving',
                  timestamp: new Date().toISOString()
                };

                logMemoryOperation('qa_storage_attempt', qaStorageData, `Q: ${input}\nA: ${finalResponse}`);

                console.log('💾 Storing Q&A pair in memory (automatic archiving)...', qaStorageData);

                const result = await mem0Service.addQAMemory(input, finalResponse, {
                  model: selectedModel,
                  timestamp: new Date().toISOString()
                });

                if (result.success) {
                  logMemoryOperation('qa_storage_success', { id: result.id, ...qaStorageData });
                  console.log('✅ Q&A pair stored in memory successfully:', result.id);
                } else {
                  logMemoryOperation('qa_storage_failure', { error: result.error, ...qaStorageData });
                  console.error('❌ Failed to store Q&A pair:', result.error);
                }
              } catch (error) {
                const errorData = {
                  message: error instanceof Error ? error.message : 'Unknown error',
                  stack: error instanceof Error ? error.stack : undefined,
                  memoryModeEnabled
                };

                logMemoryOperation('qa_storage_error', errorData);
                console.error('❌ Failed to store Q&A in memory:', error);
                console.error('Error details:', errorData);
              }
            } else {
              // Log why Q&A storage was skipped (only service availability or invalid response)
              const skipReason = {
                mem0Available,
                memoryModeEnabled, // For debugging - toggle doesn't affect storage
                responseValid: finalResponse !== '抱歉，我无法生成回复。',
                reason: !mem0Available ? 'service_unavailable' : 'invalid_response',
                note: 'Q&A storage is independent of memory toggle state'
              };

              logMemoryOperation('qa_storage_skipped', skipReason);
              console.log('⏭️ Skipping Q&A memory storage:', skipReason);
            }
          },
          onError: (error: Error) => {
            console.error('流式响应错误:', error)
            updateMessage(assistantMessageId, {
              content: `抱歉，调用阿里云模型时出现错误：${error.message}`,
              thinking: undefined,
              isStreaming: false,
            })
          }
        },
        endpoint
      )
    } catch (error) {
      console.error('阿里云API调用失败:', error)
      updateMessage(assistantMessageId, {
        content: `抱歉，调用阿里云模型时出现错误：${error instanceof Error ? error.message : '未知错误'}`,
        thinking: undefined,
        isStreaming: false,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex h-full flex-col">
      {currentMessages.length === 0 ? (
        <div className="flex-1 flex flex-col items-center justify-center px-6">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="mb-6 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 relative"
          >
            <img
              src={getLogoUrl()}
              alt="AI Assistant"
              className="ai-chat-logo ai-logo-pulse"
              onError={(e) => {
                // Fallback if logo doesn't load
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          </motion.div>
          
          <motion.h2
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-3 text-2xl font-semibold text-foreground text-center"
          >
            今天我能帮您什么？
          </motion.h2>
          
          <motion.p
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-8 text-center text-foreground/60 max-w-md"
          >
            向我提问任何问题，我将为您提供详细的回答。
          </motion.p>
          
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-8"
          >
            <p className="text-sm text-muted-foreground mb-4 text-center">推荐问题</p>
            <SuggestedQuestions onSelect={handleSuggestedQuestion} />
          </motion.div>
        </div>
      ) : (
        <ScrollArea className="flex-1 px-4">
          <div className="space-y-6 py-6">
            {currentMessages.map((message, index) => (
              <motion.div
                key={index}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.3 }}
                className={cn(
                  "flex items-start gap-3",
                  message.role === "assistant" ? "justify-start" : "justify-end",
                )}
              >
                {message.role === "assistant" && (
                  <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-primary/10 text-primary shadow-sm relative">
                    <img
                      src={getLogoUrl()}
                      alt="AI Assistant"
                      className="ai-chat-logo"
                      style={{ width: '20px', height: '20px' }}
                      onError={(e) => {
                        // Fallback if logo doesn't load
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                )}
                <div className="flex flex-col max-w-[85%]">
                  {message.thinking && message.role === "assistant" && (
                    <div className="mb-2">
                      <div
                        className="flex items-center justify-between cursor-pointer px-3 py-1 rounded-t-md bg-muted/20 hover:bg-muted/30 transition-colors"
                        onClick={() => toggleThinking(index)}
                      >
                        <span className="text-xs font-medium text-muted-foreground">思考过程</span>
                        <motion.div
                          animate={{ rotate: collapsedThinking.has(index) ? 0 : 180 }}
                          transition={{ duration: 0.2 }}
                        >
                          {collapsedThinking.has(index) ? (
                            <ChevronDown className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <ChevronUp className="h-4 w-4 text-muted-foreground" />
                          )}
                        </motion.div>
                      </div>
                      {!collapsedThinking.has(index) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="px-3 py-2 bg-muted/10 rounded-b-md border-l-2 border-primary/30"
                        >
                          <TypewriterText
                            text={message.thinking || ''}
                            speed={25}
                            enabled={false}
                            className="text-xs italic text-muted-foreground leading-relaxed"
                            onComplete={() => handleThinkingTypewriterComplete(index)}
                          />
                        </motion.div>
                      )}
                    </div>
                  )}
                  <motion.div
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                    className={cn(
                      message.role === "assistant" ? "kakao-bubble-assistant" : "kakao-bubble-user",
                    )}
                  >
                    {message.role === "assistant" ? (
                      <TypewriterText
                        text={message.content}
                        speed={30}
                        enabled={false}
                        className="text-sm leading-relaxed"
                        isMarkdown={true}
                        onComplete={() => handleContentTypewriterComplete(index)}
                      />
                    ) : (
                      <MarkdownRenderer content={message.content} className="text-sm leading-relaxed" />
                    )}
                    <div className="mt-1 text-right">
                      <span className={cn(
                        "text-xs",
                        message.role === "assistant"
                          ? "text-bubble-assistant-text/60"
                          : "text-bubble-user-text/80"
                      )}>
                        {new Date(message.timestamp).toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </span>
                    </div>
                  </motion.div>
                </div>
                {message.role === "user" && (
                  <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-primary text-primary-foreground">
                    <User className="h-5 w-5" />
                  </div>
                )}
              </motion.div>
            ))}
            {isLoading && (
              <div className="flex items-start gap-3 animate-fade-in">
                <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-primary/10 text-primary shadow-sm relative">
                  <img
                    src={getLogoUrl()}
                    alt="AI Assistant"
                    className="ai-chat-logo ai-logo-pulse"
                    onError={(e) => {
                      // Fallback if logo doesn't load
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>
                <div className="kakao-bubble-assistant flex max-w-[85%] items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-foreground/60" />
                  <p className="text-sm text-foreground/70">思考中...</p>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      )}

      <div className="mt-auto">
        <div className="flex items-center justify-between mb-4 flex-wrap gap-2">
          <div className="flex items-center gap-2">
            <ModelSelector onModelChange={handleModelChange} />
          </div>
          <div className="flex items-center gap-4">
            {apiKey ? (
              <div className="text-sm text-green-600 dark:text-green-400 flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                API已配置
              </div>
            ) : (
              <div className="text-sm text-red-600 dark:text-red-400 flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-red-500"></div>
                请在设置中配置API Key
              </div>
            )}

            {currentMessages.length > 0 && (
              <div className="text-xs text-muted-foreground flex items-center gap-1">
                <div className="h-1.5 w-1.5 rounded-full bg-primary/60"></div>
                记忆 {Math.min(Math.floor(currentMessages.length / 2), conversationRounds)} / {conversationRounds} 轮对话
              </div>
            )}

            {/* Memory Mode Status and Relevant Memories */}
            {mem0Available && memoryModeEnabled && (
              <motion.div
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-xs text-muted-foreground space-y-2"
              >
                <div className="flex items-center gap-2">
                  <span className="text-sm">🧠</span>
                  <div className="flex items-center gap-1">
                    <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
                    <span>Using memory context</span>
                    {memorySearching && (
                      <span className="animate-pulse text-blue-600 dark:text-blue-400">
                        - searching...
                      </span>
                    )}
                  </div>
                </div>

                {relevantMemories.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 rounded-lg p-3 space-y-2 border border-blue-200 dark:border-blue-800"
                  >
                    <div className="font-medium text-foreground flex items-center gap-1">
                      <span>📋</span>
                      <span>Relevant memories ({relevantMemories.length})</span>
                    </div>
                    {relevantMemories.slice(0, 2).map((memory, index) => (
                      <div key={memory.id || index} className="text-xs bg-background/80 rounded p-2 border-l-2 border-blue-400">
                        <div className="line-clamp-2 text-foreground">
                          {memory.content || memory.text || 'No content'}
                        </div>
                        {(memory.metadata?.source || memory.source) && (
                          <div className="text-muted-foreground mt-1 flex items-center gap-1">
                            <span className="text-xs">📌</span>
                            <span>Source: {memory.metadata?.source || memory.source}</span>
                          </div>
                        )}
                      </div>
                    ))}
                    {relevantMemories.length > 2 && (
                      <div className="text-center text-muted-foreground">
                        + {relevantMemories.length - 2} more memories used for context
                      </div>
                    )}
                  </motion.div>
                )}
              </motion.div>
            )}
          </div>
        </div>

        <div className="chat-input-container relative flex rounded-lg border bg-card p-1 shadow-sm transition-all duration-200">
          {/* File Upload Buttons - Left Side */}
          <div className="flex items-end p-2">
            <ChatFileUpload
              onFilesSelected={handleFilesSelected}
              className="flex-shrink-0"
            />
          </div>

          <Textarea
            ref={textareaRef}
            placeholder="请输入您的问题..."
            className="dynamic-textarea flex-1 border-0 bg-transparent p-3 shadow-none focus-visible:ring-0 text-foreground placeholder:text-muted-foreground resize-none transition-all duration-200"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={handleTextareaFocus}
            onBlur={handleTextareaBlur}
            disabled={isLoading}
            style={{ pointerEvents: 'auto' }}
          />

          {/* Action Buttons - Right Side */}
          <div className="flex items-end gap-2 p-2">
            {/* Memory Mode Toggle */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                size="icon"
                variant={memoryModeEnabled ? "default" : "outline"}
                className={cn(
                  "h-10 w-10 rounded-full transition-all duration-200 relative",
                  memoryModeEnabled && "bg-primary text-primary-foreground shadow-md",
                  memorySearching && "animate-pulse",
                  !memoryModeEnabled && "hover:bg-muted",
                  !mem0Available && "opacity-60"
                )}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();

                  const toggleData = {
                    currentState: memoryModeEnabled,
                    serviceAvailable: mem0Available,
                    timestamp: new Date().toISOString()
                  };

                  logStateChange('memory_toggle_clicked', toggleData);
                  console.log('Memory toggle clicked. Current state:', memoryModeEnabled, 'Service available:', mem0Available);

                  if (mem0Available || true) { // Allow toggle even if service not available for testing
                    const newState = !memoryModeEnabled;
                    setMemoryModeEnabled(newState);

                    logStateChange('memory_toggle_changed', {
                      ...toggleData,
                      newState,
                      stateChanged: true
                    });

                    console.log('Memory mode toggled to:', newState);
                  } else {
                    logStateChange('memory_toggle_blocked', {
                      ...toggleData,
                      reason: 'service_unavailable'
                    });

                    console.log('Mem0 service not available. Current status:', mem0Available);
                  }
                }}
                title={
                  mem0Available
                    ? (memoryModeEnabled ? "Memory-aware responses (enabled)" : "Memory-aware responses (disabled)")
                    : "Memory service not available - click to test toggle"
                }
                disabled={memorySearching}
                type="button"
              >
                {/* Main Icon - Brain for memory */}
                <Brain
                  className={cn(
                    "h-5 w-5 transition-all duration-200",
                    memoryModeEnabled ? "text-primary-foreground" : "text-muted-foreground",
                    memorySearching && "animate-pulse"
                  )}
                />

                {/* Status Indicators */}
                {memoryModeEnabled && mem0Available && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-background"
                  />
                )}

                {memorySearching && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-blue-500 rounded-full border border-background animate-pulse"
                  />
                )}

                {!mem0Available && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-orange-500 rounded-full border border-background"
                  />
                )}

                <span className="sr-only">
                  {mem0Available
                    ? (memoryModeEnabled ? "Disable memory-aware responses" : "Enable memory-aware responses")
                    : "Memory service unavailable"
                  }
                </span>
              </Button>
            </motion.div>



            {/* Send Button */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                size="icon"
                className="h-10 w-10 rounded-full"
                disabled={isLoading || (!input.trim() && uploadedFiles.length === 0)}
                onClick={handleSend}
              >
                <Send className="h-5 w-5" />
                <span className="sr-only">发送</span>
              </Button>
            </motion.div>
          </div>
        </div>
      </div>


    </div>
  )
})

ChatInterface.displayName = 'ChatInterface'

export default ChatInterface
