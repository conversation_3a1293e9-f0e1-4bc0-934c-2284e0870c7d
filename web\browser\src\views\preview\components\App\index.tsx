import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';
import { UIStyle } from '@browser/core/styles/default-styles';

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本 (继承 PersistentDialogStyle)
  const appClasses = cn(
    'm-0 p-3 text-sm rounded-[10px] shadow-dialog bg-mario-dialog',
    'max-w-[240px]', // TAB_MAX_WIDTH
    // 文本颜色根据主题
    store.theme['dialog.textColor'] ? 'text-mario-text' : 'text-black'
  );

  const appStyle: React.CSSProperties = {
    transform: `translate3d(${store.x}px, 0, 0)`,
    transition: `0.15s opacity${store.xTransition ? ', 0.08s transform' : ''}`,
  };

  // Title 样式
  const titleClasses = cn(
    'font-medium leading-[1.3rem] opacity-87',
    // maxLines(2) 效果
    'line-clamp-2'
  );

  // Domain 样式
  const domainClasses = cn(
    'opacity-70 leading-[1.3rem]'
  );

  return (
    <>
      <UIStyle />
      <div
        className={appClasses}
        style={appStyle}
      >
        <div className={titleClasses}>{store.title}</div>
        <div className={domainClasses}>{store.domain}</div>
      </div>
    </>
  );
});
