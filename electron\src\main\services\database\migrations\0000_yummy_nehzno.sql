-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TABLE `bookmarks` (
	`id` text PRIMARY KEY,
	`title` text NOT NULL,
	`url` text,
	`favicon` text,
	`is_folder` numeric DEFAULT (FALSE),
	`parent_id` text,
	`order_index` integer DEFAULT 0,
	`static_type` text,
	`expanded` numeric DEFAULT (FALSE),
	`ai_tags` text,
	`ai_summary` text,
	`ai_category` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL,
	FOREIGN KEY (`parent_id`) REFERENCES `bookmarks`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `history` (
	`id` text PRIMARY KEY,
	`url` text NOT NULL,
	`title` text,
	`visit_time` integer NOT NULL,
	`favicon` text,
	`visit_count` integer DEFAULT 1,
	`ai_insights` text,
	`ai_relevance_score` real,
	`created_at` integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE `favicons` (
	`url` text PRIMARY KEY,
	`data` text NOT NULL,
	`mime_type` text DEFAULT 'image/png',
	`created_at` integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE `form_fill_data` (
	`id` text PRIMARY KEY,
	`type` text NOT NULL,
	`url` text NOT NULL,
	`favicon` text,
	`fields` text NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE `startup_tabs` (
	`id` text PRIMARY KEY,
	`title` text,
	`url` text NOT NULL,
	`favicon` text,
	`is_user_defined` numeric DEFAULT (FALSE),
	`order_index` integer DEFAULT 0,
	`created_at` integer NOT NULL,
	`pinned` integer DEFAULT 0,
	`window_id` integer DEFAULT 1
);
--> statement-breakpoint
CREATE TABLE `permissions` (
	`id` text PRIMARY KEY,
	`origin` text NOT NULL,
	`permission_type` text NOT NULL,
	`granted` numeric DEFAULT (FALSE),
	`created_at` integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE `settings` (
	`key` text PRIMARY KEY,
	`value` text NOT NULL,
	`type` text NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_bookmarks_static` ON `bookmarks` (`static_type`);--> statement-breakpoint
CREATE INDEX `idx_bookmarks_parent` ON `bookmarks` (`parent_id`);--> statement-breakpoint
CREATE INDEX `idx_history_visit_time` ON `history` (`visit_time`);--> statement-breakpoint
CREATE INDEX `idx_history_url` ON `history` (`url`);--> statement-breakpoint
CREATE INDEX `idx_permissions_origin` ON `permissions` (`origin`);
*/