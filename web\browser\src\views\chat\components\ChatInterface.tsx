import type React from "react"
import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react"

// 使用当前项目的组件
import { Button } from '../../../shared/components/Button'
import { ScrollArea } from "../../../shared/components/ScrollArea"
import { Textarea } from "../../../shared/components/Textarea"
import ChatFileUpload from "./ChatFileUpload"
import ChatHistorySidebar from "./ChatHistorySidebar"
import ModelSelector from "./ModelSelector"
import { cn } from '../../../shared/utils/cn'
import { useChatHistory } from '../hooks/useChatHistory'
import { getResponse, getStreamingResponse, AVAILABLE_MODELS } from "../services/aliyun"

// Use Message type from context
import type { Message } from '../hooks/useChatHistory'

interface ChatInterfaceProps {
  apiKey?: string
  endpoint?: string
  onNewChat?: () => void
  conversationRounds?: number
}

type ChatSession = {
  id: string
  title: string
  messages: Message[]
  timestamp: Date
}

const ChatInterface = forwardRef<{ createNewChat: () => void }, ChatInterfaceProps>(({ apiKey: propApiKey, endpoint: propEndpoint, onNewChat, conversationRounds = 6 }, ref) => {
  const { currentMessages, addMessage, updateMessage, createNewChat: createNewChatContext } = useChatHistory()
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [selectedModel, setSelectedModel] = useState("qwen-plus")
  const [collapsedThinking, setCollapsedThinking] = useState<Set<number>>(new Set())
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  // 使用传入的API key，如果没有传入则从localStorage读取
  const apiKey = propApiKey || (typeof window !== 'undefined' ? localStorage.getItem('aliyun_api_key') || '' : '')

  // Build conversation context for API requests
  const buildConversationContext = (currentMessages: Message[]): string => {
    if (currentMessages.length === 0) return ''

    // Get the last N rounds of conversation (user + assistant pairs)
    const rounds = Math.min(conversationRounds, Math.floor(currentMessages.length / 2))
    const contextMessages = currentMessages.slice(-rounds * 2)

    let context = ''
    for (let i = 0; i < contextMessages.length; i += 2) {
      const userMsg = contextMessages[i]
      const assistantMsg = contextMessages[i + 1]

      if (userMsg && userMsg.role === 'user') {
        context += `用户: ${userMsg.content}\n`
      }
      if (assistantMsg && assistantMsg.role === 'assistant') {
        context += `助手: ${assistantMsg.content}\n`
      }
    }

    return context.trim()
  }

  // Expose createNewChat method to parent component
  useImperativeHandle(ref, () => ({
    createNewChat
  }))
  const endpoint = propEndpoint || (typeof window !== 'undefined' ? localStorage.getItem('aliyun_endpoint') || '' : '')

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [textareaFocused, setTextareaFocused] = useState(false)

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [currentMessages])

  // Focus management for textarea
  useEffect(() => {
    const handleFocusRecovery = () => {
      if (textareaRef.current && !textareaFocused) {
        console.log('🎯 Recovering textarea focus after modal interaction');
        setTimeout(() => {
          textareaRef.current?.focus();
        }, 100);
      }
    };

    // Listen for modal close events and recover focus
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleFocusRecovery();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [textareaFocused]);

  // Handle textarea focus/blur events
  const handleTextareaFocus = () => {
    console.log('🎯 Textarea focused');
    setTextareaFocused(true);
  };

  const handleTextareaBlur = () => {
    console.log('🎯 Textarea blurred');
    setTextareaFocused(false);
  };

  const createNewChat = () => {
    createNewChatContext()
    setInput("")
    setUploadedFiles([])
    onNewChat?.()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const toggleThinking = (messageIndex: number) => {
    setCollapsedThinking(prev => {
      const newSet = new Set(prev)
      if (newSet.has(messageIndex)) {
        newSet.delete(messageIndex)
      } else {
        newSet.add(messageIndex)
      }
      return newSet
    });
  };

  const handleSend = async () => {
    if (!input.trim() && uploadedFiles.length === 0) return

    // 检查API Key
    if (!apiKey) {
      alert('请先设置阿里云API Key')
      return
    }

    // Prepare message content with files
    let messageContent = input;
    if (uploadedFiles.length > 0) {
      const fileList = uploadedFiles.map(f => `📎 ${f.name}`).join('\n');
      messageContent = `${input}\n\n附件:\n${fileList}`;
    }

    // Add user message
    const userMessage = {
      id: `msg-${Date.now()}-user`,
      role: "user" as const,
      content: messageContent,
      timestamp: new Date(),
    }
    addMessage(userMessage)
    setInput("")
    setUploadedFiles([])

    // 调用阿里云模型
    setIsLoading(true)

    // Create assistant message placeholder for streaming
    const assistantMessageId = `msg-${Date.now()}-assistant`;
    const assistantMessage = {
      id: assistantMessageId,
      role: "assistant" as const,
      content: "",
      timestamp: new Date(),
      thinking: "正在思考中...",
      isStreaming: true,
    }
    addMessage(assistantMessage)

    // Track accumulated content for streaming
    let accumulatedContent = "";

    try {
      // TODO: Implement the rest of the handleSend logic
      // This is a placeholder for now
      console.log('Sending message:', messageContent);
      
      // Simulate response for now
      setTimeout(() => {
        updateMessage(assistantMessageId, {
          content: "这是一个测试回复。Chat功能正在迁移中...",
          thinking: undefined,
          isStreaming: false,
        });
        setIsLoading(false);
      }, 1000);

    } catch (error) {
      console.error('Error sending message:', error);
      updateMessage(assistantMessageId, {
        content: "抱歉，发生了错误。请稍后重试。",
        thinking: undefined,
        isStreaming: false,
      });
      setIsLoading(false);
    }
  }

  return (
    <div className="flex h-screen bg-mario-page">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar with Model Selector */}
        <div className="border-b border-mario-border p-4 bg-mario-page">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <ModelSelector onModelChange={setSelectedModel} selectedModel={selectedModel} />
              {apiKey ? (
                <div className="text-sm text-green-600 dark:text-green-400 flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                  API已配置
                </div>
              ) : (
                <div className="text-sm text-red-600 dark:text-red-400 flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-red-500"></div>
                  请在设置中配置API Key
                </div>
              )}
            </div>

            {currentMessages.length > 0 && (
              <div className="text-xs text-mario-page-text opacity-60 flex items-center gap-1">
                <div className="h-1.5 w-1.5 rounded-full bg-blue-600"></div>
                记忆 {Math.min(Math.floor(currentMessages.length / 2), conversationRounds)} / {conversationRounds} 轮对话
              </div>
            )}
          </div>
        </div>

        {/* Chat Messages Area */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4 max-w-4xl mx-auto">
            {currentMessages.length === 0 ? (
              <div className="text-center py-8">
                <div className="mb-6 flex h-12 w-12 items-center justify-center rounded-full bg-blue-600/10 mx-auto">
                  <span className="text-2xl">🤖</span>
                </div>
                <h2 className="text-2xl font-semibold mb-4 text-mario-page-text">今天我能帮您什么？</h2>
                <p className="text-mario-page-text opacity-70 max-w-md mx-auto">向我提问任何问题，我将为您提供详细的回答。</p>
              </div>
            ) : (
              currentMessages.map((message, index) => (
                <div key={message.id} className="space-y-2">
                  <div className={cn(
                    "flex gap-3",
                    message.role === "user" ? "justify-end" : "justify-start"
                  )}>
                    {message.role === "assistant" && (
                      <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                        <span className="text-white">🤖</span>
                      </div>
                    )}

                    <div className={cn(
                      "max-w-[80%] rounded-lg p-3",
                      message.role === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-mario-dialog text-mario-page-text border border-mario-border"
                    )}>
                      {message.role === "user" ? (
                        <p className="whitespace-pre-wrap">{message.content}</p>
                      ) : (
                        <div>
                          {message.thinking && (
                            <div className="mb-2 text-sm text-mario-page-text opacity-70">
                              {message.thinking}
                            </div>
                          )}
                          {message.content && (
                            <div className="prose prose-sm max-w-none">
                              <div className="whitespace-pre-wrap">{message.content}</div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {message.role === "user" && (
                      <div className="w-8 h-8 rounded-full bg-mario-dialog border border-mario-border flex items-center justify-center">
                        <span>👤</span>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
            {isLoading && (
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                  <span className="text-white">🤖</span>
                </div>
                <div className="bg-mario-dialog text-mario-page-text border border-mario-border rounded-lg p-3 flex items-center gap-2">
                  <div className="h-4 w-4 border-2 border-mario-page-text border-t-transparent rounded-full animate-spin opacity-60"></div>
                  <p className="text-sm opacity-70">思考中...</p>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="border-t border-mario-border bg-mario-page">
          <div className="p-4">
            {/* File Upload */}
            <div className="mb-4">
              <ChatFileUpload
                onFilesSelected={setUploadedFiles}
                className="max-w-md"
              />
            </div>

            {/* Input Box */}
            <div className="flex gap-2 items-end bg-mario-dialog border border-mario-border rounded-lg p-2">
              <Textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                onFocus={handleTextareaFocus}
                onBlur={handleTextareaBlur}
                placeholder="请输入您的问题..."
                className="flex-1 border-0 bg-transparent resize-none shadow-none focus-visible:ring-0 text-mario-page-text placeholder:text-mario-page-text placeholder:opacity-50 min-h-[60px] max-h-[200px]"
                disabled={isLoading}
              />

              {/* Action Buttons */}
              <div className="flex items-end gap-2">
                {/* Memory Mode Toggle */}
                <Button
                  size="icon"
                  variant="outline"
                  className="h-10 w-10 rounded-full"
                  title="记忆模式"
                >
                  <span className="text-sm">🧠</span>
                </Button>

                {/* Send Button */}
                <Button
                  size="icon"
                  className="h-10 w-10 rounded-full bg-blue-600 hover:bg-blue-700 text-white"
                  disabled={isLoading || (!input.trim() && uploadedFiles.length === 0)}
                  onClick={handleSend}
                >
                  {isLoading ? (
                    <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <span className="text-sm">📤</span>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Sidebar */}
      <ChatHistorySidebar
        onNewChat={createNewChat}
        onChatSelect={(chatId) => console.log('Select chat:', chatId)}
        className="w-80"
      />
    </div>
  )
})

ChatInterface.displayName = 'ChatInterface'

export default ChatInterface
