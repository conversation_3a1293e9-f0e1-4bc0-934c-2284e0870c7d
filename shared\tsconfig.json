{"compilerOptions": {"target": "es2020", "module": "esnext", "lib": ["es2020", "dom", "dom.iterable"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "moduleResolution": "node"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}